<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14">
  
<g transform="matrix(1,0,0,1,0,0)"><g>
    <rect x="0.5" y="8.25" width="13" height="5" rx="2.5" style="fill: none;stroke: #000000;stroke-linecap: round;stroke-linejoin: round"></rect>
    <circle cx="4" cy="10.75" r="0.5" style="fill: none;stroke: #000000;stroke-linecap: round;stroke-linejoin: round"></circle>
    <rect x="0.5" y="0.75" width="13" height="5" rx="2.5" transform="translate(14 6.5) rotate(-180)" style="fill: none;stroke: #000000;stroke-linecap: round;stroke-linejoin: round"></rect>
    <circle cx="10" cy="3.25" r="0.5" style="fill: none;stroke: #000000;stroke-linecap: round;stroke-linejoin: round"></circle>
  </g></g></svg>