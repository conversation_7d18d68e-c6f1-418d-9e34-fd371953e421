<svg xmlns="http://www.w3.org/2000/svg" width="972" height="612">
    <g id="lens-arrow-v2--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L972 0 L972 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:972;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:972;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:972;h:564">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:24.227;w:739;h:528" transform="translate(228, 24.2265625)">
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.000;y:263.875;w:296.418;h:120.124" transform="translate(-0.000244140625, 263.875)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:296.418;h:120.124">
                            <path id="Subtract" fill="#ffedeb" d="M 0 120.1237 L 31.0002 72.1237 L 0.0001 24.1238 L 162.5235 24.1234 C 176.5934 24.1234 190.0628 18.4219 199.8568 8.3204 L 207.924 0 L 211.0002 3.1235 C 233.9911 26.8427 264.101 42.0414 296.4178 46.6401 L 268.7798 75.1457 C 240.9046 103.896 202.5686 120.1234 162.5235 120.1234 L 0 120.1237 Z"/>
                            <path id="Subtract_1" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120.1237 L 31.0002 72.1237 L 0.0001 24.1238 L 162.5235 24.1234 C 176.5934 24.1234 190.0628 18.4219 199.8568 8.3204 L 207.924 0 L 211.0002 3.1235 C 233.9911 26.8427 264.101 42.0414 296.4178 46.6401 L 268.7798 75.1457 C 240.9046 103.896 202.5686 120.1234 162.5235 120.1234 L 0 120.1237 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:144;w:404.370;h:167.992" transform="translate(0, 144)">
                        <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:404.370;h:167.992">
                            <path id="Subtract_2" fill="#fef2e6" d="M 358.8556 167.9919 L 317.2705 167.9919 C 277.2184 167.9919 238.8763 151.7588 211 122.9995 L 199.978 111.8081 C 190.1837 101.7035 176.7121 96 162.6397 96 L 0 95.9999 L 31.0002 47.9999 L 0.0001 0 L 162.6397 0 C 202.6919 0 241.034 16.2331 268.9103 44.9924 L 279.9323 56.1838 C 289.7266 66.2884 303.1981 71.9919 317.2705 71.9919 L 357.6819 71.9919 L 404.3702 121.0401 L 358.8556 167.9919 Z"/>
                            <path id="Subtract_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 358.8556 167.9919 L 317.2705 167.9919 C 277.2184 167.9919 238.8763 151.7588 211 122.9995 L 199.978 111.8081 C 190.1837 101.7035 176.7121 96 162.6397 96 L 0 95.9999 L 31.0002 47.9999 L 0.0001 0 L 162.6397 0 C 202.6919 0 241.034 16.2331 268.9103 44.9924 L 279.9323 56.1838 C 289.7266 66.2884 303.1981 71.9919 317.2705 71.9919 L 357.6819 71.9919 L 404.3702 121.0401 L 358.8556 167.9919 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:265.039;w:492.777;h:262.960" transform="translate(0, 265.0390625)">
                        <g id="cu_Subtract_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:492.777;h:262.960">
                            <path id="Subtract_4" fill="#feecf7" d="M 0 262.9595 L 31.0002 214.9595 L 0.0001 166.9596 L 167.5887 166.9596 C 215.486 166.9596 261.3386 147.5481 294.6766 113.1573 L 404.3702 0 L 414.5333 10.6768 C 435.278 32.0766 463.208 44.8759 492.7768 46.7264 L 363.6057 179.9764 C 312.1859 233.0198 241.4642 262.9596 167.5887 262.9596 L 0 262.9595 Z"/>
                            <path id="Subtract_5" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 262.9595 L 31.0002 214.9595 L 0.0001 166.9596 L 167.5887 166.9596 C 215.486 166.9596 261.3386 147.5481 294.6766 113.1573 L 404.3702 0 L 414.5333 10.6768 C 435.278 32.0766 463.208 44.8759 492.7768 46.7264 L 363.6057 179.9764 C 312.1859 233.0198 241.4642 262.9596 167.5887 262.9596 L 0 262.9595 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:738;h:312">
                        <g id="cu_Subtract_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:738;h:312">
                            <path id="Subtract_6" fill="#fefbdb" d="M 31.0002 48.0001 L 0 0.0001 L 167.5887 0 C 241.4642 0 312.1859 29.9398 363.6057 82.9832 L 483.4622 208.898 C 487.8628 213.4377 493.9154 216 500.2379 216 L 706.9998 215.5 L 738 264 L 706.9999 311.4999 L 500.2379 312 C 467.9372 312 437.0156 298.9094 414.5333 275.7171 L 294.6766 149.8023 C 261.3386 115.4115 215.486 96 167.5887 96 L 0.0001 96 L 31.0002 48.0001 Z"/>
                            <path id="Subtract_7" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 31.0002 48.0001 L 0 0.0001 L 167.5887 0 C 241.4642 0 312.1859 29.9398 363.6057 82.9832 L 483.4622 208.898 C 487.8628 213.4377 493.9154 216 500.2379 216 L 706.9998 215.5 L 738 264 L 706.9999 311.4999 L 500.2379 312 C 467.9372 312 437.0156 298.9094 414.5333 275.7171 L 294.6766 149.8023 C 261.3386 115.4115 215.486 96 167.5887 96 L 0.0001 96 L 31.0002 48.0001 Z"/>
                        </g>
                    </g>
                </g>
                <g id="ic-cc-end" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:888;y:264.227;w:48;h:48" fill="#33de7b1a" transform="translate(888, 264.2265625)">
                    <g id="icon" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12.800;y:12.801;w:22.800;h:22.800" transform="translate(12.800048828125, 12.80078125)">
                        <path id="icon_1" transform="translate(0, -3.19921875)" fill="none" stroke="#484848" stroke-width="1.600000023841858" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 7.7428 4.5342 15.4667 9.0991 23.2 13.6487 C 18.7507 16.1148 11.5676 20.0508 6.3718 22.893 C 2.6858 24.9093 0 26.3751 0 26.3751 L 0.0002 0 Z M 23.2 13.6487 C 18.4396 18.6589 13.7669 23.7498 9.0506 28.8 C 8.1597 26.8307 7.3001 24.8457 6.3718 22.893" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-3.199;w:23.200;h:28.800"/>
                    </g>
                </g>
                <g id="container" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:60;h:48" transform="translate(12, 48)">
                    <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#ff00001a">
                        <text id="1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:0;w:48;h:48" fill="#d1bd08" transform="translate(6, 0)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                    </g>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:48.227;w:144;h:48" transform="translate(72, 48.2265625)">
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="container_1" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:192;w:60;h:48" transform="translate(12, 192)">
                    <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#ff00001a">
                        <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#db8333" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                    </g>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:192.227;w:144;h:48" transform="translate(72, 192.2265625)">
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="container_2" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:336;w:60;h:48" transform="translate(12, 336)">
                    <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#ff00001a">
                        <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#df5e59" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:336.227;w:144;h:48" transform="translate(72, 336.2265625)">
                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="container_3" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:480;w:60;h:48" transform="translate(12, 480)">
                    <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#ff00001a">
                        <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#d95da7" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                    </g>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:480.227;w:144;h:48" transform="translate(72, 480.2265625)">
                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:12.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 12.2265625)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:132.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 132.2265625)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:276.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 276.2265625)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:420.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 420.2265625)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:540.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 540.2265625)" width="24" height="24" rx="0" ry="0"/>
                <g id="tx-rc-end" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:732;y:276.227;w:144;h:24" fill="#ff00001a" transform="translate(732, 276.2265625)">
                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                </g>
                <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:60.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 60.2265625)">
                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:204.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 204.2265625)">
                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:348.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 348.2265625)">
                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:492.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 492.2265625)">
                    <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
            </g>
        </g>
    </g>
</svg>