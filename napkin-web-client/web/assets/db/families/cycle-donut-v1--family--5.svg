<svg xmlns="http://www.w3.org/2000/svg" width="1020" height="852">
    <g id="cycle-donut-v1--family--5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1020 0 L1020 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1020;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1020;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:1020;h:804">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:54;w:636.000;h:636.000" transform="translate(192, 54)">
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:15.387;y:0;w:302.613;h:261.619" transform="translate(15.38720703125, 0)">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:302.613;h:261.619">
                            <path id="Vector" fill="#edf4ff" d="M290.613 167.663 L290.613 0.2227 C158.604 5.1201 47.1604 90.4842 3.8936 208.704 L163.15 260.449 C174.954 264.285 187.545 258.324 194.297 247.91 C196.42 244.636 198.688 241.464 201.091 238.403 C191.162 228.412 187.635 213.06 193.363 199.233 C200.971 180.864 222.03 172.141 240.399 179.75 C248.645 183.165 254.947 189.291 258.7 196.667 C262.099 195.437 265.565 194.345 269.091 193.399 C281.068 190.185 290.613 180.064 290.613 167.663 Z M240.399 179.75 C222.03 172.141 200.971 180.864 193.363 199.233 C187.635 213.06 191.162 228.412 201.091 238.403 C204.352 241.685 208.304 244.388 212.846 246.269 C231.214 253.878 252.273 245.155 259.882 226.786 C264.075 216.663 263.308 205.723 258.7 196.667 C254.947 189.291 248.645 183.165 240.399 179.75 Z"/>
                            <path id="Vector_1" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 290.6131 167.6631 L 290.6131 0.2227 C 158.604 5.1201 47.1604 90.4842 3.8936 208.7037 L 163.1499 260.4492 C 174.9541 264.2846 187.5446 258.3239 194.2973 247.9099 C 196.4205 244.6356 198.6881 241.4636 201.091 238.403 C 191.1618 228.4117 187.6351 213.0601 193.3625 199.2328 C 200.9712 180.864 222.03 172.1411 240.3988 179.7498 C 248.6445 183.1653 254.9465 189.2911 258.7 196.6673 C 262.0994 195.4368 265.5653 194.345 269.0908 193.3989 C 281.0677 190.1847 290.6131 180.0638 290.6131 167.6631 Z M 240.3988 179.7498 C 222.03 172.1411 200.9712 180.864 193.3625 199.2328 C 187.6351 213.0601 191.1618 228.4117 201.091 238.403 C 204.3522 241.6846 208.3041 244.3879 212.8456 246.2691 C 231.2144 253.8777 252.2733 245.1548 259.8819 226.786 C 264.075 216.663 263.308 205.7229 258.7 196.6673 C 254.9465 189.2911 248.6445 183.1653 240.3988 179.7498 Z M 0 220 C 41.3159 92.3286 161.1822 0 302.6129 0"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:220;w:231.019;h:355.420" transform="translate(0, 220)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:231.019;h:355.420">
                            <path id="Vector_2" fill="#e8f9ff" d="M189 98.0004 C189 95.8651 189.052 93.742 189.154 91.6322 C189.754 79.2913 183.069 67.1567 171.318 63.3386 L11.8951 11.5391 C8.4717 23.6837 5.7515 36.1231 3.7814 48.8106 C1.2919 64.8426 0 81.2706 0 98.0004 C0 183.673 33.8788 261.432 88.9699 318.612 C99.172 329.201 110.101 339.084 121.678 348.181 L220.097 212.719 C227.387 202.685 225.618 188.882 217.8 179.254 C215.248 176.11 212.84 172.843 210.588 169.465 C201.685 173.734 191.073 174.345 181.233 170.269 C162.864 162.66 154.141 141.602 161.75 123.233 C166.674 111.345 177.231 103.498 189.047 101.502 C189.016 100.338 189 99.1712 189 98.0004 Z M228.269 150.786 C235.878 132.417 227.155 111.358 208.786 103.75 C202.305 101.065 195.489 100.414 189.047 101.502 C177.231 103.498 166.674 111.345 161.75 123.233 C154.141 141.602 162.864 162.66 181.233 170.269 C191.073 174.345 201.685 173.734 210.588 169.465 C218.304 165.764 224.736 159.315 228.269 150.786 Z"/>
                            <path id="Vector_3" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 189 98.0004 C 189 95.8651 189.0519 93.742 189.1544 91.6322 C 189.7544 79.2913 183.0686 67.1567 171.3179 63.3386 L 11.8951 11.5391 C 8.4717 23.6837 5.7515 36.1231 3.7814 48.8106 C 1.2919 64.8426 0 81.2706 0 98.0004 C 0 183.6725 33.8788 261.4318 88.9699 318.6118 C 99.172 329.2007 110.1014 339.0839 121.678 348.1809 L 220.097 212.7188 C 227.3869 202.6851 225.6179 188.8823 217.8005 179.2539 C 215.2478 176.1099 212.8402 172.8434 210.5877 169.4646 C 201.6849 173.7342 191.0729 174.345 181.2328 170.2691 C 162.864 162.6605 154.1411 141.6016 161.7498 123.2328 C 166.6737 111.3453 177.2309 103.4976 189.0466 101.5018 C 189.0156 100.3385 189 99.1712 189 98.0004 Z M 228.2691 150.786 C 235.8777 132.4172 227.1548 111.3584 208.786 103.7498 C 202.3048 101.0651 195.4886 100.4137 189.0466 101.5018 C 177.2309 103.4976 166.6737 111.3453 161.7498 123.2328 C 154.1411 141.6016 162.864 162.6605 181.2328 170.2691 C 191.0729 174.345 201.6849 173.7342 210.5877 169.4646 C 218.304 165.764 224.7364 159.3148 228.2691 150.786 Z M 131.2564 355.4203 C 116.0948 344.4024 101.944 332.0779 88.9699 318.6118 M 15.3871 0 C 10.2847 15.7669 6.3804 32.0729 3.7814 48.8106"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:131.256;y:411;w:373.493;h:225.000" transform="translate(131.25634765625, 411)">
                        <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:373.493;h:225.000">
                            <path id="Vector_4" fill="#e7fbf2" d="M363.653 171.286 L265.193 35.7673 C257.914 25.7492 244.27 23.1614 232.7 27.5739 C229.335 28.8572 225.903 30.0037 222.41 31.0066 C223.279 37.1631 222.564 43.6258 220.013 49.786 C212.404 68.1548 191.345 76.8777 172.976 69.2691 C157.488 62.8537 148.858 46.8759 151.093 31.011 C147.596 30.0074 144.159 28.8598 140.79 27.5752 C129.22 23.1631 115.577 25.751 108.298 35.7688 L9.8375 171.288 C60.4151 205.21 121.268 224.998 186.744 224.998 C252.22 224.998 313.075 205.209 363.653 171.286 Z M200.53 2.7498 C182.161 -4.8589 161.102 3.864 153.493 22.2328 C152.3 25.1135 151.509 28.0603 151.093 31.011 C148.858 46.8759 157.488 62.8537 172.976 69.2691 C191.345 76.8777 212.404 68.1548 220.013 49.786 C222.564 43.6258 223.279 37.1631 222.41 31.0066 C220.686 18.8053 212.738 7.8067 200.53 2.7498 Z"/>
                            <path id="Vector_5" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 363.6531 171.2861 L 265.1929 35.7673 C 257.9144 25.7492 244.2704 23.1614 232.7003 27.5739 C 229.3353 28.8572 225.9028 30.0037 222.4095 31.0066 C 223.2792 37.1631 222.5644 43.6258 220.0127 49.786 C 212.4041 68.1548 191.3453 76.8777 172.9765 69.2691 C 157.4883 62.8537 148.8579 46.8759 151.0929 31.011 C 147.5955 30.0074 144.1589 28.8598 140.7901 27.5752 C 129.2201 23.1631 115.5766 25.751 108.2983 35.7688 L 9.8375 171.2884 C 60.4151 205.2103 121.2684 224.9982 186.7436 224.9982 C 252.2202 224.9982 313.0748 205.2094 363.6531 171.2861 Z M 200.5297 2.7498 C 182.1609 -4.8588 161.102 3.864 153.4934 22.2328 C 152.3002 25.1135 151.5086 28.0603 151.0929 31.011 C 148.8579 46.8759 157.4883 62.8537 172.9765 69.2691 C 191.3453 76.8777 212.4041 68.1548 220.0127 49.786 C 222.5644 43.6258 223.2792 37.1631 222.4095 31.0066 C 220.6859 18.8053 212.7383 7.8067 200.5297 2.7498 Z M 373.4934 164.4141 C 370.2588 166.7648 366.9781 169.056 363.6531 171.2861 L 363.6541 171.2876 C 313.0757 205.2113 252.2207 225.0004 186.7436 225.0004 C 121.268 225.0004 60.4142 205.2122 9.8364 171.2899 L 9.8375 171.2884 C 6.5134 169.059 3.2337 166.7685 0 164.4186"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:405;y:220;w:231.000;h:355.416" transform="translate(405, 220)">
                        <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:231.000;h:355.416">
                            <path id="Vector_6" fill="#f2fae1" d="M10.9059 212.716 L109.325 348.179 C150.566 315.771 183.595 273.385 204.776 224.659 C221.646 185.85 231 143.018 231 98.0008 C231 77.3957 229.04 57.2483 225.297 37.7349 C223.596 28.8651 221.526 20.1264 219.104 11.5352 L59.6821 63.3344 C47.9311 67.1526 41.2453 79.2877 41.8456 91.6289 C41.9482 93.7399 42.0002 95.8643 42.0002 98.0008 C42.0002 99.1694 41.9846 100.334 41.9538 101.495 C44.5877 101.939 47.2133 102.684 49.786 103.75 C68.1548 111.358 76.8777 132.417 69.2691 150.786 C61.6605 169.155 40.6016 177.878 22.2328 170.269 C21.6166 170.014 21.0112 169.743 20.4169 169.458 C18.1638 172.839 15.7554 176.106 13.2019 179.252 C5.3849 188.88 3.6162 202.682 10.9059 212.716 Z M69.2691 150.786 C76.8777 132.417 68.1548 111.358 49.786 103.75 C47.2133 102.684 44.5877 101.939 41.9538 101.495 C25.782 98.7738 9.2927 107.437 2.7498 123.233 C-4.6036 140.985 3.2971 161.25 20.4169 169.458 C21.0112 169.743 21.6166 170.014 22.2328 170.269 C40.6016 177.878 61.6605 169.155 69.2691 150.786 Z"/>
                            <path id="Vector_7" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 10.9059 212.7158 L 109.3254 348.1788 C 150.5658 315.7708 183.5947 273.3852 204.7757 224.6595 C 221.6461 185.8498 231.0002 143.0176 231.0002 98.0008 C 231.0002 77.3957 229.0404 57.2483 225.2972 37.7349 C 223.5957 28.8651 221.5257 20.1264 219.1038 11.5352 L 59.6821 63.3344 C 47.9311 67.1526 41.2453 79.2877 41.8456 91.6289 C 41.9482 93.7399 42.0002 95.8643 42.0002 98.0008 C 42.0002 99.1694 41.9846 100.3343 41.9538 101.4955 C 44.5877 101.9388 47.2133 102.6841 49.786 103.7498 C 68.1548 111.3584 76.8777 132.4172 69.2691 150.786 C 61.6605 169.1548 40.6016 177.8777 22.2328 170.2691 C 21.6166 170.0138 21.0112 169.7434 20.4169 169.4585 C 18.1638 172.8386 15.7554 176.1064 13.2019 179.2516 C 5.3849 188.88 3.6162 202.6824 10.9059 212.7158 Z M 69.2691 150.786 C 76.8777 132.4172 68.1548 111.3584 49.786 103.7498 C 47.2133 102.6841 44.5877 101.9388 41.9538 101.4955 C 25.782 98.7738 9.2927 107.4368 2.7498 123.2328 C -4.6036 140.9854 3.2971 161.2505 20.4169 169.4585 C 21.0112 169.7434 21.6166 170.0138 22.2328 170.2691 C 40.6016 177.8777 61.6605 169.1548 69.2691 150.786 Z M 215.6131 0 C 219.5834 12.2687 222.8282 24.8639 225.2972 37.7349 M 99.75 355.4158 C 145.4598 322.197 181.9797 277.1004 204.7757 224.6595"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:318;y:0;w:302.613;h:261.615" transform="translate(318, 0)">
                        <g id="cu_Vector_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:302.613;h:261.615">
                            <path id="Vector_8" fill="#fefbdb" d="M139.46 260.445 L298.718 208.7 C255.45 90.4823 144.007 5.1201 12 0.2227 L12 167.663 C12 180.064 21.5453 190.185 33.5222 193.399 C37.0528 194.346 40.5235 195.44 43.9276 196.673 C52.3597 180.07 72.2927 172.504 89.786 179.75 C108.155 187.358 116.878 208.417 109.269 226.786 C107.412 231.269 104.754 235.177 101.53 238.414 C103.929 241.47 106.193 244.637 108.313 247.907 C115.066 258.32 127.656 264.281 139.46 260.445 Z M109.269 226.786 C116.878 208.417 108.155 187.358 89.786 179.75 C72.2927 172.504 52.3597 180.07 43.9276 196.673 C43.5056 197.504 43.1124 198.357 42.7498 199.233 C35.1412 217.602 43.864 238.66 62.2328 246.269 C76.1189 252.021 91.5422 248.44 101.53 238.414 C104.754 235.177 107.412 231.269 109.269 226.786 Z"/>
                            <path id="Vector_9" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 139.4603 260.4454 L 298.7177 208.6995 C 255.4498 90.4823 144.0074 5.1201 12 0.2227 L 12 167.6632 C 12 180.0639 21.5453 190.1847 33.5222 193.3989 C 37.0528 194.3464 40.5235 195.44 43.9276 196.6727 C 52.3597 180.0696 72.2927 172.5038 89.786 179.7498 C 108.1548 187.3584 116.8777 208.4172 109.2691 226.786 C 107.4123 231.2688 104.7544 235.1771 101.53 238.4137 C 103.9292 241.47 106.1933 244.6374 108.3134 247.9068 C 115.0662 258.3203 127.6564 264.2807 139.4603 260.4454 Z M 109.2691 226.786 C 116.8777 208.4172 108.1548 187.3584 89.786 179.7498 C 72.2927 172.5038 52.3597 180.0696 43.9276 196.6727 C 43.5056 197.5037 43.1124 198.3573 42.7498 199.2328 C 35.1412 217.6016 43.864 238.6605 62.2328 246.2691 C 76.1189 252.0209 91.5422 248.4397 101.53 238.4137 C 104.7544 235.1771 107.4123 231.2688 109.2691 226.786 Z M 0 0 C 141.4307 0 261.297 92.3286 302.6129 220"/>
                        </g>
                    </g>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:561;y:242;w:48;h:48" transform="translate(561, 242)">
                    <path id="rect" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d1bd08" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:609;y:386;w:48;h:48" transform="translate(609, 386)">
                    <path id="rect_1" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#93c332" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:486;y:474;w:48;h:48" transform="translate(486, 474)">
                    <path id="rect_2" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#3cc583" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                </g>
                <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:363;y:384;w:48;h:48" transform="translate(363, 384)">
                    <path id="rect_3" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#17aee1" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                </g>
                <g id="tx-cc-5-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:409;y:242;w:48;h:48" transform="translate(409, 242)">
                    <path id="rect_4" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#4987ec" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:626;y:159;w:48;h:48" transform="translate(626, 159)">
                    <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:486;y:576;w:48;h:48" transform="translate(486, 576)">
                    <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:266;y:420;w:48;h:48" transform="translate(266, 420)">
                    <rect id="Rectangle_7_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:348;y:159;w:48;h:48" transform="translate(348, 159)">
                    <rect id="Rectangle_7_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:708;y:426;w:48;h:48" transform="translate(708, 426)">
                    <rect id="Rectangle_7_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MAX;counter:MIN" data-position="x:768;y:60;w:144;h:60" transform="translate(768, 60)">
                    <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:852;y:456;w:144;h:60" transform="translate(852, 456)">
                    <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:108;y:60;w:144;h:60" transform="translate(108, 60)">
                    <g id="tx-rt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:517;y:13;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 517, 13)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:828;y:252;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 828, 252)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:702;y:642;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 702, 642)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:294;y:642;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 294, 642)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:252;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 252)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:481;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 481, 12)" width="24" height="24" rx="0" ry="0"/>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:24;y:456;w:144;h:60" transform="translate(24, 456)">
                    <g id="tx-rt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:438;y:720;w:144;h:60" transform="translate(438, 720)">
                    <g id="tx-ct-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <g id="tx-ct-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 60, -24)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>