<svg xmlns="http://www.w3.org/2000/svg" width="600" height="588">
    <g id="crossroads-v3--family--9" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L600 0 L600 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:600;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:600;h:0">
            <g id="body" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 144 0;gap:0;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:600;h:540">
                <g id="signs" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:36 0 0 0;gap:0;primary:MIN;counter:MIN" data-position="x:147;y:0;w:306;h:396" transform="translate(147, 0)">
                    <g id="column_left" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 96;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:36;w:144;h:360" transform="translate(0, 36)">
                        <g id="sign-1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MAX" data-position="x:96;y:12;w:48;h:60" transform="translate(96, 12)">
                            <path id="fill-lc" transform="translate(-60, 0)" fill="#f2fae1" d="M 0 30 L 18 0 L 18 60 L 0 30 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="fill-cc" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:60" fill="#f2fae1" transform="translate(-42, 0)" width="89" height="60" rx="0" ry="0"/>
                            <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(-36, 12)">

                            </g>
                            <path id="stroke-ct" transform="translate(-42, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:0"/>
                            <path id="stroke-cb" transform="translate(-42, 60)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:60;w:89;h:0"/>
                            <path id="stroke-lc" transform="translate(-60, 0)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 60 L 0 30 L 18 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-72;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, -72, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-rc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 12, 18)">

                            </g>
                            <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MAX" data-position="x:96;y:84;w:48;h:60" transform="translate(96, 84)">
                            <path id="fill-lc_1" transform="translate(-60, 0)" fill="#fef2e6" d="M 0 30 L 18 0 L 18 60 L 0 30 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="fill-cc_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:60" fill="#fef2e6" transform="translate(-42, 0)" width="89" height="60" rx="0" ry="0"/>
                            <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(-36, 12)">

                            </g>
                            <path id="stroke-ct_1" transform="translate(-42, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:0"/>
                            <path id="stroke-cb_1" transform="translate(-42, 60)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:60;w:89;h:0"/>
                            <path id="stroke-lc_1" transform="translate(-60, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 60 L 0 30 L 18 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-72;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, -72, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-rc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 12, 18)">

                            </g>
                            <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MAX" data-position="x:96;y:156;w:48;h:60" transform="translate(96, 156)">
                            <path id="fill-lc_2" transform="translate(-60, 0)" fill="#feecf7" d="M 0 30 L 18 0 L 18 60 L 0 30 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="fill-cc_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:60" fill="#feecf7" transform="translate(-42, 0)" width="89" height="60" rx="0" ry="0"/>
                            <g id="ic-cc-5" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(-36, 12)">

                            </g>
                            <path id="stroke-ct_2" transform="translate(-42, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:0"/>
                            <path id="stroke-cb_2" transform="translate(-42, 60)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:60;w:89;h:0"/>
                            <path id="stroke-lc_2" transform="translate(-60, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 60 L 0 30 L 18 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-72;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, -72, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-rc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 12, 18)">

                            </g>
                            <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MAX" data-position="x:96;y:228;w:48;h:60" transform="translate(96, 228)">
                            <path id="fill-lc_3" transform="translate(-60, 0)" fill="#edf4ff" d="M 0 30 L 18 0 L 18 60 L 0 30 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="fill-cc_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:60" fill="#edf4ff" transform="translate(-42, 0)" width="89" height="60" rx="0" ry="0"/>
                            <g id="ic-cc-7" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(-36, 12)">

                            </g>
                            <path id="stroke-ct_3" transform="translate(-42, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:0"/>
                            <path id="stroke-cb_3" transform="translate(-42, 60)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:60;w:89;h:0"/>
                            <path id="stroke-lc_3" transform="translate(-60, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 60 L 0 30 L 18 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-7" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-72;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, -72, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-rc-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 12, 18)">

                            </g>
                            <rect id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-9" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MAX" data-position="x:96;y:300;w:48;h:60" transform="translate(96, 300)">
                            <path id="fill-lc_4" transform="translate(-60, 0)" fill="#e7fbf2" d="M 0 30 L 18 0 L 18 60 L 0 30 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="fill-cc_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:60" fill="#e7fbf2" transform="translate(-42, 0)" width="89" height="60" rx="0" ry="0"/>
                            <g id="ic-cc-9" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(-36, 12)">

                            </g>
                            <path id="stroke-ct_4" transform="translate(-42, 0)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:0;w:89;h:0"/>
                            <path id="stroke-cb_4" transform="translate(-42, 60)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-42;y:60;w:89;h:0"/>
                            <path id="stroke-lc_4" transform="translate(-60, 0)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 18 60 L 0 30 L 18 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-9" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-72;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, -72, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-rc-9" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 12, 18)">

                            </g>
                            <rect id="bt-cc-add-9" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="column_center" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:144;y:36;w:18;h:360" transform="translate(144, 36)">
                        <g id="g-pole" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:26;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:18;h:360">
                            <path id="fill-cb" transform="translate(0, 360)" fill="#f0f0f0" d="M 18 0 L 0 0 L 0 114 L 18 114 L 18 113.8971 L 11.5502 113.88 C 11.5502 113.88 12.3002 107.89 4.7402 106.78 C 12.6302 103.53 15.4103 108.56 15.4103 108.56 C 15.4103 108.56 14.9944 101.9856 18 96.1996 L 18 0 Z M 0 113.9182 L 11.5 113.9182 L 0 113.9182 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:360;w:18;h:114"/>
                            <path id="fill-cc_5" fill="#f0f0f0" d="M 0 0 L 18 0 L 18 360 L 0 360 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:360"/>
                            <path id="stroke-rb" transform="translate(18, 360)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 96 L 0 0" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:360;w:0;h:96"/>
                            <path id="stroke-lb" transform="translate(0, 360)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 113 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:360;w:0;h:113"/>
                            <path id="stroke-rc" transform="translate(18, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 360 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:0;h:360"/>
                            <path id="stroke-lc_5" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 360 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:360"/>
                            <path id="fill-ct" transform="translate(-3, -12)" fill="#f0f0f0" d="M 0 0 L 24 0 L 24 12 L 0 12 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-3;y:-12;w:24;h:12"/>
                            <path id="stroke-ct_5" transform="translate(-3, -12)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 24 0 L 24 12 L 0 12 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-3;y:-12;w:24;h:12"/>
                            <g id="plant" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-35.000;y:449;w:88.000;h:36.520" transform="translate(-35.000179290771484, 449)">
                                <path id="fill-cc_6" fill="#f2fae1" d="M 39.7227 17.78 C 47.6127 14.53 50.3927 19.56 50.3927 19.56 C 50.3927 19.56 49.4527 4.7 60.6127 0 C 57.4427 3.77 56.6927 10.52 58.5527 16 C 62.9927 12.89 68.8827 13.56 70.8827 16.67 C 64.8152 13.8605 58.7884 20.3838 61.4825 25.0084 L 87.7605 25.0084 C 87.7605 25.0084 80.3901 36.5205 44.1829 36.5205 C 7.9757 36.5205 0 25.0084 0 25.0084 L 46.5327 25.0084 C 47.0185 20.4711 44.5158 18.4837 39.7227 17.78 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:87.760;h:36.520"/>
                                <path id="stroke" data-entity-classes="Stroke" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 46.5502 24.88 C 46.5502 24.88 47.3002 18.89 39.7402 17.78 C 47.6302 14.53 50.4103 19.56 50.4103 19.56 C 50.4103 19.56 49.4702 4.7 60.6302 0 C 57.4602 3.77 56.7103 10.52 58.5703 16 C 63.0103 12.89 68.9003 13.56 70.9003 16.67 C 66.6803 14.44 59.89 18.4682 61 24.9182 M 61 24.9182 L 88 24.9182 M 0 24.9182 L 46.5 24.9182" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:88;h:24.918"/>
                            </g>
                        </g>
                    </g>
                    <g id="column_right" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:48 96 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:162;y:36;w:144;h:324" transform="translate(162, 36)">
                        <g id="sign-2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:48;w:48;h:60" transform="translate(0, 48)">
                            <path id="fill-cc_7" transform="translate(1, 0)" fill="#fefbdb" d="M 0 0 L 89 0 L 89 60 L 0 60 L 0 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:60"/>
                            <path id="fill-rc" transform="translate(90, 0)" fill="#fefbdb" d="M 18 30 L 0 0 L 0 60 L 18 30 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <path id="stroke-cb_5" transform="translate(1, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:60;w:89;h:0"/>
                            <path id="stroke-ct_6" transform="translate(1, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:0"/>
                            <g id="ic-cc-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(48, 12)">

                            </g>
                            <path id="stroke-rc_1" transform="translate(90, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 18 30 L 0 60" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 96, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="translate(12, 18)">

                            </g>
                            <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:120;w:48;h:60" transform="translate(0, 120)">
                            <path id="fill-cc_8" transform="translate(1, 0)" fill="#ffedeb" d="M 0 0 L 89 0 L 89 60 L 0 60 L 0 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:60"/>
                            <path id="fill-rc_1" transform="translate(90, 0)" fill="#ffedeb" d="M 18 30 L 0 0 L 0 60 L 18 30 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <path id="stroke-cb_6" transform="translate(1, 60)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:60;w:89;h:0"/>
                            <path id="stroke-ct_7" transform="translate(1, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:0"/>
                            <g id="ic-cc-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(48, 12)">

                            </g>
                            <path id="stroke-rc_2" transform="translate(90, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 18 30 L 0 60" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 96, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="translate(12, 18)">

                            </g>
                            <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:192;w:48;h:60" transform="translate(0, 192)">
                            <path id="fill-cc_9" transform="translate(1, 0)" fill="#faf0ff" d="M 0 0 L 89 0 L 89 60 L 0 60 L 0 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:60"/>
                            <path id="fill-rc_2" transform="translate(90, 0)" fill="#faf0ff" d="M 18 30 L 0 0 L 0 60 L 18 30 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <path id="stroke-cb_7" transform="translate(1, 60)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:60;w:89;h:0"/>
                            <path id="stroke-ct_8" transform="translate(1, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:0"/>
                            <g id="ic-cc-6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(48, 12)">

                            </g>
                            <path id="stroke-rc_3" transform="translate(90, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 18 30 L 0 60" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 96, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-lc-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="translate(12, 18)">

                            </g>
                            <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="sign-8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:18 12 18 12;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:264;w:48;h:60" transform="translate(0, 264)">
                            <path id="fill-cc_10" transform="translate(1, 0)" fill="#e8f9ff" d="M 0 0 L 89 0 L 89 60 L 0 60 L 0 0 Z" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:60"/>
                            <path id="fill-rc_3" transform="translate(90, 0)" fill="#e8f9ff" d="M 18 30 L 0 0 L 0 60 L 18 30 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <path id="stroke-cb_8" transform="translate(1, 60)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 89 0 L 0 0" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:60;w:89;h:0"/>
                            <path id="stroke-ct_9" transform="translate(1, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 89 0" data-constraints="horizontal:STRETCH;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:0;w:89;h:0"/>
                            <g id="ic-cc-8" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(48, 12)">

                            </g>
                            <path id="stroke-rc_4" transform="translate(90, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 18 30 L 0 60" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:18;h:60"/>
                            <rect id="bt-cc-remove-8" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 96, 18)" width="24" height="24" rx="0" ry="0"/>
                            <g id="tx-lc-8" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:18;w:24;h:24;wMax:156;hMax:60" fill="#ff00001a" transform="translate(12, 18)">

                            </g>
                            <rect id="bt-cc-add-8" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:-18;w:24;h:24" fill="#1ac6ff33" transform="translate(12, -18)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-10" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:54;w:24;h:24" fill="#1ac6ff33" transform="translate(12, 54)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>