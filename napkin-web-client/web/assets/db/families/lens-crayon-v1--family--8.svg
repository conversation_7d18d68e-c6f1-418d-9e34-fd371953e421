<svg xmlns="http://www.w3.org/2000/svg" width="528" height="532">
    <g id="lens-crayon-v1--family--8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L528 0 L528 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:528;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:528;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:528;h:484">
                <g id="stack" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:12;y:12;w:504;h:460" transform="translate(12, 12)">
                    <g id="split" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:0;w:504;h:384">
                        <g id="left" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:-0.000;y:12;w:252;h:372" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, -2.842170943040401e-14, 12)">
                            <g id="flag-7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:-0.000;w:252;h:372" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, -2.842170943040401e-14)">
                                <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 60 12 12;gap:12;primary:MIN;counter:MAX" data-position="x:24;y:0.000;w:228;h:84" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 24, 1.4210854715202004e-14)">
                                    <rect id="text-7-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#edf4ff" width="228" height="84" rx="0" ry="0"/>
                                    <path id="contour-7" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 84 L 1.0287e-14 0 L 228 2.7922e-14 L 228 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                    <g id="tx-rc-7" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:24" fill="#ff00001a" transform="translate(12, 12)">
                                        <text id="Label" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#4987ec" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                    </g>
                                    <g id="tx-rc-7-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:156;h:24" fill="#ff00001a" transform="translate(12, 48)">
                                        <text id="Label_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                    </g>
                                    <g id="ic-cc-7" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(180, 12)">
                                        <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                            <path id="icon_1" transform="translate(10, 8)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                        </g>
                                    </g>
                                    <rect id="bt-cc-add-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, 186, -12)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-remove-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, -12, 12)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                                <g id="flag5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 8 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:84;w:252;h:288" transform="translate(0, 84)">
                                    <g id="stick-7" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:244;y:0;w:8;h:288" transform="translate(244, 0)">
                                        <path id="stick-fill-7" fill="#edf4ff" d="M 8 0 L 0 0 L 0 288 L 8 288 L 8 0 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:8;h:288"/>
                                        <path id="stick-stroke-7" transform="translate(7.99609375, 0)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 288 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:7.996;y:0;w:0;h:288"/>
                                    </g>
                                    <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 60 12 12;gap:12;primary:MIN;counter:MAX" data-position="x:16;y:0.000;w:228;h:84" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 16, 1.4210854715202004e-14)">
                                        <rect id="text-5-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#faf0ff" width="228" height="84" rx="0" ry="0"/>
                                        <path id="contour-5" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 84 L 1.0287e-14 0 L 228 2.7922e-14 L 228 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                        <g id="tx-rc-5" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:24" fill="#ff00001a" transform="translate(12, 12)">
                                            <text id="Label_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#b960e2" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                        </g>
                                        <g id="tx-rc-5-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:156;h:24" fill="#ff00001a" transform="translate(12, 48)">
                                            <text id="Label_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                        </g>
                                        <g id="ic-cc-5" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(180, 12)">
                                            <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                <path id="icon_3" transform="translate(10, 8)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                            </g>
                                        </g>
                                        <rect id="bt-cc-add-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, 186, -12)" width="24" height="24" rx="0" ry="0"/>
                                        <rect id="bt-cc-remove-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, -12, 12)" width="24" height="24" rx="0" ry="0"/>
                                    </g>
                                    <g id="flag3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 8 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:84;w:244;h:204" transform="translate(0, 84)">
                                        <g id="stick-5" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:236;y:0;w:8;h:204" transform="translate(236, 0)">
                                            <path id="stick-fill-5" fill="#faf0ff" d="M 8 0 L 0 0 L 0 204 L 8 204 L 8 0 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:8;h:204"/>
                                            <path id="stick-stroke-5" transform="translate(7.99609375, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 204 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:7.996;y:0;w:0;h:204"/>
                                        </g>
                                        <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 60 12 12;gap:12;primary:MIN;counter:MAX" data-position="x:8;y:0.000;w:228;h:84" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 8, 1.4210854715202004e-14)">
                                            <rect id="text-3-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#ffedeb" width="228" height="84" rx="0" ry="0"/>
                                            <path id="contour-3" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 84 L 1.0287e-14 0 L 228 2.7922e-14 L 228 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                            <g id="tx-rc-3" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:24" fill="#ff00001a" transform="translate(12, 12)">
                                                <text id="Label_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#df5e59" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                            </g>
                                            <g id="tx-rc-3-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:156;h:24" fill="#ff00001a" transform="translate(12, 48)">
                                                <text id="Label_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                            </g>
                                            <g id="ic-cc-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(180, 12)">
                                                <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                    <path id="icon_5" transform="translate(10, 8)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                                </g>
                                            </g>
                                            <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, 186, -12)" width="24" height="24" rx="0" ry="0"/>
                                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, -12, 12)" width="24" height="24" rx="0" ry="0"/>
                                        </g>
                                        <g id="flag1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 8 36 0;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:84;w:236;h:120" transform="translate(0, 84)">
                                            <g id="stick-3" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:0;w:8;h:120" transform="translate(228, 0)">
                                                <path id="stick-fill-3" fill="#ffedeb" d="M 8 0 L 0 0 L 0 120 L 8 120 L 8 0 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:8;h:120"/>
                                                <path id="stick-stroke-3" transform="translate(7.99609375, 0)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 120 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:7.996;y:0;w:0;h:120"/>
                                            </g>
                                            <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 60 12 12;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:228;h:84" transform="matrix(1, -1.2246468525851679e-16, 1.2246468525851679e-16, 1, 0, 0)">
                                                <rect id="text-1-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#fefbdb" width="228" height="84" rx="0" ry="0"/>
                                                <path id="contour-1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 228 84 L 228 2.7922e-14 L 1.0287e-14 0 L 0 84 L 220 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                                <g id="tx-rc-1" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:24" fill="#ff00001a" transform="translate(12, 12)">
                                                    <text id="Label_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#d1bd08" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                                </g>
                                                <g id="tx-rc-1-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:156;h:24" fill="#ff00001a" transform="translate(12, 48)">
                                                    <text id="Label_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 0, 0)" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                                </g>
                                                <g id="ic-cc-1" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(180, 12)">
                                                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                        <path id="icon_7" transform="translate(10, 8)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                                    </g>
                                                </g>
                                                <g id="stick-1" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:220;y:84;w:8;h:36" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 220, 84)">
                                                    <path id="stick-fill-1" fill="#fefbdb" d="M 8 0 L 0 0 L 0 36 L 8 36 L 8 0 Z" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:8;h:36"/>
                                                    <path id="stick-stroke-1" transform="translate(7.99609375, 0)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 36 L 0 0" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:7.996;y:0;w:0;h:36"/>
                                                    <path id="stick-stroke-1_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 36 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:36"/>
                                                </g>
                                                <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, 186, -12)" width="24" height="24" rx="0" ry="0"/>
                                                <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:186;y:48;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, 186, 48)" width="24" height="24" rx="0" ry="0"/>
                                                <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 1.2246467202362698e-16, -1.2246467202362698e-16, 0.9999999403953552, -12, 12)" width="24" height="24" rx="0" ry="0"/>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="right" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 12 0;gap:0;primary:MIN;counter:MIN" data-position="x:252;y:0;w:252;h:384" transform="translate(252, 0)">
                            <g id="flag-8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:252;h:372">
                                <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 60;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:228;h:84">
                                    <rect id="text-8-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#e8f9ff" width="228" height="84" rx="0" ry="0"/>
                                    <path id="contour-8" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 228 84 L 228 0 L 0 0 L 0 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                    <g id="tx-lc-8" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:12;w:156;h:24" fill="#ff00001a" transform="translate(60, 12)">
                                        <text id="Label_8" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                    <g id="tx-lc-8-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:48;w:156;h:24" fill="#ff00001a" transform="translate(60, 48)">
                                        <text id="Label_9" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                    <g id="ic-cc-8" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(12, 12)">
                                        <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                            <path id="icon_9" transform="translate(10, 8)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                        </g>
                                    </g>
                                    <path id="bt-cc-add-9" transform="translate(18, -12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:-12;w:24;h:24"/>
                                    <path id="bt-cc-remove-8" transform="translate(216, 12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:12;w:24;h:24"/>
                                </g>
                                <g id="flag6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 8;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:84;w:252;h:288" transform="translate(0, 84)">
                                    <g id="stick-8" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:0;w:8;h:300" transform="translate(8, 0)">
                                        <path id="stick-fill-8" transform="translate(-8, 0)" fill="#e8f9ff" d="M 0 0 L 8 0 L 8 300 L 0 300 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-8;y:0;w:8;h:300"/>
                                        <path id="stick-stroke-8" transform="translate(-7.99609375, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 300 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-7.996;y:0;w:0;h:300"/>
                                    </g>
                                    <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 60;gap:12;primary:MIN;counter:MIN" data-position="x:8;y:0;w:228;h:84" transform="translate(8, 0)">
                                        <rect id="text-6-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#f3f0ff" width="228" height="84" rx="0" ry="0"/>
                                        <path id="contour-6" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 228 84 L 228 0 L 0 0 L 0 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                        <g id="tx-lc-6" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:12;w:156;h:24" fill="#ff00001a" transform="translate(60, 12)">
                                            <text id="Label_10" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                        </g>
                                        <g id="tx-lc-6-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:48;w:156;h:24" fill="#ff00001a" transform="translate(60, 48)">
                                            <text id="Label_11" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                        </g>
                                        <path id="bt-cc-remove-6" transform="translate(216, 12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:12;w:24;h:24"/>
                                        <g id="ic-cc-6" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(12, 12)">
                                            <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                <path id="icon_11" transform="translate(10, 8)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                            </g>
                                        </g>
                                        <path id="bt-cc-add-7" transform="translate(18, -12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:-12;w:24;h:24"/>
                                    </g>
                                    <g id="flag4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 8;gap:0;primary:MIN;counter:MIN" data-position="x:8;y:84;w:244;h:204" transform="translate(8, 84)">
                                        <g id="stick-6" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:0;w:8;h:216" transform="translate(8, 0)">
                                            <path id="stick-fill-6" transform="translate(-8, 0)" fill="#f3f0ff" d="M 0 0 L 8 0 L 8 216 L 0 216 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-8;y:0;w:8;h:216"/>
                                            <path id="stick-stroke-6" transform="translate(-7.99609375, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 216 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-7.996;y:0;w:0;h:216"/>
                                        </g>
                                        <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 60;gap:12;primary:MIN;counter:MIN" data-position="x:8;y:0;w:228;h:84" transform="translate(8, 0)">
                                            <rect id="text-4-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#feecf7" width="228" height="84" rx="0" ry="0"/>
                                            <path id="contour-4" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 228 84 L 228 0 L 0 0 L 0 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                            <g id="tx-lc-4" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:12;w:156;h:24" fill="#ff00001a" transform="translate(60, 12)">
                                                <text id="Label_12" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                            </g>
                                            <g id="tx-lc-4-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:48;w:156;h:24" fill="#ff00001a" transform="translate(60, 48)">
                                                <text id="Label_13" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                            </g>
                                            <g id="ic-cc-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(12, 12)">
                                                <g id="icon_12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                    <path id="icon_13" transform="translate(10, 8)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                                </g>
                                            </g>
                                            <path id="bt-cc-add-5" transform="translate(18, -12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:-12;w:24;h:24"/>
                                            <path id="bt-cc-remove-4" transform="translate(216, 12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:12;w:24;h:24"/>
                                        </g>
                                        <g id="flag2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 36 8;gap:0;primary:MIN;counter:MAX" data-position="x:8;y:84;w:236;h:120;hMin:72" transform="translate(8, 84)">
                                            <g id="stick-4" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:0;w:8;h:132" transform="translate(8, 0)">
                                                <path id="stick-fill-4" transform="translate(-8, 0)" fill="#feecf7" d="M 0 0 L 8 0 L 8 132 L 0 132 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-8;y:0;w:8;h:132"/>
                                                <path id="stick-stroke-4" transform="translate(-7.99609375, 0)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 132 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-7.996;y:0;w:0;h:132"/>
                                            </g>
                                            <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 60;gap:12;primary:MIN;counter:MIN" data-position="x:8;y:0;w:228;h:84" transform="translate(8, 0)">
                                                <rect id="text-2-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84" fill="#fef2e6" width="228" height="84" rx="0" ry="0"/>
                                                <path id="contour-2" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 84 L 0 0 L 228 0 L 228 84 L 8 84" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:228;h:84"/>
                                                <g id="tx-lc-2" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:12;w:156;h:24" fill="#ff00001a" transform="translate(60, 12)">
                                                    <text id="Label_14" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                                </g>
                                                <path id="bt-cc-remove-2" transform="translate(216, 12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:12;w:24;h:24"/>
                                                <g id="tx-lc-2-desc" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:48;w:156;h:24" fill="#ff00001a" transform="translate(60, 48)">
                                                    <text id="Label_15" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                                </g>
                                                <g id="ic-cc-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:36;h:36" fill="#33de7b1a" transform="translate(12, 12)">
                                                    <g id="icon_14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                                        <path id="icon_15" transform="translate(10, 8)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 5.3399 3.1487 10.6667 6.3188 16 9.4783 C 12.9315 11.1908 7.9776 13.9242 4.3943 15.8979 C 1.8523 17.2981 0 18.3161 0 18.3161 L 0.0001 0 Z M 16 9.4783 C 12.717 12.9576 9.4944 16.4929 6.2418 20 C 5.6273 18.6324 5.0346 17.2539 4.3943 15.8979" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:8;w:16;h:20"/>
                                                    </g>
                                                </g>
                                                <g id="stick-2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:84;w:8;h:48" transform="translate(8, 84)">
                                                    <path id="stick-fill-2" transform="translate(-8, 0)" fill="#fef2e6" d="M 0 0 L 8 0 L 8 48 L 0 48 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-8;y:0;w:8;h:48.000"/>
                                                    <path id="stick-stroke-2" transform="translate(-7.99609375, 0)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 48 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-7.996;y:0;w:0;h:48.000"/>
                                                    <path id="stick-stroke-2_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 48 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0.000;h:48.000"/>
                                                </g>
                                                <path id="bt-cc-add-3" transform="translate(18, -12)" fill="#1ac6ff33" d="M 0 0 L 24 0 L 24 24 L 0 24 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:-12;w:24;h:24"/>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                    <g id="g-0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:218;y:384;w:68;h:76" transform="translate(218, 384)">
                        <g id="cu_Vector_68" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:23.332;y:51;w:21.339;h:25">
                            <path id="Vector_68" transform="translate(23.33208656311035, 51)" fill="#f6f6f6" d="M 0 0 L 10.6669 25 L 21.3385 0 L 0 0 Z"/>
                            <path id="Vector_68_1" transform="translate(23.33208656311035, 51)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 10.6669 25 L 21.3385 0 L 0 0 Z"/>
                        </g>
                        <g id="cu_Vector_69" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:2;y:0;w:64;h:51">
                            <path id="Vector_69" transform="translate(2, 0)" fill="#f6f6f6" d="M 64 0 L 0 0 L 21.3331 51 L 42.6675 51 L 64 0 Z"/>
                            <path id="Vector_69_1" transform="translate(2, 0)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 64 0 L 0 0 L 21.3331 51 L 42.6675 51 L 64 0 Z"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>