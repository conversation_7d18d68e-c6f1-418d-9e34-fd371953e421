<svg xmlns="http://www.w3.org/2000/svg" width="612" height="313">    <g id="stairs-3d-v1--family--2">        <g id="lines">            <g id="Group_386">                <g id="cu">                    <g id="cu_1" >                        <path id="vector" transform="translate(108, 216)" fill="#f6f6f6" d="M0 72 L0 0 L72 0 L72 72 L0 72 Z"></path>
                        <path id="vector_1" transform="translate(108, 216)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 72 L 0 0 L 72 0 L 72 72 L 0 72 Z"></path></g>
                    <g id="cu_2" >                        <path id="vector_2" transform="translate(108, 192)" fill="#f6f6f6" d="M24 0 L0 24 L72 24 L96 0 L24 0 Z"></path>
                        <path id="vector_3" transform="translate(108, 192)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 24 L 24 0 L 96 0 L 72 24 L 0 24 Z"></path></g>
                    <g id="cu_3" >                        <path id="vector_4" transform="translate(132, 120)" fill="#f6f6f6" d="M0 72 L0 0 L72 0 L72 72 L0 72 Z"></path>
                        <path id="vector_5" transform="translate(132, 120)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 72 L 0 0 L 72 0 L 72 72 L 0 72 Z"></path></g>
                    <g id="cu_4" >                        <path id="vector_6" transform="translate(132, 96)" fill="#f6f6f6" d="M24 0 L0 24 L72 24 L96 0 L24 0 Z"></path>
                        <path id="vector_7" transform="translate(132, 96)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 24 L 24 0 L 96 0 L 72 24 L 0 24 Z"></path></g></g></g>
            <g id="Group_385">                <g id="cu_5">                    <g id="cu_6" >                        <path id="vector_8" transform="translate(432, 216)" fill="#f6f6f6" d="M72 0 L72 72 L0 72 L0 0 L72 0 Z"></path>
                        <path id="vector_9" transform="translate(432, 216)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 72 0 L 72 72 L 0 72 L 0 0 L 72 0 Z"></path></g>
                    <g id="cu_7" >                        <path id="vector_10" transform="translate(408, 192)" fill="#f6f6f6" d="M96 24 L72 0 L0 0 L24 24 L96 24 Z"></path>
                        <path id="vector_11" transform="translate(408, 192)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 96 24 L 72 0 L 0 0 L 24 24 L 96 24 Z"></path></g>
                    <g id="cu_8" >                        <path id="vector_12" transform="translate(408, 120)" fill="#f6f6f6" d="M72 0 L72 72 L0 72 L0 0 L72 0 Z"></path>
                        <path id="vector_13" transform="translate(408, 120)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 72 72 L 72 0 L 0 0 L 0 72 L 72 72 Z"></path></g>
                    <g id="cu_9" >                        <path id="vector_14" transform="translate(384, 96)" fill="#f6f6f6" d="M96 24 L72 0 L0 0 L24 24 L96 24 Z"></path>
                        <path id="vector_15" transform="translate(384, 96)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 96 24 L 72 7.5791e-14 L 0 0 L 24 24 L 96 24 Z"></path></g></g></g>
            <g id="g-1">                <g id="cu_10">                    <g id="cu_11" >                        <path id="vector_16" transform="translate(180.00009155273438, 215.99999237060547)" fill="#e8f9ff" d="M0 72 L0 0 L252 0 L252 72 L0 72 Z"></path>
                        <path id="vector_17" transform="translate(180.00009155273438, 215.99999237060547)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 72 L 252 72 L 252 0 L 0 0 Z"></path></g>
                    <g id="cu_12" >                        <path id="vector_18" transform="translate(180.00009155273438, 191.99999237060547)" fill="#e8f9ff" d="M228 0 L252 24 L0 24 L24 0 L228 0 Z"></path>
                        <path id="vector_19" transform="translate(180.00009155273438, 191.99999237060547)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 252 24 L 228 1.1369e-13 L 24 0 L 0 24 L 252 24 Z"></path></g></g></g>
            <g id="g-2">                <g id="cu_13">                    <g id="cu_14" >                        <path id="vector_20" transform="translate(204.00009155273438, 120)" fill="#edf4ff" d="M0 72 L0 0 L204 0 L204 72 L0 72 Z"></path>
                        <path id="vector_21" transform="translate(204.00009155273438, 120)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 72 L 204 72 L 204 0 L 0 0 Z"></path></g>
                    <g id="cu_15" >                        <path id="vector_22" transform="translate(204.00009155273438, 96)" fill="#edf4ff" d="M180 0 L204 24 L0 24 L24 0 L180 0 Z"></path>
                        <path id="vector_23" transform="translate(204.00009155273438, 96)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 204 24 L 180 0 L 24 0 L 0 24 L 204 24 Z"></path></g></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 0, 0)" fill="#ff00001a" d="M0 0 L612 0 L612 48 L0 48 L0 0 Z"></path>
        <path id="tx-cc-1" transform="translate(192, 228)" fill="#ff00001a" d="M0 0 L228 0 L228 48 L0 48 L0 0 Z"></path>
        <path id="tx-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 216, 132)" fill="#ff00001a" d="M0 0 L180 0 L180 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 96, 240)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 120, 144)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, 5.551115123125783e-17, -1, 294, 216)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 96)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="ic-cc-2">            <path id="rect" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 420.00000000000006, 132)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon" transform="translate(420.00000000000006, 132)">                <path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-2b">            <path id="rect_1" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 144.00000000000009, 132)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="second-layer#number-02-48px" transform="translate(144, 132)">                <path id="vector_24" transform="translate(5.072265625, 9.6956787109375)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 10.4832 C 0 4.7864 2.7165 -0.0001 7.9385 -0.0001 C 13.1605 -0.0001 15.877 4.7864 15.877 10.4832 L 15.877 17.5774 C 15.877 23.2742 13.0331 28.1863 7.9385 28.1863 C 2.8438 28.1863 0 23.2742 0 17.5774 L 0 10.4832 Z"></path>
                <path id="vector_25" transform="translate(26.83203125, 9.6956787109375)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 7.8201 C 0 3.1998 3.2243 1.9692e-22 7.6061 1.2286e-22 C 14.8751 0 17.3544 7.0933 12.5898 13.4031 L 0.4984 27.7222 L 17.1191 27.7222"></path></g></g>
        <g id="ic-cc-1">            <path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 444.00000000000006, 228)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_2" transform="translate(444.00000000000006, 228)">                <path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-1b">            <path id="rect_3" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 120.00000000000009, 228)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="second-layer#number-01-48px" transform="translate(120, 228)">                <path id="Vector_2128" transform="translate(27.841796875, 10.18408203125)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 3.1938 L 8.4551 0 L 8.4551 27.7674"></path>
                <path id="vector_26" transform="translate(5.072265625, 9.695630073547363)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 10.4832 C 0 4.7864 2.7165 -0.0001 7.9385 -0.0001 C 13.1605 -0.0001 15.877 4.7864 15.877 10.4832 L 15.877 17.5774 C 15.877 23.2742 13.0331 28.1863 7.9385 28.1863 C 2.8438 28.1863 0 23.2742 0 17.5774 L 0 10.4832 Z"></path></g></g></g></svg>