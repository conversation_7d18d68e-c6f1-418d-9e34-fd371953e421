<svg xmlns="http://www.w3.org/2000/svg" width="528" height="957">
    <g id="list-pill-v1--family--7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L528 0 L528 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:528;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:528;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:528;h:909">
                <g id="row" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:48;y:48;w:432;h:96" transform="translate(48, 48)">
                    <g id="g-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:12;w:432;h:72;hMin:72" transform="translate(0, 12)">
                        <path id="top-fill" transform="translate(0, -12)" fill="#fefbdb" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill" transform="translate(0, 35)" fill="#fefbdb" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill" transform="translate(-0.0078125, 36)" fill="#fefbdb" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke" transform="translate(0, -12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke" transform="translate(0, 35)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke" transform="translate(-0.0078125, 36)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-1" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:-36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, -36)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:132;w:432;h:72;hMin:72" transform="translate(0, 132)">
                        <path id="top-fill_1" transform="translate(0, -12)" fill="#fef2e6" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_1" transform="translate(0, 35)" fill="#fef2e6" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_1" transform="translate(-0.0078125, 36)" fill="#fef2e6" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_1" transform="translate(0, -12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_1" transform="translate(0, 35)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_1" transform="translate(-0.0078125, 36)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-2" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="g-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:252;w:432;h:72;hMin:72" transform="translate(0, 252)">
                        <path id="top-fill_2" transform="translate(0, -12)" fill="#ffedeb" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_2" transform="translate(0, 35)" fill="#ffedeb" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_2" transform="translate(-0.0078125, 36)" fill="#ffedeb" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_2" transform="translate(0, -12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_2" transform="translate(0, 35)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_2" transform="translate(-0.0078125, 36)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-3" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="g-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:372;w:432;h:72;hMin:72" transform="translate(0, 372)">
                        <path id="top-fill_3" transform="translate(0, -12)" fill="#feecf7" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_3" transform="translate(0, 35)" fill="#feecf7" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_3" transform="translate(-0.0078125, 36)" fill="#feecf7" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_3" transform="translate(0, -12)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_3" transform="translate(0, 35)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_3" transform="translate(-0.0078125, 36)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-4" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-4" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="g-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:492;w:432;h:72;hMin:72" transform="translate(0, 492)">
                        <path id="top-fill_4" transform="translate(0, -12)" fill="#faf0ff" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_4" transform="translate(0, 35)" fill="#faf0ff" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_4" transform="translate(-0.0078125, 36)" fill="#faf0ff" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_4" transform="translate(0, -12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_4" transform="translate(0, 35)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_4" transform="translate(-0.0078125, 36)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-5" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="g-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:612;w:432;h:72;hMin:72" transform="translate(0, 612)">
                        <path id="top-fill_5" transform="translate(0, -12)" fill="#f3f0ff" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_5" transform="translate(0, 35)" fill="#f3f0ff" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_5" transform="translate(-0.0078125, 36)" fill="#f3f0ff" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_5" transform="translate(0, -12)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_5" transform="translate(0, 35)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_5" transform="translate(-0.0078125, 36)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_10" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-6" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-6" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="g-7" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:MIN" data-position="x:0;y:732;w:432;h:72;hMin:72" transform="translate(0, 732)">
                        <path id="top-fill_6" transform="translate(0, -12)" fill="#edf4ff" d="M 47.99 11.88 C 67.7 11.88 83.71 27.56 83.98 47 L 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47 L 12 47 C 12.27 27.56 28.28 11.88 47.99 11.88 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-fill_6" transform="translate(0, 35)" fill="#edf4ff" d="M 12.0003 0 L 0 0 L 0 1 L 12.0003 1 L 12.0003 0 Z M 432 0 L 84.0019 0 L 84.0019 1 L 432 1 L 432 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-fill_6" transform="translate(-0.0078125, 36)" fill="#edf4ff" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 L 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0 L 83.99 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <path id="top-stroke_6" transform="translate(0, -12)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.98 47 C 83.71 27.56 67.7 11.88 47.99 11.88 C 28.28 11.88 12.27 27.56 12 47 M 431.98 47 C 431.71 21 410.33 0 383.99 0 L 47.99 0 C 34.73 0 22.73 5.31 14.05 13.91 C 5.47 22.39 0.14 34.08 0 47" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:431.980;h:47"/>
                        <path id="sides-stroke_6" transform="translate(0, 35)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 12 1 M 0 1 L 0 0 M 432 0 L 432 1 M 84 1 L 84 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:35;w:432;h:1"/>
                        <path id="bottom-stroke_6" transform="translate(-0.0078125, 36)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 83.99 0 C 83.99 0.17 84 0.33 84 0.5 C 84 20.17 67.88 36.12 48 36.12 C 28.12 36.12 12 20.17 12 0.5 C 12 0.33 12.01 0.17 12.01 0 M 0.01 0 C 0.01 0.17 0 0.33 0 0.5 C 0 26.73 21.49 48 48 48 L 384 48 C 397.26 48 409.26 42.69 417.94 34.09 C 426.63 25.5 432 13.63 432 0.5 C 432 0.33 431.99 0.17 431.99 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.008;y:36;w:432;h:48"/>
                        <g id="text_12" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:6;w:408;h:60" transform="translate(0, 6)">
                            <rect id="bt-cc-add-8" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 72)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text_13" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:96;y:0;w:300;h:60" transform="translate(96, 0)">
                                <g id="tx-lt-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#ff00001a">
                                    <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-7-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:312;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:312;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                            </g>
                            <g id="ic-cc-7" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:6;w:48;h:48" fill="#33de7b1a" transform="translate(24, 6)">
                                <g id="icon_12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_13" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <rect id="bt-cc-remove-7" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>