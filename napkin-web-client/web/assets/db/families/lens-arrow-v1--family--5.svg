<svg xmlns="http://www.w3.org/2000/svg" width="1020" height="768">
    <g id="lens-arrow-v1--family--5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1020 0 L1020 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1020;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1020;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:1020;h:720">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:227;y:23.227;w:784;h:674" transform="translate(227, 23.2265625)">
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1.062;y:337.004;w:559.944;h:335.997" transform="translate(1.062255859375, 337.00390625)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:559.944;h:335.997">
                            <path id="Subtract" fill="#edf4ff" d="M282.376 186.196 L462.877 0 L474.233 11.7143 C496.715 34.9066 527.637 47.9972 559.938 47.9972 L559.944 47.9972 C553.621 47.9972 547.568 50.5596 543.168 55.0993 L351.305 253.016 C299.885 306.058 229.164 335.997 155.29 335.997 L0 335.997 L0 335.927 L30.9377 287.997 L0 240.067 L0 239.997 L155.29 239.997 C203.187 239.997 249.038 220.586 282.376 186.196 Z"/>
                            <path id="Subtract_1" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 282.3765 186.1963 L 462.8773 0 L 474.233 11.7143 C 496.7153 34.9066 527.637 47.9972 559.9377 47.9972 L 559.944 47.9972 C 553.6213 47.9972 547.5684 50.5596 543.1676 55.0993 L 351.3047 253.0161 C 299.8851 306.0582 229.1644 335.9972 155.2899 335.9972 L 0 335.9972 L 0 335.9273 L 30.9377 287.9971 L 0 240.0668 L 0 239.9972 L 155.2899 239.9972 C 203.1866 239.9972 249.0385 220.5862 282.3765 186.1963 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1.062;y:336.984;w:374.737;h:192.015" transform="translate(1.062255859375, 336.984375)">
                        <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:374.737;h:192.015">
                            <path id="Subtract_2" fill="#e8f9ff" d="M374.737 48.0154 C334.692 48.0154 296.356 31.7881 268.481 3.0378 L265.535 0 L187.791 80.2073 C177.996 90.3119 164.525 96.0154 150.452 96.0154 L0 96.0154 L0 96.0852 L30.9377 144.015 L0 191.946 L0 192.015 L150.452 192.015 C190.505 192.015 228.847 175.782 256.723 147.023 L337.368 63.8235 C347.162 53.7189 360.634 48.0154 374.706 48.0154 L374.737 48.0154 Z"/>
                            <path id="Subtract_3" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 374.737 48.0154 C 334.6919 48.0154 296.356 31.7881 268.4807 3.0378 L 265.5354 0 L 187.7908 80.2073 C 177.9965 90.3119 164.5249 96.0154 150.4525 96.0154 L 0 96.0154 L 0 96.0852 L 30.9377 144.0154 L 0 191.9456 L 0 192.0154 L 150.4525 192.0154 C 190.5047 192.0154 228.8468 175.7823 256.7231 147.023 L 337.368 63.8235 C 347.1623 53.7189 360.6339 48.0154 374.7063 48.0154 L 374.737 48.0154 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:289;w:265.598;h:96" transform="translate(1, 289)">
                        <g id="cu_Subtract_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:265.598;h:96">
                            <path id="Subtract_4" fill="#e7fbf2" d="M0 95.9997 L31 48 L0 0.0003 L219.074 0 L265.598 47.9846 L219.057 96 L0 95.9997 Z"/>
                            <path id="Subtract_5" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9997 L 31 48 L 0 0.0003 L 219.0735 0 L 265.5977 47.9846 L 219.0566 96 L 0 95.9997 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1.006;y:145;w:462.933;h:240" transform="translate(1.00634765625, 145)">
                        <g id="cu_Subtract_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:462.933;h:240">
                            <path id="Subtract_6" fill="#f2fae1" d="M0 95.9926 L30.9937 48 L0 0.0074 L0 0 L150.517 0 C190.562 0 228.898 16.2274 256.773 44.9777 L337.46 128.197 C347.254 138.298 360.723 144 374.793 144 L416.4 144 L462.933 192.003 L416.404 240 L374.793 240 C334.748 240 296.412 223.773 268.537 195.022 L187.85 111.803 C178.056 101.702 164.587 96 150.517 96 L0 96 L0 95.9926 Z"/>
                            <path id="Subtract_7" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9926 L 30.9937 48 L 0 0.0074 L 0 0 L 150.5168 0 C 190.5619 0 228.8979 16.2274 256.7732 44.9777 L 337.4596 128.1971 C 347.2536 138.2985 360.723 144 374.7929 144 L 416.3996 144 L 462.9332 192.0029 L 416.4043 240 L 374.7929 240 C 334.7478 240 296.4119 223.7727 268.5366 195.0223 L 187.8501 111.803 C 178.0561 101.7015 164.5867 96 150.5168 96 L 0 96 L 0 95.9926 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:784;h:386">
                        <g id="cu_Subtract_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1.062;y:1;w:780.938;h:384">
                            <path id="Subtract_8" transform="translate(1.062255859375, 1)" fill="#fefbdb" d="M0 95.9302 L30.9377 48 L0 0.0698 L0 0 L155.288 0 C229.164 0 299.886 29.9398 351.305 82.9832 L543.162 280.898 C547.563 285.438 553.615 288 559.938 288 L749.938 288 L780.938 336 L749.938 384 L559.938 384 C527.637 384 496.715 370.909 474.233 347.717 L282.376 149.802 C249.038 115.412 203.186 96 155.288 96 L0 96 L0 95.9302 Z"/>
                            <path id="Subtract_9" transform="translate(1.062255859375, 1)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9302 L 30.9377 48 L 0 0.0698 L 0 0 L 155.2884 0 C 229.1639 0 299.8856 29.9398 351.3054 82.9832 L 543.162 280.898 C 547.5626 285.4377 553.6152 288 559.9377 288 L 749.9375 288 L 780.9377 336 L 749.9376 383.9999 L 559.9377 384 C 527.637 384 496.7153 370.9094 474.233 347.7171 L 282.3763 149.8023 C 249.0383 115.4115 203.1857 96 155.2884 96 L 0 96 L 0 95.9302 Z"/>
                        </g>
                    </g>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:60;h:48" transform="translate(12, 48)">
                    <path id="rect" transform="translate(0, -5.7734375)" fill="#ff00001a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60"/>
                    <text id="1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:0.227;w:48;h:48" fill="#d1bd08" transform="translate(6, 0.2265625)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:48.227;w:144;h:48" transform="translate(72, 48.2265625)">
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="ic-cc-end" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:925;y:336.227;w:48;h:48" fill="#33de7b1a" transform="translate(925, 336.2265625)">
                    <g id="icon" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12.800;y:12.801;w:22.800;h:22.800" transform="translate(12.800048828125, 12.80078125)">
                        <path id="icon_1" transform="translate(0, -3.19921875)" fill="none" stroke="#484848" stroke-width="1.600000023841858" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 7.7428 4.5342 15.4667 9.0991 23.2 13.6487 C 18.7507 16.1148 11.5676 20.0508 6.3718 22.893 C 2.6858 24.9093 0 26.3751 0 26.3751 L 0.0002 0 Z M 23.2 13.6487 C 18.4396 18.6589 13.7669 23.7498 9.0506 28.8 C 8.1597 26.8307 7.3001 24.8457 6.3718 22.893" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-3.199;w:23.200;h:28.800"/>
                    </g>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:192;w:60;h:48" transform="translate(12, 192)">
                    <path id="rect_1" transform="translate(0, -5.76953125)" fill="#ff00001a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.770;w:60;h:60"/>
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.770;w:60;h:60" fill="#93c332" transform="translate(0, -5.76953125)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:192.227;w:144;h:48" transform="translate(72, 192.2265625)">
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:336;w:60;h:48" transform="translate(12, 336)">
                    <path id="rect_2" transform="translate(0, -5.7734375)" fill="#ff00001a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60"/>
                    <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60" fill="#3cc583" transform="translate(0, -5.7734375)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:336.227;w:144;h:48" transform="translate(72, 336.2265625)">
                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:480;w:60;h:48" transform="translate(12, 480)">
                    <path id="rect_3" transform="translate(0, -5.7734375)" fill="#ff00001a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60"/>
                    <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60" fill="#17aee1" transform="translate(0, -5.7734375)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:480.227;w:144;h:48" transform="translate(72, 480.2265625)">
                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-5-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:624;w:60;h:48" transform="translate(12, 624)">
                    <path id="rect_4" transform="translate(0, -5.7734375)" fill="#ff00001a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60"/>
                    <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-5.773;w:60;h:60" fill="#4987ec" transform="translate(0, -5.7734375)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:72;y:624.227;w:144;h:48" transform="translate(72, 624.2265625)">
                    <g id="tx-lc-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12.000;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12.000001907348633)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-rc-end" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:768;y:348.227;w:144;h:24" fill="#ff00001a" transform="translate(768, 348.2265625)">
                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                </g>
                <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:60.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 60.2265625)">
                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:204.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 204.2265625)">
                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:348.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 348.2265625)">
                    <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:492.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 492.2265625)">
                    <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:636.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 636.2265625)">
                    <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
            </g>
        </g>
    </g>
</svg>