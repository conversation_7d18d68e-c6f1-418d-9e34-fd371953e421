<svg xmlns="http://www.w3.org/2000/svg" width="947" height="756">
    <g id="arrows-ring-v1--family--9" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L947 0 L947 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:947;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:947;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:947;h:708">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:199;y:65;w:548.766;h:542.481" transform="translate(199, 65)">
                    <g id="g-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:112.242;y:0;w:181.183;h:132.929" transform="translate(112.2415771484375, 0)">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:181.183;h:132.929">
                            <path id="Vector" fill="#e7fbf2" d="M 169.1835 108 L 181.1835 54 L 169.1835 0 L 145.1835 6.1943e-7 L 147.9462 12.4324 L 157.1835 54 L 147.7043 96.6563 L 145.1835 108 L 169.1835 108 Z M 147.7043 96.6563 L 157.1835 54 L 147.9462 12.4324 C 92.255 15.6016 41.1839 36.033 0 68.4596 L 33.9521 94.4848 L 53.9403 132.929 C 80.389 112.7032 112.6107 99.6453 147.7043 96.6563 Z"/>
                            <path id="Vector_1" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 169.1835 108 L 181.1835 54 L 169.1835 0 L 145.1835 6.1943e-7 L 147.9462 12.4324 L 157.1835 54 L 147.7043 96.6563 L 145.1835 108 L 169.1835 108 Z M 147.7043 96.6563 L 157.1835 54 L 147.9462 12.4324 C 92.255 15.6016 41.1839 36.033 0 68.4596 L 33.9521 94.4848 L 53.9403 132.929 C 80.389 112.7032 112.6107 99.6453 147.7043 96.6563 Z"/>
                        </g>
                    </g>
                    <g id="g-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:16.950;y:60.832;w:154.762;h:176.007" transform="translate(16.9498291015625, 60.83203125)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:154.762;h:176.007">
                            <path id="Vector_2" fill="#e8f9ff" d="M 154.7618 82.7328 L 129.2438 33.6529 L 85.3408 0 L 66.9557 15.4269 L 77.1781 23.2627 L 110.8588 49.0798 L 131.0912 87.9938 L 136.3768 98.1597 L 154.7618 82.7328 Z M 0 161.1811 L 42.4811 159.3049 L 82.7472 176.0074 C 90.2765 142.0706 107.3856 111.7383 131.0912 87.9938 L 110.8588 49.0798 L 77.1781 23.2627 C 38.7156 59.6118 11.1761 107.3982 0 161.1811 Z"/>
                            <path id="Vector_3" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 154.7618 82.7328 L 129.2438 33.6529 L 85.3408 0 L 66.9557 15.4269 L 77.1781 23.2627 L 110.8588 49.0798 L 131.0912 87.9938 L 136.3768 98.1597 L 154.7618 82.7328 Z M 0 161.1811 L 42.4811 159.3049 L 82.7472 176.0074 C 90.2765 142.0706 107.3856 111.7383 131.0912 87.9938 L 110.8588 49.0798 L 77.1781 23.2627 C 38.7156 59.6118 11.1761 107.3982 0 161.1811 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:220.137;w:115.646;h:180.531" transform="translate(0, 220.13671875)">
                        <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:115.646;h:180.531">
                            <path id="Vector_4" fill="#edf4ff" d="M 110.5268 21.1947 L 59.431 0 L 4.1676 2.4407 L 0 26.0761 L 13.1527 25.4952 L 55.2634 23.6354 L 96.0664 40.5606 L 106.3592 44.8301 L 110.5268 21.1947 Z M 115.6462 138.8336 C 102.727 114.0053 95.425 85.787 95.425 55.8632 C 95.425 50.7088 95.6417 45.6051 96.0664 40.5606 L 55.2634 23.6354 L 13.1527 25.4952 C 12.0115 35.4598 11.425 45.593 11.425 55.8632 C 11.425 100.9524 22.7287 143.4015 42.6567 180.5314 L 73.7345 152.0251 L 115.6462 138.8336 Z"/>
                            <path id="Vector_5" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 110.5268 21.1947 L 59.431 0 L 4.1676 2.4407 L 0 26.0761 L 13.1527 25.4952 L 55.2634 23.6354 L 96.0664 40.5606 L 106.3592 44.8301 L 110.5268 21.1947 Z M 115.6462 138.8336 C 102.727 114.0053 95.425 85.787 95.425 55.8632 C 95.425 50.7088 95.6417 45.6051 96.0664 40.5606 L 55.2634 23.6354 L 13.1527 25.4952 C 12.0115 35.4598 11.425 45.593 11.425 55.8632 C 11.425 100.9524 22.7287 143.4015 42.6567 180.5314 L 73.7345 152.0251 L 115.6462 138.8336 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:32.969;y:355.555;w:173.190;h:165.470" transform="translate(32.9691162109375, 355.5546875)">
                        <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:173.190;h:165.470">
                            <path id="Vector_6" fill="#f3f0ff" d="M 93.5307 0 L 40.7654 16.6077 L 0 54 L 12 74.7846 L 21.9471 65.6606 L 52.7654 37.3923 L 95.2275 24.0275 L 105.5307 20.7846 L 93.5307 0 Z M 143.9871 165.47 L 149.4424 123.8711 L 173.1902 86.6358 C 141.6556 73.4775 114.6653 51.6053 95.2275 24.0275 L 52.7654 37.3923 L 21.9471 65.6606 C 51.3349 110.1959 93.8525 145.3033 143.9871 165.47 Z"/>
                            <path id="Vector_7" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 93.5307 0 L 40.7654 16.6077 L 0 54 L 12 74.7846 L 21.9471 65.6606 L 52.7654 37.3923 L 95.2275 24.0275 L 105.5307 20.7846 L 93.5307 0 Z M 143.9871 165.47 L 149.4424 123.8711 L 173.1902 86.6358 C 141.6556 73.4775 114.6653 51.6053 95.2275 24.0275 L 52.7654 37.3923 L 21.9471 65.6606 C 51.3349 110.1959 93.8525 145.3033 143.9871 165.47 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:175.219;y:432.786;w:181.947;h:109.695" transform="translate(175.21875, 432.7861328125)">
                        <g id="cu_Vector_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:181.947;h:109.695">
                            <path id="Vector_8" fill="#faf0ff" d="M 36.9382 0 L 7.1928 46.6392 L 0 101.4868 L 22.5526 109.6953 L 24.3293 96.1474 L 29.7454 54.8476 L 53.7762 17.1686 L 59.4908 8.2085 L 36.9382 0 Z M 153.6474 15.1479 C 136.7632 20.3902 118.8145 23.2138 100.2063 23.2138 C 84.152 23.2138 68.5886 21.1121 53.7762 17.1686 L 29.7454 54.8476 L 24.3293 96.1474 C 48.3609 103.3463 73.8319 107.2138 100.2063 107.2138 C 128.7363 107.2138 156.2093 102.6882 181.9465 94.3158 L 159.3917 58.9511 L 153.6474 15.1479 Z"/>
                            <path id="Vector_9" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 36.9382 0 L 7.1928 46.6392 L 0 101.4868 L 22.5526 109.6953 L 24.3293 96.1474 L 29.7454 54.8476 L 53.7762 17.1686 L 59.4908 8.2085 L 36.9382 0 Z M 153.6474 15.1479 C 136.7632 20.3902 118.8145 23.2138 100.2063 23.2138 C 84.152 23.2138 68.5886 21.1121 53.7762 17.1686 L 29.7454 54.8476 L 24.3293 96.1474 C 48.3609 103.3463 73.8319 107.2138 100.2063 107.2138 C 128.7363 107.2138 156.2093 102.6882 181.9465 94.3158 L 159.3917 58.9511 L 153.6474 15.1479 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:327.418;y:373.599;w:171.880;h:164.778" transform="translate(327.417724609375, 373.5986328125)">
                        <g id="cu_Vector_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:171.880;h:164.778">
                            <path id="Vector_10" fill="#feecf7" d="M 0 63.2908 L 7.1928 118.1384 L 36.9382 164.7776 L 59.4908 156.5691 L 52.1532 145.0641 L 29.7454 109.9299 L 23.9384 65.6493 L 22.5526 55.0823 L 0 63.2908 Z M 131.698 29.7392 L 99.2761 0 C 80.9287 28.3781 54.8131 51.2642 23.9384 65.6493 L 29.7454 109.9299 L 52.1532 145.0641 C 101.7441 123.7526 143.4953 87.6843 171.8795 42.3862 L 131.698 29.7392 Z"/>
                            <path id="Vector_11" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 63.2908 L 7.1928 118.1384 L 36.9382 164.7776 L 59.4908 156.5691 L 52.1532 145.0641 L 29.7454 109.9299 L 23.9384 65.6493 L 22.5526 55.0823 L 0 63.2908 Z M 131.698 29.7392 L 99.2761 0 C 80.9287 28.3781 54.8131 51.2642 23.9384 65.6493 L 29.7454 109.9299 L 52.1532 145.0641 C 101.7441 123.7526 143.4953 87.6843 171.8795 42.3862 L 131.698 29.7392 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:418.350;y:239.318;w:121.075;h:180.627" transform="translate(418.35028076171875, 239.318359375)">
                        <g id="cu_Vector_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:121.075;h:180.627">
                            <path id="Vector_12" fill="#ffedeb" d="M 0 126.6273 L 40.7654 164.0196 L 93.5308 180.6273 L 105.5308 159.8427 L 92.7303 155.8138 L 52.7654 143.235 L 20.0399 113.2173 L 12 105.8427 L 0 126.6273 Z M 35.7025 14.3451 C 36.6083 21.6635 37.0748 29.1181 37.0748 36.6816 C 37.0748 64.0497 30.9669 89.9912 20.0399 113.2173 L 52.7654 143.235 L 92.7303 155.8138 C 110.8594 120.0235 121.0748 79.5455 121.0748 36.6816 C 121.0748 24.2344 120.2134 11.9884 118.5469 0 L 79.3199 16.2714 L 35.7025 14.3451 Z"/>
                            <path id="Vector_13" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 126.6273 L 40.7654 164.0196 L 93.5308 180.6273 L 105.5308 159.8427 L 92.7303 155.8138 L 52.7654 143.235 L 20.0399 113.2173 L 12 105.8427 L 0 126.6273 Z M 35.7025 14.3451 C 36.6083 21.6635 37.0748 29.1181 37.0748 36.6816 C 37.0748 64.0497 30.9669 89.9912 20.0399 113.2173 L 52.7654 143.235 L 92.7303 155.8138 C 110.8594 120.0235 121.0748 79.5455 121.0748 36.6816 C 121.0748 24.2344 120.2134 11.9884 118.5469 0 L 79.3199 16.2714 L 35.7025 14.3451 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:397.813;y:79.697;w:150.953;h:175.893" transform="translate(397.8131103515625, 79.697265625)">
                        <g id="cu_Vector_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:150.953;h:175.893">
                            <path id="Vector_14" fill="#fef2e6" d="M 44.5938 173.4521 L 99.8572 175.8927 L 150.953 154.698 L 146.7855 131.0627 L 134.7101 136.0715 L 95.6896 152.2574 L 51.6836 150.3138 L 40.4262 149.8167 L 44.5938 173.4521 Z M 54.1407 0 L 34.421 37.9278 L 0 64.3125 C 24.635 87.1659 42.8614 116.8316 51.6836 150.3138 L 95.6896 152.2574 L 134.7101 136.0715 C 122.2454 82.6573 93.5647 35.4758 54.1407 0 Z"/>
                            <path id="Vector_15" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 44.5938 173.4521 L 99.8572 175.8927 L 150.953 154.698 L 146.7855 131.0627 L 134.7101 136.0715 L 95.6896 152.2574 L 51.6836 150.3138 L 40.4262 149.8167 L 44.5938 173.4521 Z M 54.1407 0 L 34.421 37.9278 L 0 64.3125 C 24.635 87.1659 42.8614 116.8316 51.6836 150.3138 L 95.6896 152.2574 L 134.7101 136.0715 C 122.2454 82.6573 93.5647 35.4758 54.1407 0 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:284.047;y:12.141;w:173.705;h:139.137" transform="translate(284.046630859375, 12.140625)">
                        <g id="cu_Vector_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:173.705;h:139.137">
                            <path id="Vector_16" fill="#fefbdb" d="M 104.2843 139.1373 L 148.1874 105.4843 L 173.7054 56.4045 L 155.3203 40.9776 L 149.403 52.3586 L 129.8023 90.0574 L 95.0512 116.6952 L 85.8993 123.7104 L 104.2843 139.1373 Z M 9.3784 41.8594 L 0 84.0623 C 35.2987 85.7268 67.9368 97.5588 95.0512 116.6952 L 129.8023 90.0574 L 149.403 52.3586 C 107.5084 21.0062 55.9894 1.8099 0.0763 0 L 9.3784 41.8594 Z"/>
                            <path id="Vector_17" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 104.2843 139.1373 L 148.1874 105.4843 L 173.7054 56.4045 L 155.3203 40.9776 L 149.403 52.3586 L 129.8023 90.0574 L 95.0512 116.6952 L 85.8993 123.7104 L 104.2843 139.1373 Z M 9.3784 41.8594 L 0 84.0623 C 35.2987 85.7268 67.9368 97.5588 95.0512 116.6952 L 129.8023 90.0574 L 149.403 52.3586 C 107.5084 21.0062 55.9894 1.8099 0.0763 0 L 9.3784 41.8594 Z"/>
                        </g>
                    </g>
                </g>
                <g id="ic-cc-9" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:369;y:110;w:48;h:48" fill="#33de7b1a" transform="translate(369, 110)">
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-8" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:259;y:204;w:48;h:48" fill="#33de7b1a" transform="translate(259, 204)">
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:235;y:360;w:48;h:48" fill="#33de7b1a" transform="translate(235, 360)">
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-6" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:309;y:488;w:48;h:48" fill="#33de7b1a" transform="translate(309, 488)">
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:449;y:540;w:48;h:48" fill="#33de7b1a" transform="translate(449, 540)">
                    <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:595;y:486;w:48;h:48" fill="#33de7b1a" transform="translate(595, 486)">
                    <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:668;y:360;w:48;h:48" fill="#33de7b1a" transform="translate(668, 360)">
                    <g id="icon_12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_13" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:647;y:216;w:48;h:48" fill="#33de7b1a" transform="translate(647, 216)">
                    <g id="icon_14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_15" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:533;y:110;w:48;h:48" fill="#33de7b1a" transform="translate(533, 110)">
                    <g id="icon_16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_17" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:768;y:348;w:168;h:60" transform="translate(768, 348)">
                    <g id="tx-lc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:540;y:12;w:168;h:60" transform="translate(540, 12)">
                    <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:108;y:528;w:168;h:60" transform="translate(108, 528)">
                    <g id="tx-rt-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-lc-remove-6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:12;y:348;w:168;h:60" transform="translate(12, 348)">
                    <g id="tx-rc-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-7-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-7" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:720;y:144;w:168;h:60" transform="translate(720, 144)">
                    <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-2" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:60;y:144;w:168;h:60" transform="translate(60, 144)">
                    <g id="tx-rc-8" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-8-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-8" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:684;y:528;w:168;h:60" transform="translate(684, 528)">
                    <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-4" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:240;y:12;w:168;h:60" transform="translate(240, 12)">
                    <g id="tx-rc-9" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-9-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-9" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <path id="bt-cc-add-1" transform="translate(491, 107)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:491;y:107;w:24;h:24"/>
                <path id="bt-cc-add-2" transform="translate(608, 163)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:608;y:163;w:24;h:24"/>
                <path id="bt-cc-add-3" transform="translate(683, 296)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:683;y:296;w:24;h:24"/>
                <path id="bt-cc-add-4" transform="translate(654, 446)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:654;y:446;w:24;h:24"/>
                <path id="bt-cc-add-5" transform="translate(534, 540)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:534;y:540;w:24;h:24"/>
                <path id="bt-cc-add-6" transform="translate(383, 536)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:383;y:536;w:24;h:24"/>
                <path id="bt-cc-add-7" transform="translate(266, 436)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:266;y:436;w:24;h:24"/>
                <path id="bt-cc-add-8" transform="translate(245, 285)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:245;y:285;w:24;h:24"/>
                <path id="bt-cc-add-9" transform="translate(325, 156)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:325;y:156;w:24;h:24"/>
                <path id="bt-cc-add-10" transform="translate(467, 107)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:467;y:107;w:24;h:24"/>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:390;y:636;w:168;h:60" transform="translate(390, 636)">
                    <g id="tx-ct-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <g id="tx-ct-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <rect id="bt-lc-remove-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -24)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>