<svg xmlns="http://www.w3.org/2000/svg" width="744" height="618">    <g id="pillars-dna-v2--family--7">        <g id="lines">            <g id="g-back">                <g id="cu">                    <g id="cu_1" >                        <path id="Subtract" transform="translate(293.3162636309862, 532.4387573897839)" fill="#f0f0f0" d="M5.367 2.5054 C6.2296 2.9734 7.2082 3.2411 8.235 3.2411 L9.3924 3.2411 C14.4278 9.6542 20.5416 15.6699 26.9982 20.8036 L19.2645 21.0823 C15.9347 21.2154 13.3642 24.03 13.3642 27.3637 L13.3642 28.7744 C13.4974 32.1014 16.321 34.6698 19.6507 34.5434 L45.7586 33.6278 C51.2842 36.8312 57.1459 39.8115 63.2375 42.6925 L53.809 66.7459 C36.208 58.565 18.0818 47.6126 4.8023 33.0397 C-3.1693 22.5734 -0.2846 10.0662 6.6668 0.0208 C6.7162 0.0929 6.617 -0.0512 6.6668 0.0208 C6.6169 0.0928 6.5673 0.1648 6.5178 0.237 C6.0011 0.9906 5.835 1.7306 5.367 2.5054 Z"></path>
                        <path id="Subtract_1" transform="translate(293.3162636309862, 532.4387573897839)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 5.367 2.5054 C 6.2296 2.9734 7.2082 3.2411 8.235 3.2411 L 9.3924 3.2411 C 14.4278 9.6542 20.5416 15.6699 26.9982 20.8036 L 19.2645 21.0823 C 15.9347 21.2154 13.3642 24.03 13.3642 27.3637 L 13.3642 28.7744 C 13.4974 32.1014 16.321 34.6698 19.6507 34.5434 L 45.7586 33.6278 C 51.2842 36.8312 57.1459 39.8115 63.2375 42.6925 L 53.809 66.7459 C 36.208 58.565 18.0818 47.6126 4.8023 33.0397 C -3.1693 22.5734 -0.2846 10.0662 6.6668 0.0208 C 6.617 -0.0512 6.7162 0.0929 6.6668 0.0208 C 6.6169 0.0928 6.5673 0.1648 6.5178 0.237 C 6.0011 0.9906 5.835 1.7306 5.367 2.5054 Z"></path></g>
                    <g id="cu_2" >                        <path id="Subtract_2" transform="translate(293.1674542427063, 407.80032965540886)" fill="#f0f0f0" d="M8.0562 0.8478 C6.95 0.8478 5.8997 0.5371 4.9902 0 C-0.7853 9.7493 -2.4725 21.0035 4.9511 30.6105 C16.6215 43.4175 31.8584 53.188 47.3692 60.9788 C52.5573 58.3729 57.7563 56.0154 62.8297 53.8846 C66.9108 52.2219 72.2727 50.007 78.3712 47.3098 L78.8618 47.0947 C78.6988 47.0228 78.5353 46.9506 78.3712 46.8781 C66.8174 41.8155 55.911 36.9965 45.9251 31.2088 L19.4737 32.125 C16.144 32.2514 13.3204 29.683 13.1872 26.356 L13.1872 24.9453 C13.1872 21.6116 15.7577 18.797 19.0875 18.6639 L27.1601 18.3848 C20.7028 13.2517 14.2597 7.2611 9.2222 0.8478 L8.0562 0.8478 Z"></path>
                        <path id="Subtract_3" transform="translate(293.1674542427063, 407.80032965540886)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 8.0562 0.8478 C 6.95 0.8478 5.8997 0.5371 4.9902 0 C -0.7853 9.7493 -2.4725 21.0035 4.9511 30.6105 C 16.6215 43.4175 31.8584 53.188 47.3692 60.9788 C 52.5573 58.3729 57.7563 56.0154 62.8297 53.8846 C 66.9108 52.2219 72.2727 50.007 78.3712 47.3098 L 78.8618 47.0947 C 78.6988 47.0228 78.5353 46.9506 78.3712 46.8781 C 66.8174 41.8155 55.911 36.9965 45.9251 31.2088 L 19.4737 32.125 C 16.144 32.2514 13.3204 29.683 13.1872 26.356 L 13.1872 24.9453 C 13.1872 21.6116 15.7577 18.797 19.0875 18.6639 L 27.1601 18.3848 C 20.7028 13.2517 14.2597 7.2611 9.2222 0.8478 L 8.0562 0.8478 Z"></path></g>
                    <g id="cu_3" >                        <path id="Subtract_4" transform="translate(293.2895984053612, 278.1656250655651)" fill="#f0f0f0" d="M8.3868 3.4847 C7.2996 3.4847 6.2663 3.1846 5.3676 2.6643 C5.8801 1.8032 6.0938 0.9539 6.6666 0.1186 L6.7074 0.0592 L6.6667 0 C-0.4022 10.3098 -3.1488 22.753 4.9511 33.2352 C16.4797 45.8867 31.434 55.7257 46.7535 63.4753 C52.129 60.756 57.5215 58.3043 62.7792 56.0961 C66.8252 54.4477 72.1302 52.2565 78.1638 49.5907 C66.6742 44.5544 55.8804 39.6017 45.9449 33.8451 L19.6505 34.7619 C16.3208 34.8884 13.4972 32.3199 13.364 28.9929 L13.364 27.5822 C13.364 24.2486 15.9346 21.4339 19.2643 21.3009 L27.1748 21.0212 C20.7167 15.8888 14.2716 9.8983 9.2317 3.4847 L8.3868 3.4847 Z M78.4141 77.2439 L78.569 77.3123 C87.2205 81.1032 95.608 84.8922 103.469 88.9866 L137.996 90.2322 C141.206 90.3653 143.636 93.0535 143.636 96.2608 L143.636 97.5383 C143.51 100.746 140.946 103.174 137.736 103.048 L125.353 102.555 C130.915 106.663 136.931 111.633 142.032 116.903 L149.06 116.903 C151.305 116.903 153.319 118.184 154.394 120.041 C158.201 111.406 158.497 102.014 152.166 93.8202 C140.569 81.0938 125.276 71.1285 109.864 63.3589 C104.578 66.0233 99.2792 68.4296 94.1104 70.6004 C90.0293 72.2631 84.6703 74.4775 78.5718 77.1747 L78.4141 77.2439 Z"></path>
                        <path id="Subtract_5" transform="translate(293.2895984053612, 278.1656250655651)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 8.3868 3.4847 C 7.2996 3.4847 6.2663 3.1846 5.3676 2.6643 C 5.8801 1.8032 6.0938 0.9539 6.6666 0.1186 L 6.7073 0.0592 L 6.6667 0 C -0.4022 10.3098 -3.1488 22.753 4.9511 33.2352 C 16.4797 45.8867 31.434 55.7257 46.7535 63.4753 C 52.129 60.756 57.5215 58.3043 62.7792 56.0961 C 66.8252 54.4477 72.1302 52.2565 78.1638 49.5907 C 66.6742 44.5544 55.8804 39.6017 45.9449 33.8451 L 19.6505 34.7619 C 16.3208 34.8884 13.4972 32.3199 13.364 28.9929 L 13.364 27.5822 C 13.364 24.2486 15.9346 21.4339 19.2643 21.3009 L 27.1748 21.0212 C 20.7167 15.8888 14.2716 9.8983 9.2317 3.4847 L 8.3868 3.4847 Z M 78.4141 77.2439 L 78.569 77.3123 C 87.2205 81.1032 95.608 84.8922 103.4686 88.9866 L 137.9958 90.2322 C 141.2057 90.3653 143.6364 93.0535 143.6364 96.2608 L 143.6364 97.5383 C 143.5098 100.7456 140.9459 103.1743 137.7361 103.0479 L 125.3532 102.555 C 130.9148 106.6635 136.9309 111.633 142.0321 116.9026 L 149.0603 116.9026 C 151.3047 116.9026 153.3191 118.1844 154.3939 120.0409 C 158.2013 111.4062 158.4974 102.0141 152.1658 93.8202 C 140.5688 81.0938 125.2756 71.1285 109.8637 63.3589 C 104.5784 66.0233 99.2792 68.4296 94.1104 70.6004 C 90.0293 72.2631 84.6703 74.4775 78.5718 77.1747 L 78.4141 77.2439 Z"></path></g>
                    <g id="cu_4" >                        <path id="Subtract_6" transform="translate(293.4388809502125, 150.96562811732292)" fill="#f0f0f0" d="M4.964 33.2036 C-3.0929 22.7771 -0.4629 10.2634 6.5829 0 C6.0353 0.8172 5.2797 2.0098 4.7901 2.8507 C6.3068 3.7253 7.2739 3.7253 8.3774 3.7253 L9.5621 3.7253 C14.6043 10.1391 20.7586 16.1327 27.2176 21.2645 L19.1161 21.5447 C15.7863 21.6778 13.2158 24.4924 13.2158 27.8261 L13.2158 29.2367 C13.349 32.5637 16.1726 35.1322 19.5023 35.0058 L45.9927 34.0882 C55.9684 39.8664 67.0377 44.5226 78.5765 49.5786 L78.9083 49.7252 L78.5765 49.8706 C72.478 52.5678 67.1161 54.7828 63.035 56.4455 C57.9163 58.5952 52.6698 60.9759 47.4356 63.6095 C31.9717 55.8287 16.5996 45.9724 4.964 33.2036 Z M125.536 102.7 L137.918 103.202 C141.127 103.328 143.741 100.93 143.867 97.7226 L143.867 96.445 C143.867 93.2378 141.437 90.5496 138.227 90.4165 L103.497 89.0343 C95.7305 84.988 87.4495 81.2401 78.9075 77.4943 C84.8689 74.8632 90.108 72.6988 94.1151 71.0662 C99.4227 68.8371 104.868 66.3598 110.294 63.6095 C125.561 71.3477 140.431 81.1924 151.922 93.8017 C160.021 104.284 157.147 116.789 150.078 127.099 C150.073 127.091 150.067 127.083 150.061 127.075 C150.067 127.066 150.073 127.058 150.078 127.05 C151.614 124.81 152.946 122.469 154.007 120.07 C152.928 118.227 151.299 116.949 149.065 116.949 L142.048 116.949 C136.949 111.681 131.097 106.808 125.536 102.7 Z"></path>
                        <path id="Subtract_7" transform="translate(293.4388809502125, 150.96562811732292)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 4.964 33.2036 C -3.0928 22.7771 -0.4629 10.2634 6.5829 0 C 6.0353 0.8172 5.2797 2.0098 4.7901 2.8507 C 6.3068 3.7253 7.2739 3.7253 8.3773 3.7253 L 9.5621 3.7253 C 14.6043 10.1391 20.7586 16.1327 27.2176 21.2645 L 19.1161 21.5447 C 15.7863 21.6778 13.2158 24.4924 13.2158 27.8261 L 13.2158 29.2367 C 13.349 32.5637 16.1726 35.1322 19.5023 35.0058 L 45.9927 34.0882 C 55.9684 39.8664 67.0377 44.5226 78.5765 49.5786 L 78.9083 49.7252 L 78.5765 49.8706 C 72.478 52.5678 67.1161 54.7828 63.035 56.4455 C 57.9163 58.5952 52.6698 60.9759 47.4356 63.6095 C 31.9717 55.8287 16.5996 45.9724 4.964 33.2036 Z M 125.5361 102.6995 L 137.9176 103.2015 C 141.1275 103.3279 143.7409 100.9298 143.8675 97.7226 L 143.8675 96.445 C 143.8675 93.2378 141.4368 90.5496 138.2269 90.4165 L 103.4967 89.0343 C 95.7305 84.988 87.4495 81.2401 78.9075 77.4943 C 84.8689 74.8632 90.108 72.6988 94.1151 71.0662 C 99.4227 68.8371 104.8677 66.3598 110.2939 63.6095 C 125.5615 71.3477 140.4314 81.1924 151.9216 93.8017 C 160.0215 104.2839 157.1471 116.7894 150.0782 127.0993 C 150.0726 127.091 150.067 127.0828 150.0613 127.0746 C 150.067 127.0663 150.0726 127.0581 150.0783 127.0499 C 151.6141 124.8099 152.9459 122.4692 154.0068 120.0698 C 152.9283 118.2274 151.2989 116.9494 149.065 116.9494 L 142.0477 116.9494 C 136.9487 111.6806 131.0968 106.8083 125.5361 102.6995 Z"></path></g>
                    <g id="cu_5" >                        <path id="Subtract_8" transform="translate(387.1746563911438, 84.16562506556511)" fill="#f0f0f0" d="M9.7873 28.8982 L44.4911 30.2793 C47.7009 30.4124 50.1316 33.1006 50.1316 36.3078 L50.1316 37.5854 C50.0051 40.7926 47.4412 43.2214 44.2314 43.0949 L31.8829 42.6035 C37.4425 46.7126 43.228 51.5442 48.3245 56.8122 L55.3292 56.8122 C57.5697 56.8122 59.7541 58.2291 60.8303 60.0804 C64.6437 51.6721 65.2063 41.826 58.4353 33.3864 C45.1558 18.8136 27.0296 8.181 9.4285 0 L0 24.0534 C3.3373 25.6318 6.6055 27.2399 9.7873 28.8982 Z"></path>
                        <path id="Subtract_9" transform="translate(387.1746563911438, 84.16562506556511)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 9.7873 28.8982 L 44.4911 30.2793 C 47.7009 30.4124 50.1316 33.1006 50.1316 36.3078 L 50.1316 37.5854 C 50.0051 40.7926 47.4412 43.2214 44.2314 43.0949 L 31.8829 42.6035 C 37.4425 46.7126 43.228 51.5442 48.3245 56.8122 L 55.3292 56.8122 C 57.5697 56.8122 59.7541 58.2291 60.8303 60.0804 C 64.6437 51.6721 65.2063 41.826 58.4353 33.3864 C 45.1558 18.8136 27.0296 8.181 9.4285 0 L 0 24.0534 C 3.3373 25.6318 6.6055 27.2399 9.7873 28.8982 Z"></path></g></g></g>
            <g id="g-7">                <g id="cu_6" >                    <path id="Subtract_10" transform="translate(298.35845375061035, 492.2830810546875)" fill="#e7fbf2" d="M0 42.6556 C0.8626 43.1236 2.1902 43.3912 3.217 43.3912 L73.6553 43.4027 L73.6553 29.6887 L10.3227 29.7149 C6.9836 33.1144 4.0137 36.6644 1.4784 40.362 C0.9616 41.1157 0.468 41.8808 0 42.6556 Z M31.8621 11.9913 L73.6553 13.6077 L73.6553 0.7818 L52.7261 0 C47.8326 2.4233 43.1991 4.9377 38.7164 7.6118 C36.6006 8.8775 34.3018 10.3467 31.8621 11.9913 Z M8.3224 67.5264 C8.3224 64.1927 10.8929 61.3781 14.2226 61.245 L73.6553 59.1627 L73.6553 72.6338 L14.6089 74.7061 C11.2791 74.8325 8.4555 72.2641 8.3224 68.937 L8.3224 67.5264 Z"></path>
                    <path id="Subtract_11" transform="translate(298.35845375061035, 492.2830810546875)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 0 42.6556 C 0.8626 43.1236 2.1902 43.3912 3.217 43.3912 L 73.6553 43.4027 L 73.6553 29.6887 L 10.3227 29.7149 C 6.9836 33.1144 4.0137 36.6644 1.4784 40.362 C 0.9616 41.1157 0.468 41.8808 0 42.6556 Z M 31.8621 11.9913 L 73.6553 13.6077 L 73.6553 0.7818 L 52.7261 0 C 47.8326 2.4233 43.1991 4.9377 38.7164 7.6118 C 36.6006 8.8775 34.3018 10.3467 31.8621 11.9913 Z M 8.3223 67.5264 C 8.3223 64.1927 10.8929 61.3781 14.2226 61.245 L 73.6553 59.1627 L 73.6553 72.6338 L 14.6089 74.7061 C 11.2791 74.8325 8.4555 72.2641 8.3223 68.937 L 8.3223 67.5264 Z"></path></g></g>
            <g id="g-6">                <g id="cu_7" >                    <path id="Subtract_12" transform="translate(371.7139587402344, 366.01346588134766)" fill="#fefbdb" d="M59.652 2.374 C62.8619 2.5071 65.2926 5.1953 65.2926 8.4026 L65.2926 9.6801 C65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L4.8775e-6 12.8259 L4.8775e-6 0 L59.652 2.374 Z M69.4963 42.6209 C70.2482 41.6454 71.0171 40.6605 71.6979 39.6676 C73.3331 37.2827 74.8039 34.8334 75.9614 32.3147 C74.931 30.3139 72.8416 28.9069 70.4902 28.9069 L4.8775e-6 28.9069 L4.8775e-6 42.6209 L69.4963 42.6209 Z M55.9585 56.5057 L4.8775e-6 58.3809 L0 71.852 L36.8814 70.5535 C42.6472 66.9027 49.5374 62.1226 55.9585 56.5057 Z"></path>
                    <path id="Subtract_13" transform="translate(371.7139587402344, 366.01346588134766)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 59.652 2.374 C 62.8619 2.5071 65.2926 5.1953 65.2926 8.4025 L 65.2926 9.6801 C 65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L 0 12.8259 L 0 0 L 59.652 2.374 Z M 69.4963 42.6209 C 70.2482 41.6454 71.0171 40.6605 71.6979 39.6676 C 73.3331 37.2827 74.8039 34.8334 75.9614 32.3147 C 74.931 30.3139 72.8416 28.9069 70.4902 28.9069 L 0 28.9069 L 0 42.6209 L 69.4963 42.6209 Z M 55.9585 56.5057 L 0 58.3809 L 0 71.852 L 36.8814 70.5535 C 42.6472 66.9027 49.5374 62.1226 55.9585 56.5057 Z"></path></g></g>
            <g id="g-5">                <g id="cu_8" >                    <path id="Subtract_14" transform="translate(298.1939392089844, 365.1650390625)" fill="#faf0ff" d="M0 42.5716 C0.9317 43.1449 2.0179 43.4784 3.164 43.4784 L73.6541 43.4784 L73.6541 29.7645 L10.0605 29.7645 C6.9742 32.9646 4.1441 36.3246 1.7744 39.7808 C1.1474 40.6952 0.5544 41.6264 0 42.5716 Z M31.4468 12.0036 L73.6541 13.6834 L73.6541 0.8575 L52.1063 0 C47.6559 2.2322 43.3569 4.5455 39.2547 6.9926 C36.8717 8.4182 34.2262 10.1019 31.4468 12.0036 Z M8.1615 67.5759 C8.1615 64.2423 10.7321 61.4276 14.0618 61.2945 L73.6986 59.2384 L73.6986 72.7095 L14.448 74.7556 C11.1183 74.882 8.2947 72.3136 8.1615 68.9866 L8.1615 67.5759 Z"></path>
                    <path id="Subtract_15" transform="translate(298.1939392089844, 365.1650390625)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 0 42.5716 C 0.9317 43.1449 2.0179 43.4784 3.164 43.4784 L 73.6541 43.4784 L 73.6541 29.7645 L 10.0605 29.7645 C 6.9742 32.9646 4.1441 36.3246 1.7744 39.7808 C 1.1474 40.6952 0.5544 41.6264 0 42.5716 Z M 31.4468 12.0036 L 73.6541 13.6834 L 73.6541 0.8575 L 52.1063 0 C 47.6559 2.2322 43.3569 4.5455 39.2547 6.9926 C 36.8717 8.4182 34.2262 10.1019 31.4468 12.0036 Z M 8.1615 67.5759 C 8.1615 64.2423 10.7321 61.4276 14.0618 61.2945 L 73.6986 59.2384 L 73.6986 72.7095 L 14.448 74.7556 C 11.1183 74.882 8.2947 72.3136 8.1615 68.9866 L 8.1615 67.5759 Z"></path></g></g>
            <g id="g-4">                <g id="cu_9" >                    <path id="Subtract_16" transform="translate(372.0139465332031, 238.96580505371094)" fill="#e8f9ff" d="M59.652 2.374 C62.8619 2.5071 65.2926 5.1953 65.2926 8.4026 L65.2926 9.6801 C65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L4.8775e-6 12.8259 L4.8775e-6 0 L59.652 2.374 Z M68.8627 42.6209 C69.8051 41.4275 70.6989 40.2193 71.5354 38.9991 C73.0713 36.7592 74.5356 34.1467 75.5964 31.7473 C74.6143 30.2268 72.6713 28.9458 70.4375 28.9458 L4.8775e-6 28.9069 L4.8775e-6 42.6209 L68.8627 42.6209 Z M55.3074 56.4553 L4.8775e-6 58.3809 L0 71.852 L35.9823 70.5926 C41.7414 67.0178 48.6982 62.1444 55.3074 56.4553 Z"></path>
                    <path id="Subtract_17" transform="translate(372.0139465332031, 238.96580505371094)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 59.652 2.374 C 62.8619 2.5071 65.2926 5.1953 65.2926 8.4025 L 65.2926 9.6801 C 65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L 0 12.8259 L 0 0 L 59.652 2.374 Z M 68.8627 42.6209 C 69.8051 41.4275 70.6989 40.2193 71.5354 38.9991 C 73.0713 36.7592 74.5356 34.1467 75.5964 31.7473 C 74.6142 30.2268 72.6713 28.9458 70.4375 28.9458 L 0 28.9069 L 0 42.6209 L 68.8627 42.6209 Z M 55.3074 56.4553 L 0 58.3809 L 0 71.852 L 35.9823 70.5926 C 41.7414 67.0178 48.6982 62.1444 55.3074 56.4553 Z"></path></g></g>
            <g id="g-3">                <g id="cu_10" >                    <path id="Subtract_18" transform="translate(298.34242820739746, 238.16551208496094)" fill="#fef2e6" d="M-2.3843e-5 42.6336 C0.8986 43.1539 2.1034 43.454 3.1907 43.454 L73.6808 43.454 L73.6808 29.74 L10.2842 29.74 C7.0731 33.0393 4.0748 36.5121 1.6232 40.0879 C1.0504 40.9232 0.5125 41.7724 -2.3843e-5 42.6336 Z M31.9331 11.9974 L73.6808 13.659 L73.6808 0.8331 L52.7473 0 C48.0779 2.3271 43.5721 4.7402 39.2814 7.2997 C37.0301 8.6465 34.5445 10.2237 31.9331 11.9974 Z M8.3217 67.5515 C8.3217 64.2178 10.8922 61.4032 14.222 61.2701 L73.6808 59.214 L73.6808 72.6851 L14.6082 74.7312 C11.2785 74.8576 8.4549 72.2892 8.3217 68.9621 L8.3217 67.5515 Z"></path>
                    <path id="Subtract_19" transform="translate(298.34242820739746, 238.16551208496094)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M -0 42.6336 C 0.8986 43.1539 2.1034 43.454 3.1907 43.454 L 73.6808 43.454 L 73.6808 29.74 L 10.2842 29.74 C 7.0731 33.0393 4.0748 36.5121 1.6231 40.0879 C 1.0504 40.9232 0.5125 41.7724 -0 42.6336 Z M 31.9331 11.9974 L 73.6808 13.659 L 73.6808 0.8331 L 52.7473 0 C 48.0779 2.3271 43.5721 4.7402 39.2814 7.2997 C 37.0301 8.6465 34.5445 10.2237 31.9331 11.9974 Z M 8.3217 67.5515 C 8.3217 64.2178 10.8922 61.4032 14.222 61.2701 L 73.6808 59.214 L 73.6808 72.6851 L 14.6082 74.7312 C 11.2785 74.8576 8.4549 72.2892 8.3217 68.9621 L 8.3217 67.5515 Z"></path></g></g>
            <g id="g-2">                <g id="cu_11" >                    <path id="Subtract_20" transform="translate(372.0137176513672, 112.07073020935059)" fill="#ffedeb" d="M59.652 2.374 C62.8619 2.5071 65.2926 5.1953 65.2926 8.4026 L65.2926 9.6801 C65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L4.8775e-6 12.8259 L4.8775e-6 0 L59.652 2.374 Z M69.467 42.6209 C70.3141 41.5352 71.1207 40.4377 71.88 39.3302 C73.4557 37.0322 74.8166 34.6281 75.8905 32.1635 C74.8338 30.2434 72.7819 28.9069 70.4902 28.9069 L4.8775e-6 28.9069 L4.8775e-6 42.6209 L69.467 42.6209 Z M56.0837 56.4415 L4.8775e-6 58.3809 L0 71.852 L36.9214 70.5732 C42.6853 66.9594 49.5694 62.0937 56.0837 56.4415 Z"></path>
                    <path id="Subtract_21" transform="translate(372.0137176513672, 112.07073020935059)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 59.652 2.374 C 62.8619 2.5071 65.2926 5.1953 65.2926 8.4025 L 65.2926 9.6801 C 65.1661 12.8874 62.6022 15.3161 59.3923 15.1897 L 0 12.8259 L 0 0 L 59.652 2.374 Z M 69.467 42.6209 C 70.3141 41.5352 71.1207 40.4377 71.88 39.3302 C 73.4557 37.0322 74.8166 34.6281 75.8905 32.1635 C 74.8338 30.2434 72.7819 28.9069 70.4902 28.9069 L 0 28.9069 L 0 42.6209 L 69.467 42.6209 Z M 56.0837 56.4415 L 0 58.3809 L 0 71.852 L 36.9214 70.5732 C 42.6853 66.9594 49.5694 62.0937 56.0837 56.4415 Z"></path></g></g>
            <g id="g-1">                <g id="cu_12" >                    <path id="Subtract_22" transform="translate(298.2258234024048, 111.21465682983398)" fill="#f2fae1" d="M10.0608 29.7625 C6.9982 32.9437 4.1971 36.2553 1.843 39.6887 C1.1882 40.6438 0.5755 41.6202 -8.4839e-6 42.6087 C0.9079 43.1433 2.006 43.4765 3.1095 43.4765 L73.7879 43.4765 L73.7879 29.7625 L10.0608 29.7625 Z M31.4923 12.0049 L73.7879 13.6815 L73.7879 0.8556 L52.1184 0 C49.1294 1.5014 46.209 3.0397 43.3708 4.6308 C42.0273 5.3839 40.7022 6.1489 39.3971 6.9275 C36.9864 8.3697 34.3071 10.076 31.4923 12.0049 Z M8.4288 67.574 C8.4288 64.2403 10.9994 61.4257 14.3291 61.2926 L73.7879 59.2365 L73.7879 72.7076 L14.7154 74.7537 C11.3856 74.8801 8.562 72.3117 8.4288 68.9847 L8.4288 67.574 Z"></path>
                    <path id="Subtract_23" transform="translate(298.2258234024048, 111.21465682983398)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 10.0608 29.7625 C 6.9982 32.9437 4.1971 36.2553 1.843 39.6887 C 1.1882 40.6438 0.5755 41.6202 -0 42.6087 C 0.9079 43.1433 2.006 43.4765 3.1095 43.4765 L 73.7879 43.4765 L 73.7879 29.7625 L 10.0608 29.7625 Z M 31.4923 12.0049 L 73.7879 13.6815 L 73.7879 0.8556 L 52.1184 0 C 49.1294 1.5014 46.209 3.0397 43.3708 4.6308 C 42.0273 5.3839 40.7022 6.1489 39.3971 6.9275 C 36.9864 8.3697 34.3071 10.076 31.4923 12.0049 Z M 8.4288 67.574 C 8.4288 64.2403 10.9994 61.4257 14.3291 61.2926 L 73.7879 59.2365 L 73.7879 72.7076 L 14.7154 74.7537 C 11.3856 74.8801 8.562 72.3117 8.4288 68.9847 L 8.4288 67.574 Z"></path></g></g>
            <g id="g-top">                <g id="cu_13">                    <g id="cu_14" >                        <path id="Vector" transform="translate(287.84808349609375, 371.9630126953125)" fill="#f6f6f6" d="M10.4045 127.256 C25.9784 110.165 48.2186 98.0537 68.4599 89.5527 C72.541 87.8899 77.9029 85.675 84.0014 82.9778 C96.2456 77.6127 107.958 72.2519 118.4 66.0234 C129.533 59.3631 146.395 47.069 155.88 33.2352 C162.949 22.9254 165.695 10.4822 157.595 0 C166.381 9.6418 168 18.9129 168 33.2352 C168 47.5576 166.381 56.8287 157.595 66.4705 C142.021 83.5612 119.781 95.6724 99.54 104.173 C95.4589 105.836 90.0999 108.05 84.0014 110.748 C71.7572 116.113 60.0416 121.474 49.6004 127.703 C38.4673 134.363 21.6052 146.657 12.1201 160.491 C5.0512 170.801 2.3046 183.244 10.4045 193.726 C1.6185 184.084 0 174.813 0 160.491 C0 146.168 1.6185 136.897 10.4045 127.256 Z"></path>
                        <path id="Vector_1" transform="translate(287.84808349609375, 371.9630126953125)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 68.4599 89.5527 C 48.2186 98.0537 25.9784 110.1648 10.4045 127.2556 C 1.6185 136.8973 0 146.1685 0 160.4908 C 0 174.8131 1.6185 184.0843 10.4045 193.726 C 2.3046 183.2438 5.0512 170.8006 12.1201 160.4908 C 21.6052 146.657 38.4673 134.3629 49.6004 127.7026 C 60.0416 121.4741 71.7572 116.1128 84.0014 110.7477 C 90.0999 108.0505 95.4589 105.8361 99.54 104.1734 C 119.7813 95.6724 142.0215 83.5612 157.5954 66.4705 C 166.3814 56.8287 167.9999 47.5576 167.9999 33.2352 C 167.9999 18.9129 166.3814 9.6418 157.5954 0 C 165.6953 10.4822 162.9487 22.9254 155.8798 33.2352 C 146.3947 47.069 129.5326 59.3631 118.3995 66.0234 C 107.9583 72.2519 96.2456 77.6127 84.0014 82.9778 C 77.9029 85.675 72.541 87.8899 68.4599 89.5527 Z"></path></g>
                    <g id="cu_15" >                        <path id="Vector_2" transform="translate(287.7139587402344, 244.66561889648438)" fill="#f6f6f6" d="M10.4045 127.256 C25.9784 110.165 48.2186 98.0537 68.4599 89.5527 C72.541 87.8899 77.9029 85.675 84.0014 82.9778 C96.2456 77.6127 107.958 72.2519 118.4 66.0234 C129.533 59.3631 146.395 47.069 155.88 33.2352 C162.949 22.9254 165.695 10.4822 157.595 0 C166.381 9.6418 168 18.9129 168 33.2352 C168 47.5576 166.381 56.8287 157.595 66.4705 C142.021 83.5612 119.781 95.6724 99.54 104.173 C95.4589 105.836 90.0999 108.05 84.0014 110.748 C71.7572 116.113 60.0416 121.474 49.6004 127.703 C38.4673 134.363 21.6052 146.657 12.1201 160.491 C5.0512 170.801 2.3046 183.244 10.4045 193.726 C1.6185 184.084 0 174.813 0 160.491 C0 146.168 1.6185 136.897 10.4045 127.256 Z"></path>
                        <path id="Vector_3" transform="translate(287.7139587402344, 244.66561889648438)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 68.4599 89.5527 C 48.2186 98.0537 25.9784 110.1648 10.4045 127.2556 C 1.6185 136.8973 0 146.1685 0 160.4908 C 0 174.8131 1.6185 184.0843 10.4045 193.726 C 2.3046 183.2438 5.0512 170.8006 12.1201 160.4908 C 21.6052 146.657 38.4673 134.3629 49.6004 127.7026 C 60.0416 121.4741 71.7572 116.1128 84.0014 110.7477 C 90.0999 108.0505 95.4589 105.8361 99.54 104.1734 C 119.7813 95.6724 142.0215 83.5612 157.5954 66.4705 C 166.3814 56.8287 167.9999 47.5576 167.9999 33.2352 C 167.9999 18.9129 166.3814 9.6418 157.5954 0 C 165.6953 10.4822 162.9487 22.9254 155.8798 33.2352 C 146.3947 47.069 129.5326 59.3631 118.3995 66.0234 C 107.9583 72.2519 96.2456 77.6127 84.0014 82.9778 C 77.9029 85.675 72.541 87.8899 68.4599 89.5527 Z"></path></g>
                    <g id="cu_16" >                        <path id="Vector_4" transform="translate(287.74176025390625, 117.87109375)" fill="#f6f6f6" d="M10.4382 127.256 C26.0626 110.165 48.3749 98.0537 68.6817 89.5527 C72.776 87.8899 78.1553 85.675 84.2736 82.9778 C96.5574 77.6127 108.308 72.2519 118.783 66.0234 C129.952 59.3631 146.869 47.069 156.385 33.2352 C163.477 22.9254 166.232 10.4822 158.106 0 C166.921 9.6418 168.544 18.9129 168.544 33.2352 C168.544 47.5576 166.921 56.8287 158.106 66.4705 C142.482 83.5612 120.169 95.6724 99.8625 104.173 C95.7682 105.836 90.3918 108.05 84.2736 110.748 C71.9897 116.113 60.2362 121.474 49.7611 127.703 C38.592 134.363 21.6752 146.657 12.1594 160.491 C5.0676 170.801 2.3642 183.272 10.6699 193.726 C1.8554 184.084 0 174.813 0 160.491 C0 146.168 1.6237 136.897 10.4382 127.256 Z"></path>
                        <path id="Vector_5" transform="translate(287.74176025390625, 117.87109375)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 68.6817 89.5527 C 48.3749 98.0537 26.0626 110.1648 10.4382 127.2556 C 1.6237 136.8973 0 146.1685 0 160.4908 C 0 174.8131 1.8554 184.0843 10.6699 193.726 C 2.3642 183.2719 5.0676 170.8006 12.1594 160.4908 C 21.6752 146.657 38.592 134.3629 49.7611 127.7026 C 60.2362 121.4741 71.9897 116.1128 84.2736 110.7477 C 90.3918 108.0505 95.7682 105.8361 99.8625 104.1734 C 120.1694 95.6724 142.4817 83.5612 158.106 66.4705 C 166.9206 56.8287 168.5443 47.5576 168.5443 33.2352 C 168.5443 18.9129 166.9206 9.6418 158.106 0 C 166.2322 10.4822 163.4767 22.9254 156.3849 33.2352 C 146.8691 47.069 129.9523 59.3631 118.7832 66.0234 C 108.3081 72.2519 96.5574 77.6127 84.2736 82.9778 C 78.1553 85.675 72.776 87.8899 68.6817 89.5527 Z"></path></g>
                    <g id="cu_17" >                        <path id="Vector_6" transform="matrix(-1, -8.735167256190834e-8, 8.749393742846223e-8, -1, 356.8355407714844, 184.16563415527344)" fill="#f6f6f6" d="M56.7859 33.2352 C47.3008 47.069 30.3542 59.3631 19.2212 66.0234 C17.916 66.8019 16.5909 67.567 15.2474 68.3201 C10.3837 71.0467 5.2783 73.6184 0 76.1151 L9.434 100.166 C27.0276 91.9859 45.1435 81.0368 58.4171 66.4704 C67.2031 56.8287 68.8216 47.5576 68.8216 33.2352 C68.8216 18.9129 67.2031 9.6418 58.4171 0 C66.517 10.4822 63.8548 22.9254 56.7859 33.2352 Z"></path>
                        <path id="Vector_7" transform="matrix(-1, -8.735167256190834e-8, 8.749393742846223e-8, -1, 356.8355407714844, 184.16563415527344)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="22.926000595092773"  d="M 56.7859 33.2352 C 47.3008 47.069 30.3542 59.3631 19.2212 66.0234 C 17.916 66.8019 16.5909 67.567 15.2474 68.3201 C 10.3837 71.0467 5.2783 73.6184 0 76.1151 L 9.434 100.1656 C 27.0276 91.9859 45.1435 81.0368 58.4171 66.4704 C 67.2031 56.8287 68.8216 47.5576 68.8216 33.2352 C 68.8216 18.9129 67.2031 9.6418 58.4171 0 C 66.517 10.4822 63.8548 22.9254 56.7859 33.2352 Z"></path></g></g></g></g>
        <path id="tx-rc-7" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 48, 492)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-lc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 540, 360)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-rc-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 48, 360)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-lc-4" transform="translate(540, 228)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-rc-3" transform="translate(48, 228)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 540, 96)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-rc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 48, 96)" fill="#ff00001a" d="M0 0 L156 0 L156 96 L0 96 L0 0 Z"></path>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <g id="ic-cc-7">            <path id="rect" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 216.00000000000009, 516)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon" transform="translate(216.00000000000009, 516)">                <path id="icon_1" transform="translate(8, 4)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-6">            <path id="rect_1" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 480.00000000000006, 384)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_2" transform="translate(480.00000000000006, 384)">                <path id="icon_3" transform="translate(8, 4)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-5">            <path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 216.00000000000009, 384)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_4" transform="translate(216.00000000000009, 384)">                <path id="icon_5" transform="translate(8, 4)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-4">            <path id="rect_3" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 480.00000000000006, 252)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_6" transform="translate(480.00000000000006, 252)">                <path id="icon_7" transform="translate(8, 4)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-3">            <path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 216.00000000000009, 252)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_8" transform="translate(216.00000000000009, 252)">                <path id="icon_9" transform="translate(8, 4)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-2">            <path id="rect_5" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 480.00000000000006, 120)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_10" transform="translate(480.00000000000006, 120)">                <path id="icon_11" transform="translate(8, 4)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <g id="ic-cc-1">            <path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 216.00000000000009, 120)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
            <g id="icon_12" transform="translate(216.00000000000009, 120)">                <path id="icon_13" transform="translate(8, 4)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 10.6797 6.2975 21.3334 12.6377 32 18.9565 C 25.863 22.3817 15.9552 27.8484 8.7887 31.7958 C 3.7046 34.5963 0 36.6321 0 36.6321 L 0.0002 0 Z M 32 18.9565 C 25.434 25.9151 18.9888 32.9858 12.4836 40 C 11.2547 37.2649 10.0692 34.5079 8.7887 31.7958"></path></g></g>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 5.551115123125783e-17, -5.551115123125783e-17, 0.9999999403953552, 264, 528)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 456, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 264, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 456, 264)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 264, 264)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 5.551115123125783e-17, -5.551115123125783e-17, 0.9999999403953552, 456, 132)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 264, 132)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-9" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 114, 594)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-8" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 612, 462)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 114, 462)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 606, 330)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 114, 330)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="translate(606, 198)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 114, 198)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, 1.942890293094024e-16, -1.942890293094024e-16, 1, 606, 66)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 114, 66)" width="24" height="24" rx="0" ry="0"></rect></g></svg>