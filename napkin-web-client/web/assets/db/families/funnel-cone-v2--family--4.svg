<svg xmlns="http://www.w3.org/2000/svg" width="696" height="468">
    <g id="funnel-cone-v2--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L696 0 L696 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:696;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:696;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:72 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:696;h:420">
                <g id="visual" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 24 0;gap:0;primary:MIN;counter:CENTER" data-position="x:8;y:72;w:680;h:144" transform="translate(8, 72)">
                    <g id="visual_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24;w:680;h:96" transform="translate(0, 24)">
                        <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:16.723;w:156.027;h:63.150" transform="translate(1, 16.72265625)">
                            <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156.027;h:63.150">
                                <path id="Vector" fill="#fef2e6" d="M 156.0271 31.5752 C 156.0271 49.0137 150.6423 63.1504 143.9999 63.1504 C 143.675 63.1504 143.3532 63.1166 143.0349 63.0502 C 142.9994 63.0428 142.9641 63.0351 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 C 142.9641 0.1153 142.9995 0.1075 143.0349 0.1002 C 143.3532 0.0338 143.675 0 143.9999 0 C 150.6423 0 156.0271 14.1367 156.0271 31.5752 Z M 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 L 0 31.575 L 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 Z M 143.0349 0.1002 L 142.9288 0.1235 M 143.0349 63.0502 L 142.9287 63.0269"/>
                                <path id="Vector_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 156.0271 31.5752 C 156.0271 49.0137 150.6423 63.1504 143.9999 63.1504 C 143.675 63.1504 143.3532 63.1166 143.0349 63.0502 C 142.9994 63.0428 142.9641 63.0351 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 C 142.9641 0.1153 142.9995 0.1075 143.0349 0.1002 C 143.3532 0.0338 143.675 0 143.9999 0 C 150.6423 0 156.0271 14.1367 156.0271 31.5752 Z M 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 L 0 31.575 L 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 Z M 143.0349 0.1002 L 142.9288 0.1235 M 143.0349 63.0502 L 142.9287 63.0269"/>
                            </g>
                        </g>
                        <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:157.203;y:-20.232;w:167.213;h:137.061" transform="translate(157.203125, -20.232421875)">
                            <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:167.213;h:137.061">
                                <path id="Vector_2" fill="#fefbdb" d="M 10.9814 31.746 C 4.8465 33.0557 0 49.0238 0 68.5298 C 0 88.0357 4.8465 104.0039 10.9814 105.3136 L 154.7335 136.9431 C 145.2825 134.8489 137.7881 105.0201 137.7881 68.5303 C 137.7881 32.0404 145.2825 2.2115 154.7336 0.1175 L 10.9814 31.746 Z M 155.7969 0 C 155.4449 0 155.0953 0.0384 154.7483 0.1142 L 154.7336 0.1175 C 145.2825 2.2115 137.7881 32.0404 137.7881 68.5303 C 137.7881 105.0201 145.2825 134.8489 154.7335 136.9431 L 154.7483 136.9463 C 155.0953 137.0221 155.4449 137.0605 155.7969 137.0605 C 160.1299 137.0605 164.1055 131.2372 167.2128 121.5349 C 163.8233 108.1173 161.7168 89.328 161.7168 68.5298 C 161.7168 47.7319 163.8233 28.9428 167.2126 15.5252 C 164.1054 5.8232 160.1298 0 155.7969 0 Z M 11.7973 105.4004 C 11.5231 105.4004 11.251 105.3712 10.9814 105.3136 M 11.7973 31.6592 C 11.5231 31.6592 11.251 31.6884 10.9814 31.746 M 154.7483 0.1142 C 154.7434 0.1153 154.7385 0.1164 154.7336 0.1175 M 154.7483 136.9463 C 154.7434 136.9452 154.7384 136.9441 154.7335 136.9431"/>
                                <path id="Vector_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 10.9814 31.746 C 4.8465 33.0557 0 49.0238 0 68.5298 C 0 88.0357 4.8465 104.0039 10.9814 105.3136 L 154.7335 136.9431 C 145.2825 134.8489 137.7881 105.0201 137.7881 68.5303 C 137.7881 32.0404 145.2825 2.2115 154.7336 0.1175 L 10.9814 31.746 Z M 155.7969 0 C 155.4449 0 155.0953 0.0384 154.7483 0.1142 L 154.7336 0.1175 C 145.2825 2.2115 137.7881 32.0404 137.7881 68.5303 C 137.7881 105.0201 145.2825 134.8489 154.7335 136.9431 L 154.7483 136.9463 C 155.0953 137.0221 155.4449 137.0605 155.7969 137.0605 C 160.1299 137.0605 164.1055 131.2372 167.2128 121.5349 C 163.8233 108.1173 161.7168 89.328 161.7168 68.5298 C 161.7168 47.7319 163.8233 28.9428 167.2126 15.5252 C 164.1054 5.8232 160.1298 0 155.7969 0 Z M 11.7973 105.4004 C 11.5231 105.4004 11.251 105.3712 10.9814 105.3136 M 11.7973 31.6592 C 11.5231 31.6592 11.251 31.6884 10.9814 31.746 M 154.7483 0.1142 C 154.7434 0.1153 154.7385 0.1164 154.7336 0.1175 M 154.7483 136.9463 C 154.7434 136.9452 154.7384 136.9441 154.7335 136.9431"/>
                            </g>
                        </g>
                        <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:318.920;y:-57.180;w:173.219;h:210.931" transform="translate(318.919921875, -57.1796875)">
                            <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:173.219;h:210.931">
                                <path id="Vector_4" fill="#f2fae1" d="M 17.2269 179.2171 L 160.7683 210.7993 C 148.1246 207.8054 138.082 161.7777 138.082 105.4653 C 138.082 49.1531 148.1245 3.1488 160.7682 0.1549 L 17.2269 31.737 C 12.6736 32.6005 8.5603 40.3409 5.4958 52.4725 C 2.1065 65.89 0 84.6791 0 105.4771 C 0 126.2752 2.1065 145.0645 5.496 158.4821 C 8.5605 170.6134 12.6737 178.3536 17.2269 179.2171 Z M 160.9527 210.8398 C 161.3264 210.9158 161.7023 210.9307 162.0803 210.9307 C 166.0971 210.9307 169.8835 206.5936 173.2101 198.9269 C 166.5166 179.2543 162.0781 144.7551 162.0781 105.4829 C 162.0781 66.1936 166.5205 31.6814 173.2188 12.0131 C 169.8839 4.2967 166.111 0 162.0803 0 C 161.7023 0 161.3264 0.0384 160.9527 0.1144 L 160.7682 0.1549 C 148.1245 3.1488 138.082 49.1531 138.082 105.4653 C 138.082 161.7777 148.1246 207.8054 160.7683 210.7993 L 160.9527 210.8398 Z M 18.08 179.2979 C 17.794 179.2979 17.5096 179.2708 17.2269 179.2171 M 18.08 31.6563 C 17.794 31.6563 17.5096 31.6834 17.2269 31.737 M 160.9527 210.8398 C 160.8912 210.8273 160.8297 210.8138 160.7683 210.7993 M 160.9527 0.1144 C 160.8911 0.1269 160.8297 0.1404 160.7682 0.1549"/>
                                <path id="Vector_5" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 17.2269 179.2171 L 160.7683 210.7993 C 148.1246 207.8054 138.082 161.7777 138.082 105.4653 C 138.082 49.1531 148.1245 3.1488 160.7682 0.1549 L 17.2269 31.737 C 12.6736 32.6005 8.5603 40.3409 5.4958 52.4725 C 2.1065 65.89 0 84.6791 0 105.4771 C 0 126.2752 2.1065 145.0645 5.496 158.4821 C 8.5605 170.6134 12.6737 178.3536 17.2269 179.2171 Z M 160.9527 210.8398 C 161.3264 210.9158 161.7023 210.9307 162.0803 210.9307 C 166.0971 210.9307 169.8835 206.5936 173.2101 198.9269 C 166.5166 179.2543 162.0781 144.7551 162.0781 105.4829 C 162.0781 66.1936 166.5205 31.6814 173.2188 12.0131 C 169.8839 4.2967 166.111 0 162.0803 0 C 161.7023 0 161.3264 0.0384 160.9527 0.1144 L 160.7682 0.1549 C 148.1245 3.1488 138.082 49.1531 138.082 105.4653 C 138.082 161.7777 148.1246 207.8054 160.7683 210.7993 L 160.9527 210.8398 Z M 18.08 179.2979 C 17.794 179.2979 17.5096 179.2708 17.2269 179.2171 M 18.08 31.6563 C 17.794 31.6563 17.5096 31.6834 17.2269 31.737 M 160.9527 210.8398 C 160.8912 210.8273 160.8297 210.8138 160.7683 210.7993 M 160.9527 0.1144 C 160.8911 0.1269 160.8297 0.1404 160.7682 0.1549"/>
                            </g>
                        </g>
                        <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:480.998;y:-94.125;w:197.985;h:284.844" transform="translate(480.998046875, -94.125)">
                            <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:197.985;h:284.844">
                                <path id="Vector_6" fill="#e7fbf2" d="M 197.9855 142.4219 C 197.9855 202.9168 190.0451 254.6024 178.8448 275.2461 C 175.4822 281.444 171.8256 284.8438 168.002 284.8438 C 167.5664 284.8438 167.133 284.7996 166.7019 284.7123 C 166.6281 284.6973 166.5544 284.6811 166.4808 284.6636 C 150.6281 280.9008 138.0184 218.6561 138.0184 142.4219 C 138.0184 66.1895 150.6275 3.9459 166.4796 0.1804 C 166.5536 0.1628 166.6277 0.1465 166.7019 0.1315 C 167.133 0.0441 167.5664 0 168.002 0 C 171.8256 0 175.4822 3.3998 178.8448 9.5977 C 190.0451 30.2414 197.9855 81.927 197.9855 142.4219 Z M 23.1104 253.1231 L 166.4808 284.6636 C 150.6281 280.9008 138.0184 218.6561 138.0184 142.4219 C 138.0184 66.1895 150.6275 3.9459 166.4796 0.1804 L 23.1104 31.7333 C 18.7155 32.5194 14.6232 38.7327 11.1407 48.9585 C 4.4424 68.6267 0 103.1389 0 142.4282 C 0 181.7004 4.4385 216.1996 11.1319 235.8722 C 14.6163 246.1133 18.7118 252.3364 23.1104 253.1231 Z M 24.0609 253.208 C 23.7426 253.208 23.4257 253.1795 23.1104 253.1231 M 24.0609 31.6484 C 23.7426 31.6484 23.4257 31.6769 23.1104 31.7333 M 166.7019 284.7123 L 166.4808 284.6636 M 166.7019 0.1315 L 166.4796 0.1804"/>
                                <path id="Vector_7" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 197.9855 142.4219 C 197.9855 202.9168 190.0451 254.6024 178.8448 275.2461 C 175.4822 281.444 171.8256 284.8438 168.002 284.8438 C 167.5664 284.8438 167.133 284.7996 166.7019 284.7123 C 166.6281 284.6973 166.5544 284.6811 166.4808 284.6636 C 150.6281 280.9008 138.0184 218.6561 138.0184 142.4219 C 138.0184 66.1895 150.6275 3.9459 166.4796 0.1804 C 166.5536 0.1628 166.6277 0.1465 166.7019 0.1315 C 167.133 0.0441 167.5664 0 168.002 0 C 171.8256 0 175.4822 3.3998 178.8448 9.5977 C 190.0451 30.2414 197.9855 81.927 197.9855 142.4219 Z M 23.1104 253.1231 L 166.4808 284.6636 C 150.6281 280.9008 138.0184 218.6561 138.0184 142.4219 C 138.0184 66.1895 150.6275 3.9459 166.4796 0.1804 L 23.1104 31.7333 C 18.7155 32.5194 14.6232 38.7327 11.1407 48.9585 C 4.4424 68.6267 0 103.1389 0 142.4282 C 0 181.7004 4.4385 216.1996 11.1319 235.8722 C 14.6163 246.1133 18.7118 252.3364 23.1104 253.1231 Z M 24.0609 253.208 C 23.7426 253.208 23.4257 253.1795 23.1104 253.1231 M 24.0609 31.6484 C 23.7426 31.6484 23.4257 31.6769 23.1104 31.7333 M 166.7019 284.7123 L 166.4808 284.6636 M 166.7019 0.1315 L 166.4796 0.1804"/>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="Frame_597" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 12 12 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:216;w:696;h:204" transform="translate(0, 216)">
                    <g id="g-1_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:83;primary:MAX;counter:CENTER" data-position="x:12;y:0;w:168;h:84" transform="translate(12, 0)">
                        <g id="bubble-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:168;h:84">
                            <g id="text" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">
                                <g id="tx-ct-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                                    <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                                <g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 72, -12)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-2_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:36 0 36 0;gap:24;primary:MAX;counter:CENTER" data-position="x:180;y:0;w:168;h:156" transform="translate(180, 0)">
                        <g id="bubble-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:36;w:168;h:84" transform="translate(0, 36)">
                            <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">
                                <g id="tx-ct-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                                    <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                                <g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:24;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 72, 24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-3_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:72 0 0 0;gap:83;primary:MAX;counter:CENTER" data-position="x:348;y:0;w:168;h:156" transform="translate(348, 0)">
                        <g id="bubble-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:72;w:168;h:84" transform="translate(0, 72)">
                            <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">
                                <g id="tx-ct-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                                <g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 72, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-4_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:108 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:516;y:0;w:168;h:192" transform="translate(516, 0)">
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:96;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 72, 96)" width="24" height="24" rx="0" ry="0"/>
                        <g id="bubble-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:108;w:168;h:84" transform="translate(0, 108)">
                            <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">
                                <g id="tx-ct-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                                <g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                                </g>
                            </g>
                        </g>
                    </g>
                    <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 0, 12)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:48;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 168, 48)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:84;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 84)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:504;y:120;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 504, 120)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:672;y:156;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 672, 156)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>