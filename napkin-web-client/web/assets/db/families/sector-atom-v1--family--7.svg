<svg xmlns="http://www.w3.org/2000/svg" width="696" height="552">    <g id="sector-atom-v1--family--7">        <g id="lines">            <g id="common">                <g id="cu">                    <path id="Subtract" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 287.9995748304249, 119.99988722606184)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 38.3896 301.5751 C 15.9308 279.0223 0 222.3482 3.638e-12 156 C -0 69.8436 26.8629 0 60 0 C 93.1371 -0 120 69.8436 120 156 C 120 222.3473 104.0697 279.0207 81.6114 301.5741"></path>
                    <path id="Subtract_1" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 261.3799466259262, 136.05408760839276)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 3.7164 18.9829 C -6.2683 49.2732 4.0382 106.7374 32.5598 165.9744 C 69.9359 243.6015 124.4387 294.877 154.2953 280.5016 C 184.1518 266.1262 178.0561 191.5435 140.68 113.9164 C 112.4063 55.1943 74.3321 11.5515 44.5399 0"></path>
                    <path id="Subtract_2" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 220.40337026244322, 167.9749725782468)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 254.7784 181.1298 C 250.6832 149.5065 216.4532 102.1555 165.0123 61.1202 C 97.6605 7.3925 26.3093 -15.1627 5.6448 10.7418 C -15.0198 36.6464 22.8277 101.2011 90.1795 154.9288 C 141.3858 195.777 194.904 218.6065 226.7013 215.8204"></path>
                    <path id="Subtract_3" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 200.772024353699, 207.95324492073527)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 0 55.8859 C 16.4535 83.045 68.5389 111.6131 133.863 126.5383 C 217.855 145.7288 291.9274 135.0977 299.3084 102.793 C 306.6893 70.4884 244.5838 28.7434 160.5918 9.553 C 97.5367 -4.8538 40.0722 -2.4534 12.2187 13.3067"></path>
                    <path id="Subtract_4" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 195.3081571677037, 207.9876689876935)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 300.6858 54.631 C 285.1155 82.1655 232.3989 111.3824 166.0255 126.5108 C 82.0234 145.6573 7.9566 134.9874 0.5926 102.6789 C -6.7715 70.3704 55.3559 28.6579 139.3579 9.5114 C 205.2171 -5.4998 264.9693 -2.1832 291.2567 15.5199"></path>
                    <path id="Subtract_5" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 220.76811635830626, 168.02331240605324)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 28.1543 215.7269 C 59.9604 218.4819 113.4411 195.6846 164.6234 154.8993 C 232.0033 101.2069 269.8846 36.672 249.2337 10.7566 C 228.5827 -15.1587 157.2196 7.3591 89.8397 61.0515 C 38.3317 102.0963 4.0619 149.4771 0 181.0983"></path>
                    <path id="Subtract_6" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 261.3306302219066, 136.10511300117344)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 169.6098 18.8954 C 179.6224 49.17 169.2927 106.677 140.7154 165.9504 C 103.2987 243.5578 48.769 294.8049 18.92 280.4138 C -10.929 266.0228 -4.7942 191.4433 32.6225 113.8358 C 60.9022 55.1798 98.9574 11.5819 128.7441 0"></path></g></g>
            <g id="g-0">                <g id="cu_1" >                    <path id="Vector" transform="translate(300.00048828125, 228)" fill="#ffedeb" d="M48 0 C21.4903 1.1588e-6 -1.1588e-6 21.4903 0 48 C1.1588e-6 74.5097 21.4903 96 48 96 C74.5097 96 96 74.5097 96 48 C96 21.4903 74.5097 -1.1588e-6 48 0 Z"></path>
                    <path id="Vector_1" transform="translate(300.00048828125, 228)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 48 0 C 21.4903 0 -0 21.4903 0 48 C 0 74.5097 21.4903 96 48 96 C 74.5097 96 96 74.5097 96 48 C 96 21.4903 74.5097 -0 48 0 Z"></path></g></g>
            <g id="g-7">                <g id="cu_2" >                    <path id="Vector_2" transform="translate(258, 114)" fill="#e8f9ff" d="M46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path>
                    <path id="Vector_3" transform="translate(258, 114)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path></g></g>
            <g id="g-6">                <g id="cu_3" >                    <path id="Vector_4" transform="translate(174, 216)" fill="#e8f9ff" d="M46.1673 14.818 C51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C-3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path>
                    <path id="Vector_5" transform="translate(174, 216)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 14.818 C 51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C 20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C -3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C 27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path></g></g>
            <g id="g-5">                <g id="cu_4" >                    <path id="Vector_6" transform="translate(204, 348)" fill="#e8f9ff" d="M14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path>
                    <path id="Vector_7" transform="translate(204, 348)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path></g></g>
            <g id="g-4">                <g id="cu_5" >                    <path id="Vector_8" transform="translate(324, 408)" fill="#e8f9ff" d="M33.182 46.1673 C20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C-3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 C51.2384 27.0607 45.4247 41.0962 33.182 46.1673 Z"></path>
                    <path id="Vector_9" transform="translate(324, 408)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 33.182 46.1673 C 20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C -3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C 27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 C 51.2384 27.0607 45.4247 41.0962 33.182 46.1673 Z"></path></g></g>
            <g id="g-3">                <g id="cu_6" >                    <path id="Vector_10" transform="translate(444, 348)" fill="#e8f9ff" d="M46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path>
                    <path id="Vector_11" transform="translate(444, 348)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path></g></g>
            <g id="g-2">                <g id="cu_7" >                    <path id="Vector_12" transform="translate(480, 216)" fill="#e8f9ff" d="M46.1673 14.818 C51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C-3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path>
                    <path id="Vector_13" transform="translate(480, 216)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 14.818 C 51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C 20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C -3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C 27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path></g></g>
            <g id="g-1">                <g id="cu_8" >                    <path id="Vector_14" transform="translate(390, 114)" fill="#e8f9ff" d="M14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path>
                    <path id="Vector_15" transform="translate(390, 114)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path></g></g></g>
        <path id="ic-cc-7" transform="translate(270, 126)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-6" transform="translate(186, 228)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 216, 360)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 336, 420)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-3" transform="translate(456, 360)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 492, 228)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 402, 126)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-0" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 324, 252)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="tx-rb-7" transform="translate(102, 78)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 18, 204)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-5" transform="translate(48, 336)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 276, 468)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-3" transform="translate(504, 336)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 540, 204)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-1" transform="translate(450, 78)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="translate(246, 126)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="translate(162, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="translate(192, 360)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="translate(336, 444)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="translate(480, 360)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="translate(516, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="translate(426, 126)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-8" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 318, 108)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 210, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 186, 292)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 264, 402)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 486, 294)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 408, 402)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 462, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 354, 108)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 48, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path></g></svg>