<svg xmlns="http://www.w3.org/2000/svg" width="696" height="552">    <g id="sector-atom-v1--family--5">        <g id="lines">            <g id="common">                <g id="cu">                    <path id="Subtract" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 287.9999999999996, 89.99999893057992)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 38.3889 301.5745 C 15.9305 279.0213 -0 222.3476 9.0949e-13 156 C 0 69.8436 26.8629 -0 60 4.5475e-13 C 93.1371 0 120 69.8436 120 156 C 120 222.3479 104.0694 279.0218 81.6108 301.5748"></path>
                    <path id="Subtract_1" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 244.2285156249995, 115.82934463370498)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 170.1378 0 C 138.7627 6.0641 93.5242 42.1964 55.23 94.9039 C 4.5885 164.6059 -14.732 236.9002 12.0765 256.3777 C 38.885 275.8552 101.6705 235.1402 152.312 165.4382 C 191.6088 111.3508 212.0458 55.7024 206.7039 24.3832"></path>
                    <path id="Subtract_2" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 244.2294921874995, 115.82836807120498)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 0.8376 24.3862 C -4.5027 55.7059 15.9342 111.3527 55.23 165.4386 C 105.8714 235.1406 168.657 275.8557 195.4655 256.3782 C 222.2739 236.9007 202.9535 164.6064 152.312 94.9044 C 114.0168 42.1956 68.7771 6.0627 37.4019 0"></path>
                    <path id="Subtract_3" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 202.83886718749943, 171.2988270555801)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 12.1904 139.6125 C 40.0975 154.6939 99.6992 152.5609 163.7018 131.7652 C 245.6415 105.1414 303.7656 58.0104 293.5256 26.4951 C 283.2857 -5.0201 208.5594 -8.9854 126.6198 17.6384 C 63.4714 38.1565 14.4678 70.8545 0 99.1981"></path>
                    <path id="Subtract_4" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 198.46533203124943, 171.2985829149551)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" stroke-dasharray="5 3" d="M 294.6946 99.1964 C 280.2257 70.8533 231.2228 38.1561 168.0756 17.6384 C 86.136 -8.9854 11.4097 -5.0201 1.1698 26.4951 C -9.0701 58.0104 49.0539 105.1414 130.9936 131.7652 C 194.9961 152.5609 254.5977 154.6939 282.5049 139.6126"></path></g></g>
            <g id="g-0">                <g id="cu_1" >                    <path id="Vector" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 299.99999527916975, 197.99999792546635)" fill="#ffedeb" d="M92.3346 29.636 C102.477 54.1213 90.8493 82.1925 66.364 92.3346 C41.8787 102.477 13.8075 90.8493 3.6654 66.364 C-6.4768 41.8787 5.1507 13.8075 29.636 3.6654 C54.1213 -6.4768 82.1925 5.1507 92.3346 29.636 Z"></path>
                    <path id="Vector_1" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 299.99999527916975, 197.99999792546635)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 92.3346 29.636 C 102.4768 54.1213 90.8493 82.1925 66.364 92.3346 C 41.8787 102.4768 13.8075 90.8493 3.6654 66.364 C -6.4768 41.8787 5.1507 13.8075 29.636 3.6654 C 54.1213 -6.4768 82.1925 5.1507 92.3346 29.636 Z"></path></g></g>
            <g id="g-5">                <g id="cu_2" >                    <path id="Vector_2" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 233.99999973773123, 95.99999694786334)" fill="#e8f9ff" d="M14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path>
                    <path id="Vector_3" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 233.99999973773123, 95.99999694786334)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path></g></g>
            <g id="g-4">                <g id="cu_3" >                    <path id="Vector_4" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 173.99999213194945, 270.00000004722597)" fill="#e8f9ff" d="M33.182 46.1673 C20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C-3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 C51.2384 27.0607 45.4247 41.0962 33.182 46.1673 Z"></path>
                    <path id="Vector_5" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 173.99999213194945, 270.00000004722597)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 33.182 46.1673 C 20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C -3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C 27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 C 51.2384 27.0607 45.4247 41.0962 33.182 46.1673 Z"></path></g></g>
            <g id="g-3">                <g id="cu_4" >                    <path id="Vector_6" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 324.00000266990924, 377.99999134514576)" fill="#e8f9ff" d="M46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path>
                    <path id="Vector_7" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 324.00000266990924, 377.99999134514576)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 Z"></path></g></g>
            <g id="g-2">                <g id="cu_5" >                    <path id="Vector_8" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 473.9999883172528, 269.9999979018532)" fill="#e8f9ff" d="M46.1673 14.818 C51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C-3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path>
                    <path id="Vector_9" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 473.9999883172528, 269.9999979018532)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 46.1673 14.818 C 51.2384 27.0607 45.4247 41.0962 33.182 46.1673 C 20.9393 51.2384 6.9038 45.4247 1.8327 33.182 C -3.2384 20.9393 2.5753 6.9038 14.818 1.8327 C 27.0607 -3.2384 41.0962 2.5753 46.1673 14.818 Z"></path></g></g>
            <g id="g-1">                <g id="cu_6" >                    <path id="Vector_10" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 413.9999921083371, 95.99999670968121)" fill="#e8f9ff" d="M14.818 46.1673 C2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path>
                    <path id="Vector_11" transform="matrix(1.000000000000002, 0, 0, 1.000000000000002, 413.9999921083371, 95.99999670968121)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.818 46.1673 C 2.5753 41.0962 -3.2384 27.0607 1.8327 14.818 C 6.9038 2.5753 20.9393 -3.2384 33.182 1.8327 C 45.4247 6.9038 51.2384 20.9393 46.1673 33.182 C 41.0962 45.4247 27.0607 51.2384 14.818 46.1673 Z"></path></g></g></g>
        <path id="ic-cc-5" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 246, 108)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 186, 282)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-3" transform="translate(336, 390)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 486, 282)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 426, 108)" fill="#33de7b1a" d="M0 0 L24 0 L24 24 L0 24 L0 0 Z"></path>
        <path id="ic-cc-0" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 324, 222)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-5" transform="translate(78, 84)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 18, 258)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-3" transform="translate(276, 438)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 534, 258)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-1" transform="translate(474, 84)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="translate(222, 108)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="translate(162, 282)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="translate(336, 414)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="translate(510, 282)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="translate(450, 108)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 318, 78)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 186, 193)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 240, 354)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 432, 354)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 486, 193)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 354, 78)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 48, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path></g></svg>