<svg xmlns="http://www.w3.org/2000/svg" width="1020" height="792">
    <g id="cycle-donut-v1--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1020 0 L1020 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1020;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1020;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:1020;h:744">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:52;w:636;h:636" transform="translate(192, 52)">
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:318;h:636">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:318;h:636">
                            <path id="Vector" fill="#f2fae1" d="M155.749 304.227 C162.162 288.745 178.131 280.117 193.99 282.348 C196.942 282.763 199.891 283.555 202.773 284.749 C221.137 292.356 229.858 313.409 222.251 331.773 C217.194 343.983 206.192 351.929 193.989 353.649 C187.838 354.516 181.381 353.8 175.227 351.251 C156.863 343.644 148.142 322.591 155.749 304.227 Z M0 318 C0 489.609 135.934 629.468 306 635.778 L306 468.337 C306 455.937 296.452 445.815 284.476 442.601 C240.886 430.902 206.41 396.938 193.989 353.649 C187.838 354.516 181.381 353.8 175.227 351.251 C156.863 343.644 148.142 322.591 155.749 304.227 C162.162 288.745 178.131 280.117 193.99 282.348 C206.412 239.06 240.887 205.098 284.475 193.399 C296.452 190.185 306 180.063 306 167.663 L306 0.2222 C299.104 0.4781 292.264 0.9535 285.486 1.6418 C125.133 17.9266 0 153.35 0 318 Z"/>
                            <path id="Vector_1" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 155.749 304.227 C 162.1618 288.7453 178.1315 280.1174 193.9897 282.3482 C 196.9422 282.7635 199.8907 283.5552 202.773 284.7491 C 221.137 292.3557 229.8576 313.409 222.251 331.773 C 217.1936 343.9826 206.1921 351.9294 193.9889 353.6489 C 187.8378 354.5156 181.3814 353.8002 175.227 351.2509 C 156.863 343.6443 148.1424 322.591 155.749 304.227 Z M 0 318 C 0 489.6087 135.9338 629.4684 306 635.7778 L 306 468.3373 C 306 455.9366 296.4524 445.8153 284.4755 442.6009 C 240.8856 430.902 206.4103 396.9382 193.9889 353.6489 C 187.8378 354.5156 181.3814 353.8002 175.227 351.2509 C 156.863 343.6443 148.1424 322.591 155.749 304.227 C 162.1618 288.7453 178.1315 280.1174 193.9897 282.3482 C 206.4118 239.0602 240.8865 205.0978 284.4755 193.3991 C 296.4523 190.1847 306 180.0634 306 167.6627 L 306 0.2222 C 299.1037 0.4781 292.2636 0.9535 285.4864 1.6418 C 125.1329 17.9266 0 153.3501 0 318 Z M 318 636 C 142.3734 636 0 493.6266 0 318 M 318 0 C 307.0233 0 296.1766 0.5561 285.4864 1.6418"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:318;y:0;w:318;h:636" transform="translate(318, 0)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:318;h:636">
                            <path id="Vector_2" fill="#fefbdb" d="M162.251 304.227 C155.838 288.745 139.869 280.117 124.01 282.348 C121.058 282.763 118.109 283.555 115.227 284.749 C96.863 292.356 88.1424 313.409 95.749 331.773 C100.806 343.983 111.808 351.929 124.011 353.649 C130.162 354.516 136.619 353.8 142.773 351.251 C161.137 343.644 169.858 322.591 162.251 304.227 Z M318 318 C318 489.609 182.066 629.468 12 635.778 L12 468.337 C12 455.937 21.5476 445.815 33.5245 442.601 C77.1144 430.902 111.59 396.938 124.011 353.649 C130.162 354.516 136.619 353.8 142.773 351.251 C161.137 343.644 169.858 322.591 162.251 304.227 C155.838 288.745 139.869 280.117 124.01 282.348 C111.588 239.06 77.1134 205.098 33.5245 193.399 C21.5477 190.185 12 180.063 12 167.663 L12 0.2222 C18.8963 0.4781 25.7364 0.9535 32.5136 1.6418 C192.867 17.9266 318 153.35 318 318 Z"/>
                            <path id="Vector_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 162.251 304.227 C 155.8382 288.7453 139.8685 280.1174 124.0103 282.3482 C 121.0578 282.7635 118.1093 283.5552 115.227 284.7491 C 96.863 292.3557 88.1424 313.409 95.749 331.773 C 100.8064 343.9826 111.8079 351.9294 124.0111 353.6489 C 130.1622 354.5156 136.6186 353.8002 142.773 351.2509 C 161.137 343.6443 169.8576 322.591 162.251 304.227 Z M 318 318 C 318 489.6087 182.0662 629.4684 12 635.7778 L 12 468.3373 C 12 455.9366 21.5476 445.8153 33.5245 442.6009 C 77.1144 430.902 111.5897 396.9382 124.0111 353.6489 C 130.1622 354.5156 136.6186 353.8002 142.773 351.2509 C 161.137 343.6443 169.8576 322.591 162.251 304.227 C 155.8382 288.7453 139.8685 280.1174 124.0103 282.3482 C 111.5882 239.0602 77.1135 205.0978 33.5245 193.3991 C 21.5477 190.1847 12 180.0634 12 167.6627 L 12 0.2222 C 18.8963 0.4781 25.7364 0.9535 32.5136 1.6418 C 192.8671 17.9266 318 153.3501 318 318 Z M 0 636 C 175.6266 636 318 493.6266 318 318 M 0 0 C 10.9767 0 21.8234 0.5561 32.5136 1.6418"/>
                        </g>
                    </g>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:615;y:346;w:48;h:48" transform="translate(615, 346)">
                    <path id="rect" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d1bd08" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:357;y:346;w:48;h:48" transform="translate(357, 346)">
                    <path id="rect_1" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#93c332" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:720;y:346;w:48;h:48" transform="translate(720, 346)">
                    <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:346;w:48;h:48" transform="translate(252, 346)">
                    <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 516, 12)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:498;y:708;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 498, 708)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:480;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 480, 12)" width="24" height="24" rx="0" ry="0"/>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:12;y:336;w:144;h:60" transform="translate(12, 336)">
                    <g id="tx-rt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                </g>
                <g id="text-1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:864;y:336;w:144;h:60" transform="translate(864, 336)">
                    <g id="tx-lt-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>