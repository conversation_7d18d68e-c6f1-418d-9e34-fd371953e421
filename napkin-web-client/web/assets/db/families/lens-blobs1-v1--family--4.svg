<svg xmlns="http://www.w3.org/2000/svg" width="594" height="564">
    <g id="lens-blobs1-v1--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L594 0 L594 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:594;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:594;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:156 48 54 48;gap:18;primary:MIN;counter:MIN" data-position="x:0;y:0;w:594;h:516">
                <g id="Frame_11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:66;primary:MIN;counter:MIN" data-position="x:48;y:156;w:240;h:306" transform="translate(48, 156)">
                    <g id="bubble" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:60 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:180;h:120">
                        <g id="shape-1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-30;y:-138;w:246;h:282" transform="translate(-30, -138)">
                            <path id="fill" transform="translate(0.00017166142060887069, 246)" fill="#fefbdb" d="M204 36 L35.9997 35.9999 C16.1175 35.9999 -0.0002 19.8977 -0.0002 0.0156 L-0.0002 0 L240 0 C240 19.8822 223.882 36 204 36 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:246;w:240;h:36"/>
                            <path id="fill_1" fill="#fefbdb" d="M244.6 18.191 L240 18.191 L240 150 L0 150 C2.1544e-5 130.118 16.1177 114.321 35.9999 114.321 L162 114.321 C203.07 114.321 228 84.1909 228 44.8509 L228 18.1909 L223.32 18.1909 C222.54 18.1909 222.05 17.3409 222.44 16.6609 L233.163 0.4483 C233.559 -0.1506 234.439 -0.1492 234.833 0.451 L245.48 16.6609 C245.88 17.341 245.39 18.191 244.6 18.191 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:245.622;h:150"/>
                            <path id="fill_2" transform="translate(0, 150)" fill="#fefbdb" d="M240 0 L240 96 L0 96 L2.7525e-5 0 L240 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:150;w:240;h:96"/>
                            <path id="stroke" transform="translate(0, -0.18359375)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 150.1836 C 0 130.3014 16.1177 114.3209 35.9999 114.3209 L 162 114.3209 C 203.07 114.3209 228 84.1909 228 44.8509 L 228 18.1909 L 223.32 18.1909 C 222.54 18.1909 222.05 17.3409 222.44 16.6609 L 233.1631 0.4483 C 233.5592 -0.1506 234.4387 -0.1492 234.833 0.451 L 245.48 16.6609 C 245.88 17.341 245.39 18.191 244.6 18.191 L 240 18.191 L 240 150.1836" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.184;w:245.622;h:150.184"/>
                            <path id="stroke_1" transform="translate(0, 246)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 240.0001 0 C 240.0001 19.8822 223.8823 36.0078 204.0001 36.0078 L 35.9999 36.0077 C 16.1177 36.0077 0 19.8977 0 0.0156 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:246;w:240.000;h:36.008"/>
                            <path id="stroke_2" transform="translate(0, 150)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 240.0001 96 L 240.0001 0 M 0 0 L 0 96" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:150;w:240.000;h:96"/>
                        </g>
                        <g id="shape-3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-30;y:-138;w:246;h:282" transform="translate(-30, -138)">
                            <path id="fill_3" transform="translate(257.99951171875, 150)" fill="#e8f9ff" d="M12 96 L0 96 L0 0 L12 0 L12 96 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:258.000;y:150;w:12;h:96"/>
                            <path id="fill_4" transform="translate(252.302734375, 0)" fill="#e8f9ff" d="M22.2969 18.191 L17.6969 18.191 L17.6969 150 L5.6969 150 L5.6969 18.1909 L1.0169 18.1909 C0.2369 18.1909 -0.2531 17.3409 0.1369 16.6609 L10.8599 0.4483 C11.2561 -0.1506 12.1356 -0.1492 12.5298 0.451 L23.1769 16.6609 C23.5769 17.341 23.0869 18.191 22.2969 18.191 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252.303;y:0;w:23.319;h:150"/>
                            <path id="stroke_3" transform="translate(258, 150)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 96 M 12 96 L 12 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:258;y:150;w:12;h:96"/>
                            <path id="stroke_4" transform="translate(252.302734375, 0)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 5.6969 150 L 5.6969 18.1909 L 1.0169 18.1909 C 0.2369 18.1909 -0.2531 17.3409 0.1369 16.6609 L 10.8599 0.4483 C 11.2561 -0.1506 12.1356 -0.1492 12.5298 0.451 L 23.1769 16.6609 C 23.5769 17.341 23.0869 18.191 22.2969 18.191 L 17.6969 18.191 L 17.6969 150" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252.303;y:0;w:23.319;h:150"/>
                        </g>
                        <g id="shape-3_1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-30;y:108;w:270.000;h:90.305" transform="translate(-30, 108)">
                            <path id="fill_5" fill="#e8f9ff" d="M204 54.3209 C234 54.3209 258 36 258 0 L270 0 L270 90.3055 L0 90.3055 C2.1544e-5 70.4233 16.1177 54.3209 35.9999 54.3209 L204 54.3209 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270.000;h:90.305"/>
                            <path id="stroke_5" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 258 0 C 258 36 234 54.3209 204 54.3209 L 35.9999 54.3209 C 16.1177 54.3209 0 70.4233 0 90.3055 M 270 90.3055 L 270 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270.000;h:90.305"/>
                        </g>
                        <g id="tx-ct-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:24" fill="#ff00001a" transform="translate(0, 60)">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:180;h:24" fill="#ff00001a" transform="translate(0, 96)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:78;y:-45;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 78, -45)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24.500;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24.5, 60)" width="24" height="24" rx="0" ry="0"/>
                        <g id="ic-cc-1" data-entity-classes="InsideFill" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:0;w:48;h:48" transform="translate(66, 0)">
                            <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                            <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <path id="icon_1" transform="translate(10, 6)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                            </g>
                        </g>
                    </g>
                    <g id="bubble_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:60 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:186;w:210;h:120" transform="translate(0, 186)">
                        <g id="shape-3_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-30;y:12;w:270.000;h:132.008" transform="translate(-30, 12)">
                            <path id="fill_6" fill="#e8f9ff" d="M270 0 L270 95.9844 L0 95.9844 L3.0965e-5 0 L270 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270;h:96"/>
                            <path id="fill_7" transform="translate(0, 96.0078125)" fill="#e8f9ff" d="M234 36 L35.9999 35.9999 C16.1177 35.9999 3.2428e-5 19.8977 0 0.0156 L0 0 L270 0 C270 19.8822 253.882 36 234 36 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96.008;w:270.000;h:36.000"/>
                            <path id="stroke_6" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 270 95.9844 L 270 0 M 0 0 L 0 96" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270;h:96"/>
                            <path id="stroke_7" transform="translate(0, 96.0078125)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 270.0001 0 C 270.0001 19.8822 253.8823 36 234.0001 36 L 35.9999 35.9999 C 16.1177 35.9999 0 19.8977 0 0.0156 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96.008;w:270.000;h:36.000"/>
                        </g>
                        <g id="tx-ct-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:210;h:24" fill="#ff00001a" transform="translate(0, 60)">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:210;h:24" fill="#ff00001a" transform="translate(0, 96)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:78;y:-45;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 78, -45)" width="24" height="24" rx="0" ry="0"/>
                        <g id="ic-cc-3" data-entity-classes="InsideFill" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:81;y:0;w:48;h:48" transform="translate(81, 0)">
                            <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                            <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <path id="icon_3" transform="translate(10, 6)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24.500;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24.5, 60)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:78;y:141;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 78, 141)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Frame_12" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:66;primary:MIN;counter:MAX" data-position="x:306;y:156;w:240;h:306" transform="translate(306, 156)">
                    <g id="bubble_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:60 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:60;y:0;w:180;h:120" transform="translate(60, 0)">
                        <g id="shape-2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:-138;w:246;h:282" transform="translate(-36, -138)">
                            <path id="fill_8" transform="translate(5.999996185302734, 246.00006103515625)" fill="#fef2e6" d="M36 36 L204 35.9999 C223.882 35.9999 240 19.8977 240 0.0156 L240 0 L0 0 C0 19.8822 16.1177 36 36 36 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.000;y:246.000;w:240;h:36.000"/>
                            <path id="fill_9" transform="translate(0.3775787353515625, 0)" fill="#fef2e6" d="M1.0224 18.191 L5.6224 18.191 L5.6224 150 L245.622 150 C245.622 130.118 229.505 114.321 209.623 114.321 L83.6224 114.321 C42.5524 114.321 17.6224 84.1909 17.6224 44.8509 L17.6224 18.1909 L22.3024 18.1909 C23.0824 18.1909 23.5724 17.3409 23.1824 16.6609 L12.4594 0.4483 C12.0632 -0.1506 11.1837 -0.1492 10.7895 0.451 L0.1424 16.6609 C-0.2576 17.341 0.2324 18.191 1.0224 18.191 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.378;y:0;w:245.622;h:150"/>
                            <path id="fill_10" transform="translate(5.9998931884765625, 150)" fill="#fef2e6" d="M0 0 L0 96 L240 96 L240 0 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.000;y:150;w:240.000;h:96"/>
                            <path id="stroke_8" transform="translate(0.3775787353515625, -0.18359375)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 245.6224 150.1836 C 245.6224 130.3014 229.5047 114.3209 209.6226 114.3209 L 83.6224 114.3209 C 42.5524 114.3209 17.6224 84.1909 17.6224 44.8509 L 17.6224 18.1909 L 22.3024 18.1909 C 23.0824 18.1909 23.5724 17.3409 23.1824 16.6609 L 12.4594 0.4483 C 12.0632 -0.1506 11.1837 -0.1492 10.7895 0.451 L 0.1424 16.6609 C -0.2576 17.341 0.2324 18.191 1.0224 18.191 L 5.6224 18.191 L 5.6224 150.1836" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.378;y:-0.184;w:245.622;h:150.184"/>
                            <path id="stroke_9" transform="translate(6, 246)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 C 0 19.8822 16.1177 36 36 36 L 204.0002 35.9999 C 223.8823 35.9999 240 19.8977 240 0.0156 L 240 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:246;w:240;h:36"/>
                            <path id="stroke_10" transform="translate(5.9998931884765625, 150)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 96 L 0 0 M 240 0 L 240 96" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.000;y:150;w:240;h:96"/>
                        </g>
                        <g id="shape-4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:-138;w:246;h:282" transform="translate(-36, -138)">
                            <path id="fill_11" transform="translate(-23.994873046875, 150)" fill="#faf0ff" d="M0 96 L12 96 L12 0 L0 0 L0 96 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-23.995;y:150;w:12;h:96"/>
                            <path id="fill_12" transform="translate(-29.617431640625, 0)" fill="#faf0ff" d="M1.0224 18.191 L5.6224 18.191 L5.6224 150 L17.6224 150 L17.6224 18.1909 L22.3024 18.1909 C23.0824 18.1909 23.5724 17.3409 23.1824 16.6609 L12.4594 0.4483 C12.0632 -0.1506 11.1837 -0.1492 10.7895 0.451 L0.1424 16.6609 C-0.2576 17.341 0.2324 18.191 1.0224 18.191 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-29.617;y:0;w:23.319;h:150"/>
                            <path id="stroke_11" transform="translate(-23.995361328125, 150)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 12 0 L 12 96 M 0 96 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-23.995;y:150;w:12;h:96"/>
                            <path id="stroke_12" transform="translate(-29.617431640625, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 17.6224 150 L 17.6224 18.1909 L 22.3024 18.1909 C 23.0824 18.1909 23.5724 17.3409 23.1824 16.6609 L 12.4594 0.4483 C 12.0632 -0.1506 11.1837 -0.1492 10.7895 0.451 L 0.1424 16.6609 C -0.2576 17.341 0.2324 18.191 1.0224 18.191 L 5.6224 18.191 L 5.6224 150" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-29.617;y:0;w:23.319;h:150"/>
                        </g>
                        <g id="shape-4_1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-60;y:108;w:270.000;h:90.305" transform="translate(-60, 108)">
                            <path id="fill_13" transform="translate(-0.000030517578125, 0)" fill="#faf0ff" d="M66 54.3209 C36 54.3209 12 36 12 0 L0 0 L0 90.3055 L270 90.3055 C270 70.4233 253.882 54.3209 234 54.3209 L66 54.3209 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.000;y:0;w:270.000;h:90.305"/>
                            <path id="stroke_13" transform="translate(-0.000030517578125, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 12 0 C 12 36 36 54.3209 66 54.3209 L 234.0002 54.3209 C 253.8824 54.3209 270 70.4233 270 90.3055 M 0 90.3055 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.000;y:0;w:270.000;h:90.305"/>
                        </g>
                        <g id="tx-ct-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:24" fill="#ff00001a" transform="translate(0, 60)">
                            <text id="Label_4" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:180;h:24" fill="#ff00001a" transform="translate(0, 96)">
                            <text id="Label_5" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:78;y:-45;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 78, -45)" width="24" height="24" rx="0" ry="0"/>
                        <g id="ic-cc-2" data-entity-classes="InsideFill" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:0;w:48;h:48" transform="translate(66, 0)">
                            <rect id="Rectangle_7_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                            <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <path id="icon_5" transform="translate(10, 6)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:179;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 179, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="bubble_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:60 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:30;y:186;w:210;h:120" transform="translate(30, 186)">
                        <g id="shape-4_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-30;y:12;w:270.000;h:132.008" transform="translate(-30, 12)">
                            <path id="fill_14" fill="#faf0ff" d="M0 0 L0 96 L270 96 L270 0 L0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270;h:96"/>
                            <path id="fill_15" transform="translate(0, 96)" fill="#faf0ff" d="M36 36 L234 35.9999 C253.882 35.9999 270 19.8977 270 0.0156 L270 0 L0 0 C0 19.8822 16.1177 36 36 36 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:270;h:36"/>
                            <path id="stroke_14" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 96 L 0 0 M 270 0 L 270 96" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:270;h:96"/>
                            <path id="stroke_15" transform="translate(0, 96)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 C 0 19.8822 16.1177 36.0078 35.9999 36.0078 L 234.0001 36.0077 C 253.8823 36.0077 270 19.8977 270.0001 0.0156 L 270.0001 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:270.000;h:36.008"/>
                        </g>
                        <g id="tx-ct-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:210;h:24" fill="#ff00001a" transform="translate(0, 60)">
                            <text id="Label_6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:96;w:210;h:24" fill="#ff00001a" transform="translate(0, 96)">
                            <text id="Label_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:-45;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, -45)" width="24" height="24" rx="0" ry="0"/>
                        <g id="ic-cc-4" data-entity-classes="InsideFill" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:81;y:0;w:48;h:48" transform="translate(81, 0)">
                            <rect id="Rectangle_7_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                            <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <path id="icon_7" transform="translate(10, 6)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:210;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 210, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>