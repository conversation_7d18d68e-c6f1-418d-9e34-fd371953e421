<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="diverge3-v5--family--10">        <g id="lines">            <g id="g-10">                <g id="ar-with-terminator">                    <path id="line" marker-end="url(#arrow)" transform="translate(444.0000305175781, 324.00001525878906)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-9">                <g id="ar-with-terminator_1">                    <path id="line_1" marker-end="url(#arrow)" transform="matrix(-1, -8.742277657347586e-8, 8.742277657347586e-8, -1, 300.00000056824825, 324.00007677078247)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-8">                <g id="ar-with-terminator_2">                    <path id="line_2" marker-end="url(#arrow)" transform="matrix(0.30901700258255005, -0.9510565400123596, 0.9510565400123596, 0.30901700258255005, 394.2509913181693, 255.52259339590637)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-7">                <g id="ar-with-terminator_3">                    <path id="line_3" marker-end="url(#arrow)" transform="matrix(-0.3090170919895172, 0.9510565400123596, -0.9510565400123596, -0.3090170919895172, 349.7526374725428, 392.4746970598463)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-6">                <g id="ar-with-terminator_4">                    <path id="line_4" marker-end="url(#arrow)" transform="matrix(-0.80901700258255, -0.5877852439880371, 0.5877852439880371, -0.80901700258255, 313.7518376521298, 281.67589936011024)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-5">                <g id="ar-with-terminator_5">                    <path id="line_5" marker-end="url(#arrow)" transform="matrix(0.8090169429779053, 0.5877853035926819, -0.5877853035926819, 0.8090169429779053, 430.25038594712373, 366.3170939816782)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-4">                <g id="ar-with-terminator_6">                    <path id="line_6" marker-end="url(#arrow)" transform="matrix(-0.80901700258255, 0.5877852439880371, -0.5877852439880371, -0.80901700258255, 313.74964929356656, 366.3170237211809)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-3">                <g id="ar-with-terminator_7">                    <path id="line_7" marker-end="url(#arrow)" transform="matrix(0.8090170621871948, -0.5877851843833923, 0.5877851843833923, 0.8090170621871948, 430.24809393286705, 281.6759120821953)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-2">                <g id="ar-with-terminator_8">                    <path id="line_8" marker-end="url(#arrow)" transform="matrix(0.30901700258255005, 0.9510565400123596, -0.9510565400123596, 0.30901700258255005, 394.2472745683008, 392.4747487588935)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-1">                <g id="ar-with-terminator_9">                    <path id="line_9" marker-end="url(#arrow)" transform="matrix(-0.3090169131755829, -0.9510565400123596, 0.9510565400123596, -0.3090169131755829, 349.7488317858042, 255.52255974312538)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 48)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-start" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 287.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-10" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 326.6243896484375, 209.28457641601562)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-9" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-16, 2.7755575615628914e-16, 1, 272.625, 248.51889038085938)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-8" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 252, 312)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-16, -2.7755575615628914e-16, 1, 272.6272888183594, 375.48046875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 326.6279602050781, 414.7127685546875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 393.3756103515625, 414.7115783691406)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-16, 2.7755575615628914e-16, 1, 447.375, 375.4772644042969)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 468, 311.99609375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-16, -2.7755575615628914e-16, 1, 447.3726501464844, 248.515625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 393.3718566894531, 209.2833251953125)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rb-10" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 192, 120)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-9" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 108, 204)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-8" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, 288)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-7" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 108, 372)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rt-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 192, 456)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lt-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 396, 456)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 480, 372)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-3" transform="translate(504, 288)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="translate(480, 204)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 396, 120)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>