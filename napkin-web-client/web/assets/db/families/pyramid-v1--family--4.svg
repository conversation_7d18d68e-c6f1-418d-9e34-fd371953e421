<svg xmlns="http://www.w3.org/2000/svg" width="612" height="456">    <g id="pyramid-v1--family--4">        <g id="lines">            <g id="g-4">                <g id="cu">                    <g id="cu_1">                        <path id="Vector" transform="translate(114, 372)" fill="#f3f0ff" d="M432 72 L0 72 L38.4063 0 L383.992 0 L432 72 Z"></path>
                        <path id="Vector_1" transform="translate(114, 372)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 72 L 432 72 L 383.9922 0 L 38.4063 0 L 0 72 Z"></path></g>
                    <g id="cu_2">                        <path id="Vector_2" transform="translate(66, 343.07177734375)" fill="#f3f0ff" d="M48 100.928 L0 64.9283 L48.095 0 L86.4063 28.9165 L48 100.928 Z"></path>
                        <path id="Vector_3" transform="translate(66, 343.07177734375)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 48 100.9283 L 0 64.9283 L 48.095 0 L 86.4063 28.9165 L 48 100.9283 Z"></path></g></g></g>
            <g id="g-3">                <g id="cu_3">                    <g id="cu_4">                        <path id="Vector_4" transform="translate(152.40625, 300)" fill="#e8f9ff" d="M38.3937 0 L0 72 L345.586 72 L297.594 0 L38.3937 0 Z"></path>
                        <path id="Vector_5" transform="translate(152.40625, 300)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 297.5937 0 L 38.3937 0 L 0 72 L 345.5858 72 L 297.5937 0 Z"></path></g>
                    <g id="cu_5">                        <path id="Vector_6" transform="translate(114.0947265625, 278.462646484375)" fill="#e8f9ff" d="M76.705 21.5374 L38.3113 93.5256 L0 64.6091 L47.8586 0 L76.705 21.5374 Z"></path>
                        <path id="Vector_7" transform="translate(114.0947265625, 278.462646484375)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 38.3113 93.5256 L 76.705 21.5374 L 47.8586 0 L 0 64.6091 L 38.3113 93.5256 Z"></path></g></g></g>
            <g id="g-2">                <g id="cu_6">                    <g id="cu_7">                        <path id="Vector_8" transform="translate(190.7998046875, 228)" fill="#f2fae1" d="M211.2 0 L259.2 72 L0 72 L38.4 0 L211.2 0 Z"></path>
                        <path id="Vector_9" transform="translate(190.7998046875, 228)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 211.2 0 L 259.2 72 L 0 72 L 38.4 0 L 211.2 0 Z"></path></g>
                    <g id="cu_8">                        <path id="Vector_10" transform="translate(161.9541015625, 213.535888671875)" fill="#f2fae1" d="M67.2464 14.4641 L28.8464 86.4641 L0 64.9267 L48.0939 0 L67.2464 14.4641 Z"></path>
                        <path id="Vector_11" transform="translate(161.9541015625, 213.535888671875)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 28.8464 86.4641 L 67.2464 14.4641 L 48.0939 0 L 0 64.9267 L 28.8464 86.4641 Z"></path></g></g></g>
            <g id="g-1">                <g id="cu_9">                    <g id="cu_10">                        <path id="Vector_12" transform="translate(229.2001953125, 84)" fill="#fefbdb" d="M76.8 0 L172.8 144 L0 144 L76.8 0 Z"></path>
                        <path id="Vector_13" transform="translate(229.2001953125, 84)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 76.8 0 L 172.8 144 L 0 144 L 76.8 0 Z"></path></g>
                    <g id="cu_11">                        <path id="Vector_14" transform="translate(210.0478515625, 84)" fill="#fefbdb" d="M95.9525 0 L19.1525 144 L0 129.536 L95.9525 0 Z"></path>
                        <path id="Vector_15" transform="translate(210.0478515625, 84)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 95.9525 0 L 19.1525 144 L 0 129.5359 L 95.9525 0 Z"></path></g></g></g></g>
        <rect id="tx-cc-4" fill="#ff00001a" transform="translate(150, 372)" width="348" height="72" rx="0" ry="0"></rect>
        <rect id="tx-cc-3" fill="#ff00001a" transform="translate(186, 300)" width="264" height="72" rx="0" ry="0"></rect>
        <rect id="tx-cc-2" fill="#ff00001a" transform="translate(222, 228)" width="192" height="72" rx="0" ry="0"></rect>
        <rect id="tx-cc-1" fill="#ff00001a" transform="translate(258, 156)" width="108" height="72" rx="0" ry="0"></rect>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 0, 0)" fill="#ff00001a" d="M0 0 L612 0 L612 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 432)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 360)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 216)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 294, 72)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 99, 380)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 142, 313)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 185, 244)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 225, 180)" width="24" height="24" rx="0" ry="0"></rect></g></svg>