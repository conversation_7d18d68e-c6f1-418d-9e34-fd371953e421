<svg xmlns="http://www.w3.org/2000/svg" width="744" height="546">
    <g id="venn-arrows-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L744 0 L744 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:744;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:744;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:744;h:498">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:210;w:360;h:264" transform="translate(192, 210)">
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:96;y:8.998;w:168;h:246.003" transform="translate(96, 8.998046875)">
                        <path id="vect-fill" fill="#ffedeb" d="M 84 246.0029 C 133.1683 226.8012 168 178.969 168 123.0014 C 168 67.0339 133.1683 19.2017 84 0 C 34.8317 19.2017 0 67.0339 0 123.0014 C 0 178.969 34.8317 226.8012 84 246.0029 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:246.003"/>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:179.500;y:0;w:180.500;h:264" transform="translate(179.5, 0)">
                        <g id="cu_cu" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180.500;h:264">
                            <path id="cu" fill="#fef2e6" d="M 48.5 264 C 121.4016 264 180.5 204.9016 180.5 132 C 180.5 59.0984 121.4016 0 48.5 0 C 31.566 0 15.3768 3.1888 0.5 8.9986 C 49.6683 28.2002 84.5 76.0324 84.5 132 C 84.5 187.9675 49.6683 235.7997 0.5 255.0014 C 0.3332 254.9362 0.1665 254.8708 0 254.805 C 15.0138 260.7393 31.3761 264 48.5 264 Z"/>
                            <path id="cu_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 48.5 264 C 121.4016 264 180.5 204.9016 180.5 132 C 180.5 59.0984 121.4016 0 48.5 0 C 31.566 0 15.3768 3.1888 0.5 8.9986 C 49.6683 28.2002 84.5 76.0324 84.5 132 C 84.5 187.9675 49.6683 235.7997 0.5 255.0014 C 0.3332 254.9362 0.1665 254.8708 0 254.805 C 15.0138 260.7393 31.3761 264 48.5 264 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:183.380;h:264">
                        <g id="cu_cu_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:183.380;h:264">
                            <path id="cu_2" fill="#fefbdb" d="M 180 8.9985 C 130.8317 28.2002 96 76.0324 96 132 C 96 187.9675 130.8317 235.7997 180 255.0014 C 181.1345 254.5583 182.2614 254.1001 183.3804 253.6268 C 167.5881 260.3063 150.2254 264 132 264 C 59.0984 264 0 204.9016 0 132 C 0 59.0984 59.0984 0 132 0 C 148.934 0 165.1233 3.1887 180 8.9985 Z"/>
                            <path id="cu_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 180 8.9985 C 130.8317 28.2002 96 76.0324 96 132 C 96 187.9675 130.8317 235.7997 180 255.0014 C 181.1345 254.5583 182.2614 254.1001 183.3804 253.6268 C 167.5881 260.3063 150.2254 264 132 264 C 59.0984 264 0 204.9016 0 132 C 0 59.0984 59.0984 0 132 0 C 148.934 0 165.1233 3.1887 180 8.9985 Z"/>
                        </g>
                    </g>
                    <rect id="bt-cc-add-4" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:-6;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, -6)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MAX;counter:CENTER" data-position="x:36;y:12;w:192;h:84" transform="translate(36, 12)">
                    <g id="column-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:6;primary:MAX;counter:MIN" data-position="x:0;y:0;w:192;h:84">
                        <rect id="column-1-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:84" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="round" width="192" height="84" rx="0" ry="0"/>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MAX;counter:MIN" data-position="x:12;y:12;w:168;h:60" transform="translate(12, 12)">
                            <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                    </g>
                    <g id="curve-with-terminator" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:94.734;y:84;w:50.531;h:207" transform="translate(94.734375, 84)">
                        <path id="curve" marker-end="url(#arrow)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0312 0 C 0.0312 47.3267 -0.4688 69.5 2.533 98 C 6.0089 131 17.0312 180 50.5312 207" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50.531;h:207"/>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:STRETCH;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MAX;counter:CENTER" data-position="x:516;y:12;w:192;h:84" transform="translate(516, 12)">
                    <g id="column-2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:6;primary:MIN;counter:MIN" data-position="x:0;y:0;w:192;h:84">
                        <rect id="column-2-bg" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:84" stroke="#db8333" fill="#fef2e6" stroke-width="2" stroke-linejoin="round" width="192" height="84" rx="0" ry="0"/>
                        <g id="text-3_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:12;y:12;w:168;h:60" transform="translate(12, 12)">
                            <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                        </g>
                    </g>
                    <g id="curve-with-terminator_1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:45.734;y:84;w:50.531;h:207" transform="translate(45.734375, 84)">
                        <path id="curve_1" marker-end="url(#arrow)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 50.5 0 C 50.4999 47.3267 51 69.5 47.9981 98 C 44.5223 131 33.5 180 0 207" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:50.531;h:207"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:312;w:48;h:48" fill="#33de7b1a" transform="translate(216, 312)">
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:480;y:312;w:48;h:48" fill="#33de7b1a" transform="translate(480, 312)">
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="tx-cc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:300;y:330;w:144;h:24" fill="#ff00001a" transform="translate(300, 330)">
                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                </g>
            </g>
        </g>
    </g>
    <defs >
        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">
            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"/>
        </marker>
    </defs>
</svg>