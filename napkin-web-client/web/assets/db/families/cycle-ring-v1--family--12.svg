<svg xmlns="http://www.w3.org/2000/svg" width="996" height="816">
    <g id="arrows-ring-v1--family--12" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L996 0 L996 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:996;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:996;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:996;h:768">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:66.001;w:636.000;h:636.000" transform="translate(180, 66.0009765625)">
                    <g id="g-12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:172.549;y:0;w:163.451;h:121.530" transform="translate(172.548828125, 0)">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:163.451;h:121.530">
                            <path id="Vector" fill="#fef2e6" d="M 151.4507 108 L 163.4507 54 L 151.4507 0 L 127.4507 6.1943e-7 L 130.2002 12.3728 L 139.4507 54 L 129.9999 96.5287 L 127.4507 108 L 151.4507 108 Z M 139.4507 54 L 130.2002 12.3728 C 83.2386 14.6778 39.0598 27.5695 0 48.7115 L 29.0388 80.3698 L 41.9937 121.5296 C 68.5922 107.4942 98.3861 98.7021 129.9999 96.5287 L 139.4507 54 Z"/>
                            <path id="Vector_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 151.4507 108 L 163.4507 54 L 151.4507 0 L 127.4507 6.1943e-7 L 130.2002 12.3728 L 139.4507 54 L 129.9999 96.5287 L 127.4507 108 L 151.4507 108 Z M 139.4507 54 L 130.2002 12.3728 C 83.2386 14.6778 39.0598 27.5695 0 48.7115 L 29.0388 80.3698 L 41.9937 121.5296 C 68.5922 107.4942 98.3861 98.7021 129.9999 96.5287 L 139.4507 54 Z"/>
                        </g>
                    </g>
                    <g id="g-11" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:57.407;y:39.604;w:160.788;h:159.988" transform="translate(57.4072265625, 39.6043701171875)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:160.788;h:159.988">
                            <path id="Vector_2" fill="#fefbdb" d="M 160.7881 93.5307 L 144.1804 40.7654 L 106.788 0 L 86.0034 12 L 94.5508 21.3184 L 123.3957 52.7654 L 136.4713 94.3086 L 140.0034 105.5307 L 160.7881 93.5307 Z M 0 117.9159 L 40.9611 130.8083 L 72.7726 159.9875 C 89.233 133.9323 110.9655 111.5398 136.4713 94.3086 L 123.3957 52.7654 L 94.5508 21.3184 C 56.3112 46.0676 23.9443 79.1168 0 117.9159 Z"/>
                            <path id="Vector_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 160.7881 93.5307 L 144.1804 40.7654 L 106.788 0 L 86.0034 12 L 94.5508 21.3184 L 123.3957 52.7654 L 136.4713 94.3086 L 140.0034 105.5307 L 160.7881 93.5307 Z M 0 117.9159 L 40.9611 130.8083 L 72.7726 159.9875 C 89.233 133.9323 110.9655 111.5398 136.4713 94.3086 L 123.3957 52.7654 L 94.5508 21.3184 C 56.3112 46.0676 23.9443 79.1168 0 117.9159 Z"/>
                        </g>
                    </g>
                    <g id="g-10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12.121;y:153.805;w:127.013;h:155.564" transform="translate(12.12109375, 153.8050537109375)">
                        <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:127.013;h:155.564">
                            <path id="Vector_4" fill="#f2fae1" d="M 127.0125 54 L 86.2472 16.6077 L 33.4818 0 L 21.4818 20.7846 L 33.5166 24.5725 L 74.2472 37.3923 L 106.3216 66.8128 L 115.0125 74.7846 L 127.0125 54 Z M 0 155.5008 L 41.8789 146.1944 L 84.0435 155.5643 C 85.257 123.8191 93.1363 93.782 106.3216 66.8128 L 74.2472 37.3923 L 33.5166 24.5725 C 13.2571 64.0135 1.3132 108.4281 0 155.5008 Z"/>
                            <path id="Vector_5" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 127.0125 54 L 86.2472 16.6077 L 33.4818 0 L 21.4818 20.7846 L 33.5166 24.5725 L 74.2472 37.3923 L 106.3216 66.8128 L 115.0125 74.7846 L 127.0125 54 Z M 0 155.5008 L 41.8789 146.1944 L 84.0435 155.5643 C 85.257 123.8191 93.1363 93.782 106.3216 66.8128 L 74.2472 37.3923 L 33.5166 24.5725 C 13.2571 64.0135 1.3132 108.4281 0 155.5008 Z"/>
                        </g>
                    </g>
                    <g id="g-9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:299.999;w:121.530;h:163.450" transform="translate(0, 299.9993896484375)">
                        <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:121.530;h:163.450">
                            <path id="Vector_6" fill="#e7fbf2" d="M 0 12 L 54 0 L 108 12 L 108 36 L 96.5293 33.451 L 54 24 L 12.3734 33.2504 L 0 36 L 0 12 Z M 48.7114 163.45 L 80.3693 134.4116 L 121.5296 121.4565 C 107.4946 94.8581 98.7026 65.0645 96.5293 33.451 L 54 24 L 12.3734 33.2504 C 14.6783 80.2117 27.5697 124.3903 48.7114 163.45 Z"/>
                            <path id="Vector_7" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 12 L 54 0 L 108 12 L 108 36 L 96.5293 33.451 L 54 24 L 12.3734 33.2504 L 0 36 L 0 12 Z M 48.7114 163.45 L 80.3693 134.4116 L 121.5296 121.4565 C 107.4946 94.8581 98.7026 65.0645 96.5293 33.451 L 54 24 L 12.3734 33.2504 C 14.6783 80.2117 27.5697 124.3903 48.7114 163.45 Z"/>
                        </g>
                    </g>
                    <g id="g-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:39.604;y:417.803;w:159.987;h:160.788" transform="translate(39.603515625, 417.80322265625)">
                        <g id="cu_Vector_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:159.987;h:160.788">
                            <path id="Vector_8" fill="#e8f9ff" d="M 0 54 L 40.7654 16.6077 L 93.5307 0 L 105.5307 20.7846 L 94.309 24.3166 L 52.7654 37.3923 L 21.3187 66.237 L 12 74.7846 L 0 54 Z M 117.9148 160.7876 L 130.8069 119.8272 L 159.9867 88.0152 C 133.9319 71.5546 111.5399 49.8223 94.309 24.3166 L 52.7654 37.3923 L 21.3187 66.237 C 46.0676 104.4764 79.1162 136.8432 117.9148 160.7876 Z"/>
                            <path id="Vector_9" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 54 L 40.7654 16.6077 L 93.5307 0 L 105.5307 20.7846 L 94.309 24.3166 L 52.7654 37.3923 L 21.3187 66.237 L 12 74.7846 L 0 54 Z M 117.9148 160.7876 L 130.8069 119.8272 L 159.9867 88.0152 C 133.9319 71.5546 111.5399 49.8223 94.309 24.3166 L 52.7654 37.3923 L 21.3187 66.237 C 46.0676 104.4764 79.1162 136.8432 117.9148 160.7876 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:153.803;y:496.865;w:155.566;h:127.013" transform="translate(153.802734375, 496.864990234375)">
                        <g id="cu_Vector_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:155.566;h:127.013">
                            <path id="Vector_10" fill="#edf4ff" d="M 0 93.5307 L 16.6077 40.7654 L 54 0 L 74.7846 12 L 66.8131 20.6906 L 37.3923 52.7654 L 24.5727 93.4954 L 20.7846 105.5307 L 0 93.5307 Z M 146.1963 85.1349 L 155.5664 42.9696 C 123.8204 41.7561 93.7828 33.8765 66.8131 20.6906 L 37.3923 52.7654 L 24.5727 93.4954 C 64.0141 113.7555 108.4292 125.6999 155.5026 127.0132 L 146.1963 85.1349 Z"/>
                            <path id="Vector_11" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 93.5307 L 16.6077 40.7654 L 54 0 L 74.7846 12 L 66.8131 20.6906 L 37.3923 52.7654 L 24.5727 93.4954 L 20.7846 105.5307 L 0 93.5307 Z M 146.1963 85.1349 L 155.5664 42.9696 C 123.8204 41.7561 93.7828 33.8765 66.8131 20.6906 L 37.3923 52.7654 L 24.5727 93.4954 C 64.0141 113.7555 108.4292 125.6999 155.5026 127.0132 L 146.1963 85.1349 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:299.999;y:514.470;w:163.449;h:121.530" transform="translate(299.9990234375, 514.4703369140625)">
                        <g id="cu_Vector_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:163.449;h:121.530">
                            <path id="Vector_12" fill="#f3f0ff" d="M 12 121.5296 L 0 67.5296 L 12 13.5296 L 36 13.5296 L 33.4511 24.9997 L 24 67.5296 L 33.2502 109.1556 L 36 121.5296 L 12 121.5296 Z M 134.4111 41.1608 L 121.4559 0 C 94.8577 14.0348 65.0643 22.8264 33.4511 24.9997 L 24 67.5296 L 33.2502 109.1556 C 80.2113 106.8509 124.3897 93.9597 163.4492 72.8183 L 134.4111 41.1608 Z"/>
                            <path id="Vector_13" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 12 121.5296 L 0 67.5296 L 12 13.5296 L 36 13.5296 L 33.4511 24.9997 L 24 67.5296 L 33.2502 109.1556 L 36 121.5296 L 12 121.5296 Z M 134.4111 41.1608 L 121.4559 0 C 94.8577 14.0348 65.0643 22.8264 33.4511 24.9997 L 24 67.5296 L 33.2502 109.1556 C 80.2113 106.8509 124.3897 93.9597 163.4492 72.8183 L 134.4111 41.1608 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:417.803;y:436.410;w:160.788;h:159.986" transform="translate(417.802734375, 436.4102783203125)">
                        <g id="cu_Vector_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:160.788;h:159.986">
                            <path id="Vector_14" fill="#faf0ff" d="M 54 159.9863 L 16.6077 119.2209 L 0 66.4556 L 20.7846 54.4556 L 24.3165 65.677 L 37.3923 107.2209 L 66.2367 138.6673 L 74.7846 147.9863 L 54 159.9863 Z M 119.8267 29.1794 L 88.0152 0 C 71.5545 26.0545 49.8221 48.4463 24.3165 65.677 L 37.3923 107.2209 L 66.2367 138.6673 C 104.4762 113.9186 136.8432 80.8701 160.7878 42.0717 L 119.8267 29.1794 Z"/>
                            <path id="Vector_15" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 54 159.9863 L 16.6077 119.2209 L 0 66.4556 L 20.7846 54.4556 L 24.3165 65.677 L 37.3923 107.2209 L 66.2367 138.6673 L 74.7846 147.9863 L 54 159.9863 Z M 119.8267 29.1794 L 88.0152 0 C 71.5545 26.0545 49.8221 48.4463 24.3165 65.677 L 37.3923 107.2209 L 66.2367 138.6673 C 104.4762 113.9186 136.8432 80.8701 160.7878 42.0717 L 119.8267 29.1794 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:496.864;y:326.629;w:127.015;h:155.568" transform="translate(496.8642578125, 326.62939453125)">
                        <g id="cu_Vector_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:127.015;h:155.568">
                            <path id="Vector_16" fill="#feecf7" d="M 93.5307 155.5678 L 40.7654 138.9601 L 0 101.5678 L 12 80.7832 L 20.6911 88.7552 L 52.7654 118.1755 L 93.4961 130.9954 L 105.5307 134.7832 L 93.5307 155.5678 Z M 85.1357 9.3699 L 42.971 0 C 41.7575 31.7467 33.8776 61.7851 20.6911 88.7552 L 52.7654 118.1755 L 93.4961 130.9954 C 113.7567 91.5535 125.7014 47.1377 127.0146 0.0635 L 85.1357 9.3699 Z"/>
                            <path id="Vector_17" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 93.5307 155.5678 L 40.7654 138.9601 L 0 101.5678 L 12 80.7832 L 20.6911 88.7552 L 52.7654 118.1755 L 93.4961 130.9954 L 105.5307 134.7832 L 93.5307 155.5678 Z M 85.1357 9.3699 L 42.971 0 C 41.7575 31.7467 33.8776 61.7851 20.6911 88.7552 L 52.7654 118.1755 L 93.4961 130.9954 C 113.7567 91.5535 125.7014 47.1377 127.0146 0.0635 L 85.1357 9.3699 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:514.471;y:172.549;w:121.530;h:163.450" transform="translate(514.470703125, 172.5494384765625)">
                        <g id="cu_Vector_9" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:121.530;h:163.450">
                            <path id="Vector_18" fill="#ffedeb" d="M 13.5296 151.4499 L 67.5296 163.4499 L 121.5296 151.4499 L 121.5296 127.4499 L 109.1562 130.1995 L 67.5296 139.4499 L 25.0003 129.9989 L 13.5296 127.4499 L 13.5296 151.4499 Z M 41.1603 29.0384 L 0 41.9934 C 14.035 68.5918 22.827 98.3855 25.0003 129.9989 L 67.5296 139.4499 L 109.1562 130.1995 C 106.8513 83.2382 93.9598 39.0596 72.8182 0 L 41.1603 29.0384 Z"/>
                            <path id="Vector_19" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 13.5296 151.4499 L 67.5296 163.4499 L 121.5296 151.4499 L 121.5296 127.4499 L 109.1562 130.1995 L 67.5296 139.4499 L 25.0003 129.9989 L 13.5296 127.4499 L 13.5296 151.4499 Z M 41.1603 29.0384 L 0 41.9934 C 14.035 68.5918 22.827 98.3855 25.0003 129.9989 L 67.5296 139.4499 L 109.1562 130.1995 C 106.8513 83.2382 93.9598 39.0596 72.8182 0 L 41.1603 29.0384 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:436.409;y:57.407;w:159.987;h:160.788" transform="translate(436.4091796875, 57.4072265625)">
                        <g id="cu_Vector_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:159.987;h:160.788">
                            <path id="Vector_20" fill="#fef2e6" d="M 66.4565 160.7883 L 119.2219 144.1806 L 159.9872 106.7883 L 147.9872 86.0037 L 138.6685 94.5513 L 107.2219 123.396 L 65.6783 136.4716 L 54.4565 140.0037 L 66.4565 160.7883 Z M 29.1788 40.9617 L 0 72.7727 C 26.0549 89.2333 48.4472 110.9658 65.6783 136.4716 L 107.2219 123.396 L 138.6685 94.5513 C 113.9194 56.3116 80.8704 23.9444 42.0714 0 L 29.1788 40.9617 Z"/>
                            <path id="Vector_21" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 66.4565 160.7883 L 119.2219 144.1806 L 159.9872 106.7883 L 147.9872 86.0037 L 138.6685 94.5513 L 107.2219 123.396 L 65.6783 136.4716 L 54.4565 140.0037 L 66.4565 160.7883 Z M 29.1788 40.9617 L 0 72.7727 C 26.0549 89.2333 48.4472 110.9658 65.6783 136.4716 L 107.2219 123.396 L 138.6685 94.5513 C 113.9194 56.3116 80.8704 23.9444 42.0714 0 L 29.1788 40.9617 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:326.630;y:12.120;w:155.566;h:127.014" transform="translate(326.6298828125, 12.1204833984375)">
                        <g id="cu_Vector_11" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:155.566;h:127.014">
                            <path id="Vector_22" fill="#fefbdb" d="M 101.5657 127.0139 L 138.958 86.2485 L 155.5657 33.4832 L 134.7811 21.4832 L 130.9934 33.5173 L 118.1734 74.2485 L 88.7535 106.3224 L 80.7811 115.0139 L 101.5657 127.0139 Z M 0.0632 0 L 9.3698 41.8795 L 0 84.0435 C 31.746 85.257 61.7838 93.1365 88.7535 106.3224 L 118.1734 74.2485 L 130.9934 33.5173 C 91.5519 13.2573 47.1367 1.3131 0.0632 0 Z"/>
                            <path id="Vector_23" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 101.5657 127.0139 L 138.958 86.2485 L 155.5657 33.4832 L 134.7811 21.4832 L 130.9934 33.5173 L 118.1734 74.2485 L 88.7535 106.3224 L 80.7811 115.0139 L 101.5657 127.0139 Z M 0.0632 0 L 9.3698 41.8795 L 0 84.0435 C 31.746 85.257 61.7838 93.1365 88.7535 106.3224 L 118.1734 74.2485 L 130.9934 33.5173 C 91.5519 13.2573 47.1367 1.3131 0.0632 0 Z"/>
                        </g>
                    </g>
                </g>
                <g id="ic-cc-12" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:405;y:103;w:48;h:48" fill="#33de7b1a" transform="translate(405, 103)">
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-11" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:286;y:173;w:48;h:48" fill="#33de7b1a" transform="translate(286, 173)">
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-10" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:220;y:294;w:48;h:48" fill="#33de7b1a" transform="translate(220, 294)">
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-9" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:222;y:432;w:48;h:48" fill="#33de7b1a" transform="translate(222, 432)">
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-8" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:548;w:48;h:48" fill="#33de7b1a" transform="translate(288, 548)">
                    <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:406;y:616;w:48;h:48" fill="#33de7b1a" transform="translate(406, 616)">
                    <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-6" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:543;y:616;w:48;h:48" fill="#33de7b1a" transform="translate(543, 616)">
                    <g id="icon_12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_13" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:660;y:548;w:48;h:48" fill="#33de7b1a" transform="translate(660, 548)">
                    <g id="icon_14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_15" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:728;y:432;w:48;h:48" fill="#33de7b1a" transform="translate(728, 432)">
                    <g id="icon_16" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_17" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:731;y:294;w:48;h:48" fill="#33de7b1a" transform="translate(731, 294)">
                    <g id="icon_18" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_19" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:663;y:173;w:48;h:48" fill="#33de7b1a" transform="translate(663, 173)">
                    <g id="icon_20" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_21" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:542;y:103;w:48;h:48" fill="#33de7b1a" transform="translate(542, 103)">
                    <g id="icon_22" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_23" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:828;y:288;w:168;h:60" transform="translate(828, 288)">
                    <g id="tx-lc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-10" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:288;w:168;h:60" transform="translate(0, 288)">
                    <g id="tx-rc-10" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-10-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-10" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:588;y:12;w:168;h:60" transform="translate(588, 12)">
                    <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-12" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:240;y:12;w:168;h:60" transform="translate(240, 12)">
                    <g id="tx-rc-12" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-12-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-12" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:588;y:696;w:168;h:60" transform="translate(588, 696)">
                    <g id="tx-lc-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:240;y:697;w:168;h:60" transform="translate(240, 697)">
                    <g id="tx-rc-7" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-7-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-7" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:768;y:144;w:168;h:60" transform="translate(768, 144)">
                    <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-2" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:60;y:144;w:168;h:60" transform="translate(60, 144)">
                    <g id="tx-rc-11" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-11-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-11" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:756;y:564;w:168;h:60" transform="translate(756, 564)">
                    <g id="tx-lc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:60;y:564;w:168;h:60" transform="translate(60, 564)">
                    <g id="tx-rc-8" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_18" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-8-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_19" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-8" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:828;y:432;w:168;h:60" transform="translate(828, 432)">
                    <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_20" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_21" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-4" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:432;w:168;h:60" transform="translate(0, 432)">
                    <g id="tx-rc-9" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_22" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-9-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_23" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-9" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>