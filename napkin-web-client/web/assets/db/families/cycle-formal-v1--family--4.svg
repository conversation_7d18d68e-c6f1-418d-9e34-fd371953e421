<svg xmlns="http://www.w3.org/2000/svg" width="744" height="468">    <g id="cycle-formal-v1--family--4">        <g id="lines">            <g id="g-4">                <g id="cu" >                    <path id="arrow" transform="translate(272.4781494140625, 212.73509216308594)" fill="#fef2e6" d="M28.4874 122.598 L22.9573 126.889 L20.8114 124.124 C-5.1659 90.6505 -6.8232 44.4215 15.8346 9.3194 L12.0214 6.5317 L26.8089 0 L25.0717 16.0722 L21.4958 13.458 C0.6683 46.0414 2.2851 88.8342 26.3416 119.833 L28.4874 122.598 Z"></path>
                    <path id="arrow_1" transform="translate(272.4781494140625, 212.73509216308594)" fill="none" stroke="#db8333" stroke-width="1" stroke-linejoin="miter" stroke-linecap="arrow_equilateral" stroke-miterlimit="4"  d="M 28.4874 122.5979 L 22.9573 126.8895 L 20.8114 124.1244 C -5.1659 90.6505 -6.8232 44.4215 15.8346 9.3194 L 12.0214 6.5317 L 26.8089 0 L 25.0717 16.0722 L 21.4958 13.458 C 0.6683 46.0414 2.2851 88.8342 26.3416 119.8328 L 28.4874 122.5979 Z"></path></g></g>
            <g id="g-3">                <g id="cu_1" >                    <path id="arrow_2" transform="translate(308.7681655883789, 347.063720703125)" fill="#f2fae1" d="M122.598 0 L126.889 5.5301 L124.124 7.6759 C90.6505 33.6533 44.4215 35.3106 9.3194 12.6528 L6.5317 16.466 L0 1.6785 L16.0722 3.4156 L13.458 6.9915 C46.0414 27.8191 88.8342 26.2022 119.833 2.1458 L122.598 0 Z"></path>
                    <path id="arrow_3" transform="translate(308.7681655883789, 347.063720703125)" fill="none" stroke="#93c332" stroke-width="1" stroke-linejoin="miter" stroke-linecap="arrow_equilateral" stroke-miterlimit="4"  d="M 122.5979 0 L 126.8895 5.5301 L 124.1244 7.6759 C 90.6505 33.6533 44.4215 35.3106 9.3194 12.6528 L 6.5317 16.466 L 0 1.6785 L 16.0722 3.4156 L 13.458 6.9915 C 46.0414 27.8191 88.8342 26.2022 119.8328 2.1458 L 122.5979 0 Z"></path></g></g>
            <g id="g-2">                <g id="cu_2" >                    <path id="arrow_4" transform="translate(442.9957275390625, 212.27415466308594)" fill="#feecf7" d="M16.4584 120.358 L1.6705 126.889 L3.4086 110.817 L6.9844 113.431 C27.8142 80.8492 26.1997 38.0563 2.1456 7.0564 L0 4.2912 L5.5304 0 L7.676 2.7652 C33.6508 36.2404 35.3056 82.4695 12.6454 117.57 L16.4584 120.358 Z"></path>
                    <path id="arrow_5" transform="translate(442.9957275390625, 212.27415466308594)" fill="none" stroke="#d95da7" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 16.4584 120.3583 L 1.6705 126.8891 L 3.4086 110.817 L 6.9844 113.4314 C 27.8142 80.8491 26.1997 38.0563 2.1456 7.0564 L 0 4.2912 L 5.5304 0 L 7.676 2.7652 C 33.6508 36.2404 35.3056 82.4694 12.6454 117.5704 L 16.4584 120.3583 Z"></path></g></g>
            <g id="g-1">                <g id="cu_3" >                    <path id="arrow_6" transform="translate(308.30472564697266, 176.5501251220703)" fill="#f3f0ff" d="M120.358 12.025 L126.889 26.8129 L110.817 25.0748 L113.431 21.4991 C80.8492 0.6692 38.0563 2.2837 7.0564 26.3378 L4.2912 28.4834 L0 22.953 L2.7652 20.8074 C36.2404 -5.1674 82.4694 -6.8222 117.57 15.838 L120.358 12.025 Z"></path>
                    <path id="arrow_7" transform="translate(308.30472564697266, 176.5501251220703)" fill="none" stroke="#7e62ec" stroke-width="1" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 120.3583 12.025 L 126.8891 26.8129 L 110.817 25.0748 L 113.4314 21.4991 C 80.8491 0.6692 38.0563 2.2837 7.0564 26.3378 L 4.2912 28.4834 L 0 22.953 L 2.7652 20.8074 C 36.2404 -5.1674 82.4694 -6.8222 117.5704 15.838 L 120.3583 12.025 Z"></path></g></g></g>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 108, 240)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-3" transform="translate(300, 396)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 492, 240)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-cb-1" transform="translate(300, 84)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="translate(292, 196)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="translate(292, 332)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="translate(428, 332)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="translate(428, 196)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="translate(360, 360)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="translate(456, 263.99951171875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="translate(360, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="tx-cb-title">            <path id="rect" transform="translate(72, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
            <text id="Label" fill="#484848" transform="translate(323.7861328125, 16)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="translate(264, 264)" width="24" height="24" rx="0" ry="0"></rect></g></svg>