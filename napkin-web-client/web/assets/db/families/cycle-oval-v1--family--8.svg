<svg xmlns="http://www.w3.org/2000/svg" width="984" height="1008">    <g id="cycle-oval-v1--family--8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:6506;y:11569.193;w:984;h:1008">        <g id="lines"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:252;y:83.808;w:480.000;h:912.193" transform="translate(252, 83.8076171875)">            <g id="g-8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:24.000;y:0;w:264.000;h:279.429" transform="translate(24.000009536743164, 0)">                <g id="cu_Vector"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:-0.000;y:0.000;w:264.000;h:279.429" >                    <path id="Vector" transform="translate(-0.0000209808349609375, 0.0001005170852295123)" fill="#e8f9ff" d="M3.447e-6 240 L0 279.429 L60 228 L120 279.429 L120 240 C120 195.268 150.595 157.681 192 147.024 L192 168 L264 84 L192 0 L192 25.3182 C84.0012 37.2569 1.3167e-5 128.819 3.447e-6 240 Z"></path>
                    <path id="Vector_1" transform="translate(-0.0000209808349609375, 0.0001005170852295123)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 239.9999 L 0 279.4286 L 60 228 L 120 279.4286 L 120 239.9999 C 120 195.2678 150.5946 157.6814 192 147.0244 L 192 168 L 264 84 L 192 0 L 192 25.3182 C 84.0012 37.2569 0 128.8187 0 239.9999 Z"></path></g></g>
            <g id="g-7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:227.999;w:168;h:243.429" transform="translate(0, 227.9989471435547)">                <g id="cu_Vector_1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:168;h:243.429" >                    <path id="Vector_2" fill="#edf4ff" d="M24 51.4286 L0 72 L24 72 L24 243.429 L84 192 L144 243.429 L144 72 L168 72 L144 51.4286 L84 0 L24 51.4286 Z"></path>
                    <path id="Vector_3" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 72 L 24 72 L 24 243.4286 L 84 192 L 144 243.4286 L 144 72 L 168 72 L 84 0 L 0 72 Z"></path></g></g>
            <g id="g-6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:419.999;w:168;h:243.429" transform="translate(0, 419.9989318847656)">                <g id="cu_Vector_2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:168;h:243.429" >                    <path id="Vector_4" fill="#f3f0ff" d="M24 243.429 L84 192 L144 243.429 L144 72 L168 72 L144 51.4286 L84 0 L24 51.4286 L0 72 L24 72 L24 243.429 Z"></path>
                    <path id="Vector_5" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 24 243.4286 L 84 192 L 144 243.4286 L 144 72 L 168 72 L 84 0 L 0 72 L 24 72 L 24 243.4286 Z"></path></g></g>
            <g id="g-5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:612.192;w:243.482;h:276" transform="translate(0, 612.1923828125)">                <g id="cu_Vector_3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:243.482;h:276" >                    <path id="Vector_6" fill="#faf0ff" d="M192 216 L243.482 155.938 C242.326 155.979 241.166 156 240 156 C191.045 156 150.648 119.356 144.743 72 L168 72 L144 51.4286 L84 0 L24 51.4286 L0 72 L24.3277 72 C30.5542 185.712 124.733 276 240 276 C241.137 276 242.273 275.991 243.406 275.974 L192 216 Z"></path>
                    <path id="Vector_7" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 216 L 243.4816 155.9381 C 242.3262 155.9793 241.1655 156.0001 240 156.0001 C 191.0446 156.0001 150.6479 119.3558 144.7427 72 L 168 72 L 84 0 L 0 72 L 24.3277 72 C 30.5542 185.7119 124.7332 276 240 276 C 241.1374 276 242.2728 275.9911 243.406 275.9736 L 192 216 Z"></path></g></g>
            <g id="g-4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:632.765;w:264;h:279.429" transform="translate(192, 632.7646484375)">                <g id="cu_Vector_4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:264;h:279.429" >                    <path id="Vector_8" fill="#faf0ff" d="M264 39.4286 L264 0 L204 51.4286 L144 0 L144 39.4286 C144 84.1608 113.405 121.747 72 132.404 L72 111.429 L51.4816 135.367 L0 195.429 L51.406 255.402 L72 279.429 L72 254.11 C179.999 242.172 264 150.61 264 39.4286 Z"></path>
                    <path id="Vector_9" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 264 39.4286 L 264 0 L 204 51.4286 L 144 0 L 144 39.4286 C 144 84.1608 113.4054 121.7472 72 132.4042 L 72 111.4286 L 0 195.4286 L 72 279.4286 L 72 254.1104 C 179.9988 242.1717 264 150.6099 264 39.4286 Z"></path></g></g>
            <g id="g-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:312.000;y:440.570;w:168;h:243.429" transform="translate(311.99993896484375, 440.5702209472656)">                <g id="cu_Vector_5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:168;h:243.429" >                    <path id="Vector_10" fill="#ffedeb" d="M144 0 L84 51.4286 L24 0 L24 171.429 L0 171.429 L24 192 L84 243.429 L144 192 L168 171.429 L144 171.429 L144 0 Z"></path>
                    <path id="Vector_11" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 144 0 L 84 51.4286 L 24 0 L 24 171.4286 L 0 171.4286 L 84 243.4286 L 168 171.4286 L 144 171.4286 L 144 0 Z"></path></g></g>
            <g id="g-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:312.000;y:247.335;w:168;h:244.664" transform="translate(311.99993896484375, 247.33485412597656)">                <g id="cu_Vector_6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:168;h:244.664" >                    <path id="Vector_12" fill="#fef2e6" d="M84 52.6638 L22.5589 0 C23.5061 5.4122 24 10.9803 24 16.6638 L24 172.664 L0 172.664 L24 193.235 L84 244.664 L144 193.235 L168 172.664 L144 172.664 L144 16.6638 C144 11.6251 143.827 6.6267 143.488 1.6741 L84 52.6638 Z"></path>
                    <path id="Vector_13" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 84 52.6638 L 22.5589 0 C 23.5061 5.4122 24 10.9803 24 16.6638 L 24 172.6638 L 0 172.6638 L 84 244.6638 L 168 172.6638 L 144 172.6638 L 144 16.6638 C 144 11.6251 143.8275 6.6266 143.488 1.6741 L 84 52.6638 Z"></path></g></g>
            <g id="g-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:236.518;y:23.999;w:243.482;h:276.000" transform="translate(236.51829528808594, 23.998910903930664)">                <g id="cu_Vector_7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:-0.000;w:243.482;h:276" >                    <path id="Vector_14" transform="translate(0.00021199679758865386, -0.00002670351204869803)" fill="#fefbdb" d="M51.4816 60 L0 120.062 C1.1555 120.021 2.3161 120 3.4816 120 C52.4371 120 92.8338 156.644 98.7389 204 L75.4816 204 L159.482 276 L243.482 204 L219.154 204 C212.927 90.2881 118.748 1.0077e-5 3.4816 0 C2.3442 -9.9436e-8 1.2089 0.0089 0.0756 0.0264 L51.4816 60 Z"></path>
                    <path id="Vector_15" transform="translate(0.00021199679758865386, -0.00002670351204869803)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 51.4816 60 L 0 120.0619 C 1.1555 120.0207 2.3161 119.9999 3.4816 119.9999 C 52.4371 119.9999 92.8338 156.6442 98.7389 204 L 75.4816 204 L 159.4816 276 L 243.4816 204 L 219.1539 204 C 212.9274 90.2881 118.7485 0 3.4816 8.3789e-13 C 2.3442 -9.9435e-8 1.2089 0.0089 0.0756 0.0264 L 51.4816 60 Z"></path></g></g></g>
        <g id="tx-cb-title"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-0.193;w:816;h:48" transform="translate(84, -0.193359375)">            <path id="rect" fill="#ff00001a" d="M0 0 L816 0 L816 48 L0 48 L0 0 Z"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:816;h:48"></path>
            <text id="Label"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:358;y:16;w:87.500;h:16" fill="#484848" transform="translate(358, 16)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g>
        <rect id="ic-cc-8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:348;y:203.807;w:48;h:48" fill="#33de7b1a" transform="translate(348, 203.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:312;y:419.807;w:48;h:48" fill="#33de7b1a" transform="translate(312, 419.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:312;y:611.807;w:48;h:48" fill="#33de7b1a" transform="translate(312, 611.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:348;y:840;w:48;h:48" fill="#33de7b1a" transform="translate(348, 840)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:588;y:840;w:48;h:48" fill="#33de7b1a" transform="translate(588, 840)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:624;y:611.807;w:48;h:48" fill="#33de7b1a" transform="translate(624, 611.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:624;y:419.807;w:48;h:48" fill="#33de7b1a" transform="translate(624, 419.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:588;y:203.807;w:48;h:48" fill="#33de7b1a" transform="translate(588, 203.806640625)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:732;y:215.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 732, 215.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:732;y:431.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 732, 431.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:732;y:623.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 732, 623.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:732;y:815.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 732, 815.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:228;y:815.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 228, 815.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:228;y:623.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 228, 623.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:228;y:431.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 228, 431.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:228;y:215.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 228, 215.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:540;y:155.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 540, 155.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:636;y:371.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 371.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:636;y:563.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 563.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:636;y:755.807;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 755.806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:432;y:900;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 432, 900)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:324;y:684;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 324, 684)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:324;y:492;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 324, 492)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-8"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:324;y:300;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 324, 300)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-9"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:516;y:156;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 516, 156)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:768;y:197.807;w:192;h:60" transform="translate(768, 197.806640625)">            <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_1"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
            <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_2"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:LEFT;vertical:TOP"></text></g></g>
        <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:768;y:413.807;w:192;h:60" transform="translate(768, 413.806640625)">            <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_3"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
            <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_4"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:LEFT;vertical:TOP"></text></g></g>
        <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:768;y:605.807;w:192;h:60" transform="translate(768, 605.806640625)">            <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_5"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
            <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_6"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:LEFT;vertical:TOP"></text></g></g>
        <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:768;y:797.807;w:192;h:60" transform="translate(768, 797.806640625)">            <g id="tx-lt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_7"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
            <g id="tx-lt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_8"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:LEFT;vertical:TOP"></text></g></g>
        <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:24;y:797.807;w:192;h:60" transform="translate(24, 797.806640625)">            <g id="tx-rt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_9"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:RIGHT;vertical:TOP"></text></g>
            <g id="tx-rt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_10"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:RIGHT;vertical:TOP"></text></g></g>
        <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:24;y:605.807;w:192;h:60" transform="translate(24, 605.806640625)">            <g id="tx-rt-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_11"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:RIGHT;vertical:TOP"></text></g>
            <g id="tx-rt-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_12"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:RIGHT;vertical:TOP"></text></g></g>
        <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:24;y:413.807;w:192;h:60" transform="translate(24, 413.806640625)">            <g id="tx-rt-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_13"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:RIGHT;vertical:TOP"></text></g>
            <g id="tx-rt-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_14"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:RIGHT;vertical:TOP"></text></g></g>
        <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:24;y:197.807;w:192;h:60" transform="translate(24, 197.806640625)">            <g id="tx-rt-8" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_15"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:RIGHT;vertical:TOP"></text></g>
            <g id="tx-rt-8-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_16"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:RIGHT;vertical:TOP"></text></g></g></g></svg>