<svg xmlns="http://www.w3.org/2000/svg" width="960" height="264">
    <g id="list-boxes-v1--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L960 0 L960 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:960;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:960;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:960;h:216">
                <g id="tabs" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:144;primary:MIN;counter:MIN" data-position="x:24;y:84;w:432;h:48" transform="translate(24, 84)">
                    <g id="row-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                        <g id="g-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                            <path id="top-fill" transform="translate(0, -48)" fill="#fefbdb" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill" transform="translate(0, -12)" fill="#fefbdb" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill" transform="translate(0, 60)" fill="#fefbdb" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke" transform="translate(0, -48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke" transform="translate(0, -12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke" transform="translate(0, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="Frame_659" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="Frame_660" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="g-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:480;y:0;w:432;h:48" transform="translate(480, 0)">
                            <path id="top-fill_1" transform="translate(0, -48)" fill="#fef2e6" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_1" transform="translate(0, -12)" fill="#fef2e6" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_1" transform="translate(0, 60)" fill="#fef2e6" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_1" transform="translate(0, -48)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_1" transform="translate(0, -12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_1" transform="translate(0, 60)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="Frame_659_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="Frame_660_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                            <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-408;y:108;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -408, 108)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>