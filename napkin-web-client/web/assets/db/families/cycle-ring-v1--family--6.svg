<svg xmlns="http://www.w3.org/2000/svg" width="876" height="600">
    <g id="arrows-ring-v1--family--6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L876 0 L876 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:876;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:876;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:876;h:552">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:210;y:36;w:456.000;h:480.000" transform="translate(210, 36)">
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:35.012;y:0;w:210.988;h:160.662" transform="translate(35.012451171875, 0)">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210.988;h:160.662">
                            <path id="Vector" fill="#f3f0ff" d="M 198.9876 108 L 210.9876 54 L 198.9876 0 L 174.9876 6.1943e-7 L 177.7654 12.5001 L 186.9876 54 L 177.4707 96.8263 L 174.9876 108 L 198.9876 108 Z M 177.4707 96.8263 L 186.9876 54 L 177.7654 12.5001 C 102.8569 17.4379 37.8322 58.553 0 118.5367 L 40.9068 131.412 L 72.7957 160.6623 C 95.876 125.7674 133.7454 101.511 177.4707 96.8263 Z"/>
                            <path id="Vector_1" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 198.9876 108 L 210.9876 54 L 198.9876 0 L 174.9876 6.1943e-7 L 177.7654 12.5001 L 186.9876 54 L 177.4707 96.8263 L 174.9876 108 L 198.9876 108 Z M 177.4707 96.8263 L 186.9876 54 L 177.7654 12.5001 C 102.8569 17.4379 37.8322 58.553 0 118.5367 L 40.9068 131.412 L 72.7957 160.6623 C 95.876 125.7674 133.7454 101.511 177.4707 96.8263 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:114.805;w:116.685;h:231.607" transform="translate(0, 114.8046875)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:116.685;h:231.607">
                            <path id="Vector_2" fill="#faf0ff" d="M 116.6845 54 L 75.9192 16.6077 L 23.1538 0 L 11.1538 20.7846 L 23.327 24.6161 L 63.9192 37.3923 L 96.2306 67.0302 L 104.6845 74.7846 L 116.6845 54 Z M 99.1805 189.6209 C 89.4669 170.2361 84 148.3544 84 125.1957 C 84 104.4968 88.3673 84.8179 96.2306 67.0302 L 63.9192 37.3923 L 23.327 24.6161 C 8.3902 54.9539 0 89.095 0 125.1957 C 0 163.6289 9.5094 199.841 26.3036 231.6075 L 57.9194 202.6077 L 99.1805 189.6209 Z"/>
                            <path id="Vector_3" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 116.6845 54 L 75.9192 16.6077 L 23.1538 0 L 11.1538 20.7846 L 23.327 24.6161 L 63.9192 37.3923 L 96.2306 67.0302 L 104.6845 74.7846 L 116.6845 54 Z M 99.1805 189.6209 C 89.4669 170.2361 84 148.3544 84 125.1957 C 84 104.4968 88.3673 84.8179 96.2306 67.0302 L 63.9192 37.3923 L 23.327 24.6161 C 8.3902 54.9539 0 89.095 0 125.1957 C 0 163.6289 9.5094 199.841 26.3036 231.6075 L 57.9194 202.6077 L 99.1805 189.6209 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:17.154;y:300.805;w:202.236;h:167.033" transform="translate(17.154052734375, 300.8046875)">
                        <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:202.236;h:167.033">
                            <path id="Vector_4" fill="#feecf7" d="M 93.5307 0 L 40.7654 16.6077 L 0 54 L 12 74.7846 L 21.4254 66.1391 L 52.7654 37.3923 L 94.615 24.2203 L 105.5307 20.7846 L 93.5307 0 Z M 202.1431 167.0327 L 192.8459 125.1957 L 202.2355 82.9426 C 158.054 80.337 119.2292 57.8117 94.615 24.2203 L 52.7654 37.3923 L 21.4254 66.1391 C 60.8406 124.837 126.8174 164.2058 202.1431 167.0327 Z"/>
                            <path id="Vector_5" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 93.5307 0 L 40.7654 16.6077 L 0 54 L 12 74.7846 L 21.4254 66.1391 L 52.7654 37.3923 L 94.615 24.2203 L 105.5307 20.7846 L 93.5307 0 Z M 202.1431 167.0327 L 192.8459 125.1957 L 202.2355 82.9426 C 158.054 80.337 119.2292 57.8117 94.615 24.2203 L 52.7654 37.3923 L 21.4254 66.1391 C 60.8406 124.837 126.8174 164.2058 202.1431 167.0327 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:210;y:319.338;w:210.987;h:160.662" transform="translate(210, 319.337890625)">
                        <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:210.987;h:160.662">
                            <path id="Vector_6" fill="#ffedeb" d="M 12 160.6617 L 0 106.6617 L 12 52.6617 L 36 52.6617 L 33.517 63.8354 L 24 106.6617 L 33.2222 148.1616 L 36 160.6617 L 12 160.6617 Z M 170.0806 29.2505 L 138.1915 0 C 115.1112 34.8945 77.242 59.1507 33.517 63.8354 L 24 106.6617 L 33.2222 148.1616 C 108.1304 143.2238 173.1549 102.109 210.9872 42.1257 L 170.0806 29.2505 Z"/>
                            <path id="Vector_7" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 12 160.6617 L 0 106.6617 L 12 52.6617 L 36 52.6617 L 33.517 63.8354 L 24 106.6617 L 33.2222 148.1616 L 36 160.6617 L 12 160.6617 Z M 170.0806 29.2505 L 138.1915 0 C 115.1112 34.8945 77.242 59.1507 33.517 63.8354 L 24 106.6617 L 33.2222 148.1616 C 108.1304 143.2238 173.1549 102.109 210.9872 42.1257 L 170.0806 29.2505 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:339.315;y:133.589;w:116.685;h:231.608" transform="translate(339.315185546875, 133.5888671875)">
                        <g id="cu_Vector_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:116.685;h:231.608">
                            <path id="Vector_8" fill="#fef2e6" d="M 93.5307 231.6076 L 40.7654 214.9999 L 0 177.6076 L 12 156.823 L 20.4538 164.5773 L 52.7654 194.2153 L 93.3573 206.9914 L 105.5307 210.823 L 93.5307 231.6076 Z M 17.5047 41.9866 C 27.218 61.3712 32.6848 83.2527 32.6848 106.411 C 32.6848 127.1102 28.3174 146.7894 20.4538 164.5773 L 52.7654 194.2153 L 93.3573 206.9914 C 108.2944 176.6534 116.6848 142.512 116.6848 106.411 C 116.6848 67.9782 107.1755 31.7664 90.3815 0 L 58.7656 28.9999 L 17.5047 41.9866 Z"/>
                            <path id="Vector_9" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 93.5307 231.6076 L 40.7654 214.9999 L 0 177.6076 L 12 156.823 L 20.4538 164.5773 L 52.7654 194.2153 L 93.3573 206.9914 L 105.5307 210.823 L 93.5307 231.6076 Z M 17.5047 41.9866 C 27.218 61.3712 32.6848 83.2527 32.6848 106.411 C 32.6848 127.1102 28.3174 146.7894 20.4538 164.5773 L 52.7654 194.2153 L 93.3573 206.9914 C 108.2944 176.6534 116.6848 142.512 116.6848 106.411 C 116.6848 67.9782 107.1755 31.7664 90.3815 0 L 58.7656 28.9999 L 17.5047 41.9866 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:236.610;y:12.163;w:202.236;h:167.034" transform="translate(236.6103515625, 12.1630859375)">
                        <g id="cu_Vector_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:202.236;h:167.034">
                            <path id="Vector_10" fill="#fefbdb" d="M 202.2358 113.0335 L 161.4705 150.4258 L 108.7051 167.0335 L 96.7051 146.2489 L 107.6211 142.8131 L 149.4705 129.6412 L 180.8106 100.8943 L 190.2358 92.2489 L 202.2358 113.0335 Z M 9.3896 41.837 L 0 84.0901 C 44.1818 86.6957 83.0069 109.2213 107.6211 142.8131 L 149.4705 129.6412 L 180.8106 100.8943 C 141.3955 42.196 75.4184 2.8269 0.0925 0 L 9.3896 41.837 Z"/>
                            <path id="Vector_11" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 202.2358 113.0335 L 161.4705 150.4258 L 108.7051 167.0335 L 96.7051 146.2489 L 107.6211 142.8131 L 149.4705 129.6412 L 180.8106 100.8943 L 190.2358 92.2489 L 202.2358 113.0335 Z M 9.3896 41.837 L 0 84.0901 C 44.1818 86.6957 83.0069 109.2213 107.6211 142.8131 L 149.4705 129.6412 L 180.8106 100.8943 C 141.3955 42.196 75.4184 2.8269 0.0925 0 L 9.3896 41.837 Z"/>
                        </g>
                    </g>
                </g>
                <g id="ic-cc-6" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:312;y:96;w:48;h:48" fill="#33de7b1a" transform="translate(312, 96)">
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:252;w:48;h:48" fill="#33de7b1a" transform="translate(228, 252)">
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:312;y:408;w:48;h:48" fill="#33de7b1a" transform="translate(312, 408)">
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:408;w:48;h:48" fill="#33de7b1a" transform="translate(516, 408)">
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:600;y:252;w:48;h:48" fill="#33de7b1a" transform="translate(600, 252)">
                    <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:516;y:96;w:48;h:48" fill="#33de7b1a" transform="translate(516, 96)">
                    <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:576;y:480;w:168;h:60" transform="translate(576, 480)">
                    <g id="tx-lt-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-lc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:576;y:12;w:168;h:60" transform="translate(576, 12)">
                    <g id="tx-lb-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:BOTTOM"/>
                    </g>
                    <g id="tx-lb-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-lc-remove-1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:132;y:12;w:168;h:60" transform="translate(132, 12)">
                    <g id="tx-rb-6" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:BOTTOM"/>
                    </g>
                    <g id="tx-rb-6-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:BOTTOM"/>
                    </g>
                    <rect id="bt-lc-remove-6" data-constraints="horizontal:MAX;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 36)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:696;y:246;w:168;h:60" transform="translate(696, 246)">
                    <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-2" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <path id="bt-cc-add-1" transform="translate(456, 79)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:456;y:79;w:24;h:24"/>
                <path id="bt-cc-add-2" transform="translate(588, 175)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:588;y:175;w:24;h:24"/>
                <path id="bt-cc-add-3" transform="translate(585, 360)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:585;y:360;w:24;h:24"/>
                <path id="bt-cc-add-4" transform="translate(420, 450)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:420;y:450;w:24;h:24"/>
                <path id="bt-cc-add-5" transform="translate(264, 353)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:264;y:353;w:24;h:24"/>
                <path id="bt-cc-add-6" transform="translate(271, 168)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:271;y:168;w:24;h:24"/>
                <path id="bt-cc-add-7" transform="translate(432, 79)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432;y:79;w:24;h:24"/>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:12;y:246;w:168;h:60" transform="translate(12, 246)">
                    <g id="tx-rc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <g id="tx-rc-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-lc-remove-5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 18)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:120;y:480;w:168;h:60" transform="translate(120, 480)">
                    <g id="tx-rt-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-lc-remove-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 168, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
            </g>
        </g>
    </g>
</svg>