<svg xmlns="http://www.w3.org/2000/svg" width="600" height="468">
    <g id="performance-v4--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L600 0 L600 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:600;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:600;h:0">
            <g id="body" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:276 12 0 12;gap:36;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:600;h:420">
                <g id="left_column" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:24.000;y:276;w:240;h:144" transform="translate(23.999950408935547, 276)">
                    <g id="tx-cc-left-value" data-entity-classes="Number Hero" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:48" fill="#ff00001a" transform="translate(0, 12)">
                        <text id="45.50%" data-constraints="horizontal:STRETCH;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#df5e59" font-size="40" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP">45.50%</text>
                    </g>
                    <g id="text_content_left" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:84;w:240;h:60" transform="translate(0, 84)">
                        <g id="text" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:240;h:60">
                            <g id="tx-ct-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#ff00001a">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <g id="tx-ct-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:240;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                        </g>
                    </g>
                    <g id="bg-left" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-179;w:240;h:143" transform="translate(0, -179)">
                        <path id="Union" fill="#ffedeb" d="M 239.8315 136.792 C 240.2883 138.4935 239.8014 140.3091 238.5542 141.555 C 237.307 142.8008 235.4889 143.2877 233.7847 142.8322 L 195.407 132.5745 C 193.7022 132.1188 192.3705 130.7894 191.9138 129.0872 C 191.4572 127.3849 191.9449 125.5688 193.1933 124.3231 L 199.0505 118.4788 L 168.7255 88.2203 L 148.8866 108.0157 C 146.9584 109.9397 143.8336 109.9397 141.9054 108.0157 L 90.8663 57.0888 L 69.3402 78.5676 C 67.412 80.4916 64.2872 80.4916 62.359 78.5676 L 1.4477 17.79 C 0.5208 16.8652 0 15.6104 0 14.302 C 0 12.9936 0.5208 11.7388 1.4477 10.814 L 10.8392 1.443 C 12.7675 -0.481 15.8923 -0.481 17.8205 1.443 L 65.8496 49.3679 L 87.3757 27.8891 C 89.304 25.9651 92.4287 25.9651 94.3569 27.8891 L 145.396 78.8161 L 165.2349 59.0207 C 167.1631 57.0967 170.2879 57.0967 172.2161 59.0207 L 215.4284 102.1381 L 221.2908 96.2886 C 222.5384 95.0437 224.3564 94.5578 226.0601 95.0138 C 227.7638 95.4699 229.0945 96.7987 229.5512 98.4999 L 239.8315 136.792 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:143"/>
                    </g>
                    <path id="ic-cc-1" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 24, -203.75)" fill="#33de7b1a" d="M 0 0 L 192 0 L 192 192 L 0 192 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:-203.750;w:192;h:192"/>
                </g>
                <g id="middle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:300.000;y:276;w:0.000;h:24" transform="translate(299.99993896484375, 276)">

                </g>
                <g id="right_column" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:336.000;y:276;w:240;h:144" transform="translate(336.0000305175781, 276)">
                    <g id="tx-cc-right-value" data-entity-classes="Number Hero" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:12;w:240;h:48" fill="#ff00001a" transform="translate(0, 12)">
                        <text id="45.50%_1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#3cc583" font-size="40" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP">45.50%</text>
                    </g>
                    <g id="text_content_right" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:84;w:240;h:60" transform="translate(0, 84)">
                        <g id="text_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:240;h:60">
                            <g id="tx-ct-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#ff00001a">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                            <g id="tx-ct-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:240;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                            </g>
                        </g>
                    </g>
                    <g id="bg-right" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-179;w:240;h:143" transform="translate(0, -179)">
                        <path id="Union_1" fill="#e7fbf2" d="M 239.8315 6.208 C 240.2883 4.5065 239.8014 2.6909 238.5542 1.445 C 237.307 0.1992 235.4889 -0.2877 233.7847 0.1678 L 195.407 10.4255 C 193.7022 10.8811 192.3705 12.2106 191.9138 13.9128 C 191.4572 15.615 191.9449 17.4312 193.1933 18.6769 L 199.0505 24.5212 L 168.7255 54.7797 L 148.8866 34.9843 C 146.9584 33.0603 143.8336 33.0603 141.9054 34.9843 L 90.8663 85.9113 L 69.3402 64.4324 C 67.412 62.5084 64.2872 62.5084 62.359 64.4324 L 1.4477 125.21 C 0.5208 126.1348 0 127.3896 0 128.698 C 0 130.0064 0.5208 131.2612 1.4477 132.1861 L 10.8392 141.557 C 12.7675 143.481 15.8923 143.481 17.8205 141.557 L 65.8496 93.6321 L 87.3757 115.1109 C 89.304 117.0349 92.4287 117.0349 94.3569 115.1109 L 145.396 64.1839 L 165.2349 83.9793 C 167.1631 85.9033 170.2879 85.9033 172.2161 83.9793 L 215.4284 40.8619 L 221.2908 46.7114 C 222.5384 47.9563 224.3564 48.4422 226.0601 47.9862 C 227.7638 47.5301 229.0945 46.2013 229.5512 44.5001 L 239.8315 6.208 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:143.000"/>
                    </g>
                    <path id="ic-cc-2" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 24, -203.75)" fill="#33de7b1a" d="M 0 0 L 192 0 L 192 192 L 0 192 L 0 0 Z" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:-203.750;w:192;h:192"/>
                </g>
                <path id="line" transform="translate(300, 276)" fill="none" stroke="#bcbcbc" stroke-width="1" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" stroke-dasharray="12 12" d="M 0 144 L 0 0" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:300;y:276;w:0;h:144"/>
            </g>
        </g>
    </g>
</svg>