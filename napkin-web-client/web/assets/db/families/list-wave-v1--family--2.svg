<svg xmlns="http://www.w3.org/2000/svg" width="480" height="299">
    <g id="list-wave-v1--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L480 0 L480 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:480;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:480;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:48 36 48 36;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:0;w:480;h:251">
                <g id="target-market-slide" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:60;primary:MIN;counter:MIN" data-position="x:36;y:48;w:408;h:144" transform="translate(36, 48)">
                    <g id="row-top" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:0;w:408;h:60">
                        <g id="g-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:0;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:180;h:60">
                            <path id="top-fill" transform="translate(0, -24)" fill="#fefbdb" d="M 180 0 L 0 0 L 0 24 L 180 24 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-24;w:180;h:24"/>
                            <path id="bottom-fill" transform="translate(0, 60)" fill="#fefbdb" d="M 180 0 L 180 12.0275 C 180 12.0275 137 12.0275 99 21.0482 C 51.28 32.3741 0 24.055 0 24.055 L 0 0 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:27"/>
                            <path id="sides-fill" fill="#fefbdb" d="M 180 0 L 0 0 L 0 60 L 180 60 L 180 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:60"/>
                            <path id="top-stroke" transform="translate(0, -24)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 24 L 180 0 L 0 0 L 0 24" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-24;w:180;h:24"/>
                            <path id="bottom-stroke" transform="translate(0, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 12.0275 C 180 12.0275 137 12.0275 99 21.0482 C 51.28 32.3741 0 24.055 0 24.055 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:27"/>
                            <path id="sides-stroke" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 60 M 0 60 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:60"/>
                            <g id="tx-cc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:36" fill="#ff00001a" transform="translate(12, 12)">
                                <text id="Label" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:36" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -36, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="g-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:0;primary:CENTER;counter:CENTER" data-position="x:228;y:0;w:180;h:60" transform="translate(228, 0)">
                            <path id="top-fill_1" transform="translate(0, -24)" fill="#fef2e6" d="M 180 0 L 0 0 L 0 24 L 180 24 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-24;w:180;h:24"/>
                            <path id="bottom-fill_1" transform="translate(0, 60)" fill="#fef2e6" d="M 180 0 L 180 12.0275 C 180 12.0275 137 12.0275 99 21.0482 C 51.28 32.3741 0 24.055 0 24.055 L 0 0 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:27"/>
                            <path id="sides-fill_1" fill="#fef2e6" d="M 180 0 L 0 0 L 0 60 L 180 60 L 180 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:60"/>
                            <path id="top-stroke_1" transform="translate(0, -24)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 24 L 180 0 L 0 0 L 0 24" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-24;w:180;h:24"/>
                            <path id="bottom-stroke_1" transform="translate(0, 60)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 12.0275 C 180 12.0275 137 12.0275 99 21.0482 C 51.28 32.3741 0 24.055 0 24.055 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:180;h:27"/>
                            <path id="sides-stroke_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 60 M 0 60 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:60"/>
                            <g id="tx-cc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:156;h:36" fill="#ff00001a" transform="translate(12, 12)">
                                <text id="Label_1" data-constraints="horizontal:SCALE;vertical:SCALE;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:36" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-36;y:18;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -36, 18)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="row-bottom" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:120;w:408;h:24" transform="translate(0, 120)">
                        <g id="g-1_1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:CENTER" data-position="x:0;y:0;w:180;h:24">
                            <path id="top-fill_2" transform="translate(0, -48)" fill="#f6f6f6" d="M 180 0 L 180 48 L 0 48 L 0 12 C 0 12 51.28 20.3 99 9 C 137 0 180 0 180 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:180;h:48"/>
                            <path id="sides-fill_2" fill="#f6f6f6" d="M 180 0 L 180 24 L 0 24 L 0 0 L 180 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24"/>
                            <path id="bottom-fill_2" transform="translate(0, 24)" fill="#f6f6f6" d="M 180 0 L 0 0 L 0 36 L 180 36 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24;w:180;h:36"/>
                            <path id="top-stroke_2" transform="translate(0, -48)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 48 L 180 0 C 180 0 137 0 99 9 C 51.28 20.3 0 12 0 12 L 0 48" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:180;h:48"/>
                            <path id="sides-stroke_2" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 24 L 180 0 M 0 0 L 0 24" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24"/>
                            <path id="bottom-stroke_2" transform="translate(0, 24)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 36 L 0 36 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24;w:180;h:36"/>
                            <g id="tx-cc-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:0;w:156;h:24" fill="#ff00001a" transform="translate(12, 0)">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                        </g>
                        <g id="g-2_1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:CENTER;counter:CENTER" data-position="x:228;y:0;w:180;h:24" transform="translate(228, 0)">
                            <path id="top-fill_3" transform="translate(0, -48)" fill="#f6f6f6" d="M 180 0 L 180 48 L 0 48 L 0 12 C 0 12 51.28 20.3 99 9 C 137 0 180 0 180 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:180;h:48"/>
                            <path id="sides-fill_3" fill="#f6f6f6" d="M 180 0 L 180 24 L 0 24 L 0 0 L 180 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24"/>
                            <path id="bottom-fill_3" transform="translate(0, 24)" fill="#f6f6f6" d="M 180 0 L 0 0 L 0 36 L 180 36 L 180 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24;w:180;h:36"/>
                            <path id="top-stroke_3" transform="translate(0, -48)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 48 L 180 0 C 180 0 137 0 99 9 C 51.28 20.3 0 12 0 12 L 0 48" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:180;h:48"/>
                            <path id="sides-stroke_3" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 24 L 180 0 M 0 0 L 0 24" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24"/>
                            <path id="bottom-stroke_3" transform="translate(0, 24)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" d="M 180 0 L 180 36 L 0 36 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24;w:180;h:36"/>
                            <g id="tx-cc-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:0;w:156;h:24" fill="#ff00001a" transform="translate(12, 0)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                            </g>
                            <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-102;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, -102)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>