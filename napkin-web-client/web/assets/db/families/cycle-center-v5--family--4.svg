<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="cycle-center-v5--family--4">        <g id="lines">            <g id="g-4">                <rect id="cr-small-4" stroke="#17aee1" fill="#e8f9ff" stroke-width="2" stroke-linejoin="miter"  transform="matrix(-1, -8.742279078433057e-8, -8.74227694680485e-8, 1, 264, 312.00000629444)" width="71.99999237060547" height="72" rx="36" ry="36"></rect>
                <g id="ar-with-terminator" data-entity-classes="Arrow">                    <path id="line" data-entity-classes="Arrow" marker-start="url(#arrow)" transform="translate(238.68096113204956, 407.72528624534607)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 14.9568 33.3512 42.0303 60.0833 75.6375 74.5891"></path></g></g>
            <g id="g-3">                <rect id="cr-small-3" stroke="#3cc583" fill="#e7fbf2" stroke-width="2" stroke-linejoin="miter"  transform="matrix(-1, -8.742277657347586e-8, -8.742277657347586e-8, 1, 408.00000762939453, 456)" width="72" height="72" rx="36" ry="36"></rect>
                <g id="ar-with-terminator_1" data-entity-classes="Arrow">                    <path id="line_1" data-entity-classes="Arrow" marker-start="url(#arrow)" transform="translate(431.5244162082672, 405.2138671875)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 76.5029 C 33.79 61.4973 60.8391 34.0582 75.3449 0"></path></g></g>
            <g id="g-2">                <rect id="cr-small-2" stroke="#93c332" fill="#f2fae1" stroke-width="2" stroke-linejoin="miter"  transform="matrix(-1, -8.742277657347586e-8, -8.742277657347586e-8, 1, 551.5849609375, 312)" width="72" height="72" rx="36" ry="36"></rect>
                <g id="ar-with-terminator_2" data-entity-classes="Arrow">                    <path id="line_2" data-entity-classes="Arrow" marker-start="url(#arrow)" transform="translate(428.9990234375, 213.115478515625)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 76.5639 74.6257 C 61.3998 41.1039 33.9729 14.323 0 0"></path></g></g>
            <g id="g-1">                <rect id="cr-small-1" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(-1.1924880638503055e-8, 1, -1, -1.1924880638503055e-8, 408, 168.00000085859142)" width="72" height="72" rx="36" ry="36"></rect>
                <g id="ar-with-terminator_3" data-entity-classes="Arrow">                    <path id="line_3" data-entity-classes="Arrow" marker-start="url(#arrow)" transform="translate(237.62109375, 214.61451768875122)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 74.3941 0 C 41.0551 15.0787 14.3839 42.2863 0 75.991"></path></g></g></g>
        <path id="ic-cc-4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 204, 324)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-3" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 348, 468)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 492, 324)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 348, 180)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 312)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-3" transform="translate(300, 540)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 564, 312)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-cb-1" transform="translate(300, 84)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="translate(258, 234)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="translate(258, 438)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="translate(463, 438)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="translate(463, 234)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="translate(252, 336)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="translate(360, 444)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="translate(468, 336)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="translate(360, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="tx-cb-title">            <path id="rect" transform="translate(72, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
            <text id="Label" fill="#484848" transform="translate(323.7861328125, 16)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g>
        <g id="g-0">            <ellipse id="cr-big" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="translate(288, 264)" cx="84" cy="84" rx="84" ry="84"></ellipse></g>
        <path id="tx-cc-0" transform="translate(312.001953125, 360.0098876953125)" fill="#ff00001a" d="M0 0 L119.998 0 L119.998 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-0" transform="translate(342, 288)" fill="#33de7b1a" d="M0 0 L60 0 L60 60 L0 60 L0 0 Z"></path></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>