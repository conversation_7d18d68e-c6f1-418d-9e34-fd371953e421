<svg xmlns="http://www.w3.org/2000/svg" width="768" height="636">    <g id="Gear-large-v1--family--7">        <g id="lines">            <g id="g-7">                <g id="cu">                    <g id="cu_1" >                        <path id="Union" transform="translate(319.43829345703125, 411.9998779296875)" fill="#edf4ff" d="M64.5618 7.2528 C53.4312 7.2528 42.9033 4.6043 33.5006 0 L0 69.5547 C2.6544 70.8528 5.3089 72.0329 8.0813 73.154 L1.5337 104.132 C8.0813 106.551 14.8059 108.616 21.6484 110.327 L33.6818 81.0607 C39.4036 82.2408 45.1844 83.1259 51.1421 83.6569 L54.0325 115.166 C57.5127 115.343 61.052 115.52 64.5912 115.52 C68.1305 115.52 71.6697 115.402 75.1499 115.166 L78.0404 83.6569 C83.9391 83.1259 89.7788 82.2408 95.5006 81.0607 L107.534 110.327 C114.436 108.616 121.16 106.61 127.649 104.132 L121.101 73.154 C123.814 72.0329 126.528 70.8528 129.182 69.5547 L95.623 0 C86.2202 4.6043 75.6924 7.2528 64.5618 7.2528 Z"></path>
                        <path id="Union_1" transform="translate(319.43829345703125, 411.9998779296875)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 64.5618 7.2528 C 53.4312 7.2528 42.9033 4.6043 33.5006 0 L 0 69.5547 C 2.6544 70.8528 5.3089 72.0329 8.0813 73.154 L 1.5337 104.1318 C 8.0813 106.551 14.8059 108.6162 21.6484 110.3274 L 33.6818 81.0607 C 39.4036 82.2408 45.1844 83.1259 51.1421 83.6569 L 54.0325 115.1658 C 57.5127 115.3428 61.052 115.5198 64.5912 115.5198 C 68.1305 115.5198 71.6697 115.4018 75.1499 115.1658 L 78.0404 83.6569 C 83.9391 83.1259 89.7788 82.2408 95.5006 81.0607 L 107.534 110.3274 C 114.4356 108.6162 121.1601 106.61 127.6487 104.1318 L 121.1011 73.154 C 123.8145 72.0329 126.528 70.8528 129.1824 69.5547 L 95.623 0 C 86.2202 4.6043 75.6924 7.2528 64.5618 7.2528 Z"></path></g></g></g>
            <g id="g-6">                <g id="cu_2">                    <g id="cu_3" >                        <path id="Union_2" transform="translate(414.9999694824219, 363.00018310546875)" fill="#e7fbf2" d="M38.9221 0 C34.0366 21.6292 19.461 39.532 0 48.9289 L33.6204 118.496 C36.2748 117.197 38.8703 115.84 41.4657 114.424 L61.5804 138.852 C67.5971 135.194 73.4369 131.241 78.9817 126.933 L63.645 99.3188 C68.187 95.6014 72.4931 91.5301 76.5632 87.2817 L102.99 104.688 C107.709 99.4368 112.074 93.9493 116.144 88.1668 L93.3156 66.2757 C96.5599 61.3783 99.5093 56.3039 102.164 50.9934 L132.542 59.8442 C135.492 53.4716 138.028 46.922 140.27 40.1954 L111.956 25.9751 C112.781 23.1428 113.489 20.3106 114.197 17.4193 L114.138 17.4783 L38.9221 0 Z"></path>
                        <path id="Union_3" transform="translate(414.9999694824219, 363.00018310546875)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 38.9221 0 C 34.0366 21.6292 19.461 39.532 0 48.9289 L 33.6204 118.4955 C 36.2748 117.1974 38.8703 115.8403 41.4657 114.4241 L 61.5804 138.8524 C 67.5971 135.194 73.4369 131.2407 78.9817 126.9333 L 63.645 99.3188 C 68.187 95.6014 72.4931 91.5301 76.5632 87.2817 L 102.9895 104.6883 C 107.7085 99.4368 112.0735 93.9493 116.1437 88.1668 L 93.3156 66.2757 C 96.5599 61.3783 99.5093 56.3039 102.1637 50.9934 L 132.5423 59.8442 C 135.4916 53.4716 138.0281 46.922 140.2696 40.1954 L 111.9556 25.9751 C 112.7814 23.1428 113.4893 20.3106 114.1972 17.4193 L 114.1381 17.4783 L 38.9221 0 Z"></path></g></g></g>
            <g id="g-5">                <g id="cu_4">                    <g id="cu_5" >                        <path id="Union_4" transform="translate(212.84820365905762, 363.00018310546875)" fill="#feecf7" d="M101.189 0 L101.23 0.0405 C106.115 21.6599 120.691 39.5142 140.152 48.9474 L106.649 118.555 C103.995 117.256 101.34 115.899 98.8038 114.483 L78.6891 138.911 C72.6134 135.253 66.8327 131.3 61.2879 126.992 L76.6246 99.3778 C72.0826 95.6014 67.7765 91.5891 63.7064 87.3407 L37.28 104.747 C32.561 99.4958 28.1959 93.9493 24.1258 88.2258 L46.9539 66.3348 C43.7096 61.4373 40.7602 56.3039 38.1058 51.0524 L7.7273 59.9032 C4.778 53.5306 2.1825 46.981 0 40.2544 L28.314 26.0341 C27.4881 23.2018 26.7213 20.3696 26.0724 17.4783 C26.0724 17.4783 101.23 0.0405 101.189 0 Z"></path>
                        <path id="Union_5" transform="translate(212.84820365905762, 363.00018310546875)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 101.1894 0 L 101.2298 0.0405 C 106.1152 21.6599 120.6908 39.5142 140.1519 48.9474 L 106.6492 118.5545 C 103.9947 117.2564 101.3403 115.8993 98.8038 114.4831 L 78.6891 138.9114 C 72.6134 135.253 66.8327 131.2997 61.2879 126.9923 L 76.6246 99.3778 C 72.0826 95.6014 67.7765 91.5891 63.7064 87.3407 L 37.28 104.7473 C 32.561 99.4958 28.1959 93.9493 24.1258 88.2258 L 46.9539 66.3348 C 43.7096 61.4373 40.7602 56.3039 38.1058 51.0524 L 7.7273 59.9032 C 4.778 53.5306 2.1825 46.981 0 40.2544 L 28.314 26.0341 C 27.4881 23.2018 26.7213 20.3696 26.0724 17.4783 C 26.0724 17.4783 101.2298 0.0405 101.1894 0 Z"></path></g></g></g>
            <g id="g-4">                <g id="cu_6">                    <g id="cu_7" >                        <path id="Union_6" transform="translate(440.0001220703125, 248.36508178710938)" fill="#fef2e6" d="M60.3526 6.1956 C62.1812 8.4968 63.9508 10.857 65.6614 13.2762 L94.3882 0 C98.2814 5.8415 101.762 11.9191 104.947 18.2327 L79.1105 36.4653 C81.529 41.7758 83.7115 47.2633 85.5401 52.8688 L116.921 48.6794 C118.868 55.347 120.402 62.1917 121.581 69.2133 L91.4979 79.0672 C92.2647 84.8497 92.6777 90.6913 92.7956 96.6508 L124 101.902 C123.882 108.983 123.41 116.005 122.466 122.908 L90.849 123.439 C90.3771 126.39 89.7872 129.281 89.1384 132.113 L13.9331 115.002 C15.1043 109.897 15.7908 104.551 15.7908 99.0845 L15.7504 99.0845 C15.7504 82.1618 9.8138 66.6861 0 54.4262 L60.3526 6.1956 Z"></path>
                        <path id="Union_7" transform="translate(440.0001220703125, 248.36508178710938)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 60.3526 6.1956 C 62.1812 8.4968 63.9508 10.857 65.6614 13.2762 L 94.3882 0 C 98.2814 5.8415 101.7617 11.9191 104.947 18.2327 L 79.1105 36.4653 C 81.529 41.7758 83.7115 47.2633 85.5401 52.8688 L 116.9214 48.6794 C 118.868 55.347 120.4017 62.1917 121.5814 69.2133 L 91.4979 79.0672 C 92.2647 84.8497 92.6777 90.6913 92.7956 96.6508 L 123.9999 101.9023 C 123.8819 108.9829 123.4101 116.0046 122.4663 122.9082 L 90.849 123.4392 C 90.3771 126.3895 89.7872 129.2808 89.1384 132.113 L 13.9331 115.0023 C 15.1043 109.8974 15.7908 104.5512 15.7908 99.0845 L 15.7504 99.0845 C 15.7504 82.1618 9.8137 66.6861 0 54.4262 L 60.3526 6.1956 Z"></path></g></g></g>
            <g id="g-3">                <g id="cu_8">                    <g id="cu_9" >                        <path id="Union_8" transform="translate(203.9999999999991, 248.36508178710938)" fill="#fefbdb" d="M110.067 115.002 C108.896 109.857 108.209 104.551 108.209 99.0845 L108.25 99.0845 C108.25 82.202 114.146 66.6861 124 54.4262 L63.6473 6.1956 C61.8187 8.4968 60.0491 10.857 58.3385 13.2762 L29.6117 0 C25.7775 5.8415 22.2382 11.9781 19.0529 18.2327 L44.8894 36.4653 C42.4119 41.7758 40.2884 47.2633 38.4598 52.8688 L7.0785 48.6794 C5.1319 55.406 3.5393 62.2507 2.4185 69.2133 L32.502 79.0672 C31.7352 84.8497 31.2633 90.6913 31.2043 96.6508 L0 101.902 C0.118 109.042 0.6488 116.064 1.5336 122.908 L33.1509 123.439 C33.6228 126.331 34.2127 129.222 34.8616 132.113 L110.067 115.002 Z"></path>
                        <path id="Union_9" transform="translate(203.9999999999991, 248.36508178710938)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 110.0669 115.0023 C 108.8957 109.8572 108.2091 104.5512 108.2091 99.0845 L 108.2496 99.0845 C 108.2496 82.202 114.1459 66.6861 124 54.4262 L 63.6473 6.1956 C 61.8187 8.4968 60.0491 10.857 58.3385 13.2762 L 29.6117 0 C 25.7775 5.8415 22.2382 11.9781 19.0529 18.2327 L 44.8894 36.4653 C 42.4119 41.7758 40.2884 47.2633 38.4598 52.8688 L 7.0785 48.6794 C 5.1319 55.406 3.5393 62.2507 2.4185 69.2133 L 32.502 79.0672 C 31.7352 84.8497 31.2633 90.6913 31.2043 96.6508 L 0 101.9023 C 0.118 109.0419 0.6488 116.0636 1.5336 122.9082 L 33.1509 123.4392 C 33.6228 126.3305 34.2127 129.2218 34.8616 132.113 L 110.0669 115.0023 Z"></path></g></g></g>
            <g id="g-2">                <g id="cu_10">                    <g id="cu_11" >                        <path id="Union_10" transform="translate(383.9704284667969, 168.05924817174673)" fill="#faf0ff" d="M116.441 86.5019 C114.613 84.2007 112.725 81.9585 110.719 79.7753 L130.008 54.757 C125.171 49.6825 120.039 44.8441 114.613 40.3597 L91.0176 61.5426 C86.3576 57.9433 81.5206 54.58 76.4477 51.5707 L87.5372 21.95 C81.4026 18.5277 75.091 15.4594 68.5434 12.8042 L52.2628 39.9466 C46.777 37.8815 41.1732 36.1703 35.3924 34.7542 L37.221 3.1273 C30.3785 1.7112 23.477 0.6491 16.3985 0 L8.8481 30.7418 C5.9577 30.5648 3.0084 30.4468 0 30.4468 L0.0295 107.768 C22.8045 107.768 43.0758 118.403 56.2805 134.941 L56.3208 134.9 L116.441 86.5019 Z"></path>
                        <path id="Union_11" transform="translate(383.9704284667969, 168.05924817174673)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 116.4411 86.5019 C 114.6125 84.2007 112.7249 81.9585 110.7193 79.7753 L 130.0082 54.757 C 125.1713 49.6825 120.0394 44.8441 114.6125 40.3597 L 91.0176 61.5426 C 86.3576 57.9433 81.5206 54.58 76.4477 51.5707 L 87.5372 21.95 C 81.4026 18.5277 75.091 15.4594 68.5434 12.8042 L 52.2628 39.9466 C 46.777 37.8815 41.1732 36.1703 35.3924 34.7542 L 37.221 3.1273 C 30.3785 1.7112 23.477 0.6491 16.3985 0 L 8.8481 30.7418 C 5.9577 30.5648 3.0084 30.4468 0 30.4468 L 0.0295 107.7683 C 22.8045 107.7683 43.0758 118.4028 56.2805 134.9409 L 56.3208 134.9005 L 116.4411 86.5019 Z"></path></g></g></g>
            <g id="g-1">                <g id="cu_12">                    <g id="cu_13" >                        <path id="Union_12" transform="translate(254.02130126953125, 168)" fill="#e8f9ff" d="M130.008 30.4468 C127.059 30.4468 124.109 30.5648 121.16 30.7418 L113.61 0 C106.531 0.6491 99.5707 1.6522 92.7871 3.1273 L94.6158 34.7542 C88.835 36.1703 83.2312 37.8814 77.7454 39.9466 L61.4648 12.8042 C54.9172 15.5184 48.6056 18.5277 42.4709 21.95 L53.5605 51.5707 C48.4876 54.58 43.6507 57.9433 38.9907 61.5426 L15.3957 40.3597 C9.9689 44.8441 4.837 49.6825 0 54.757 L19.2889 79.7753 C17.3423 81.9585 15.3957 84.2007 13.5671 86.5019 C17.7088 89.8019 73.6343 135.067 73.6875 135 C86.8921 118.453 107.163 107.813 129.938 107.813 L129.979 107.854 L130.008 30.4468 Z"></path>
                        <path id="Union_13" transform="translate(254.02130126953125, 168)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 130.0082 30.4468 C 127.0589 30.4468 124.1095 30.5648 121.1601 30.7418 L 113.6097 0 C 106.5313 0.6491 99.5707 1.6522 92.7871 3.1273 L 94.6158 34.7542 C 88.835 36.1703 83.2312 37.8814 77.7454 39.9466 L 61.4648 12.8042 C 54.9172 15.5184 48.6056 18.5277 42.4709 21.95 L 53.5605 51.5707 C 48.4876 54.58 43.6507 57.9433 38.9907 61.5426 L 15.3957 40.3597 C 9.9689 44.8441 4.837 49.6825 0 54.757 L 19.2889 79.7753 C 17.3423 81.9585 15.3957 84.2007 13.5671 86.5019 C 17.7088 89.8019 73.6343 135.0667 73.6875 135 C 86.8921 118.4533 107.1634 107.8133 129.9383 107.8133 L 129.9788 107.8537 L 130.0082 30.4468 Z"></path></g></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 84, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 319, 228)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-3" transform="translate(258, 306)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 276, 396)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-6" transform="translate(456, 396)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-7" transform="translate(366, 438)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 474, 306)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 414, 228)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 372, 528)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 216, 456)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 564, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 180, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 456, 156)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 288, 156)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rb-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 132, 84)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-3" transform="translate(12, 264)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 48, 432)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-7" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 300, 552)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 552, 432)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-4" transform="translate(588, 264)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 468, 84)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 84, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 660, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 120, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 624, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 264, 552)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-8" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 480, 552)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6_1" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 492, 468)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 540, 48)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 204, 48)" width="24" height="24" rx="0" ry="0"></rect></g></svg>