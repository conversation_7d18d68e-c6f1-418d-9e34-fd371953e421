<svg xmlns="http://www.w3.org/2000/svg" width="876" height="324">
    <g id="gauge-lens-v1--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L876 0 L876 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:876;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:876;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:876;h:276">
                <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:132;w:444;h:120" transform="translate(216, 132)">
                    <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:2;w:316;h:116">
                        <path id="Vector" transform="translate(0, 2)" fill="#f3f0ff" d="M58 0 C25.9675 0 0 25.9675 0 58 C0 90.0325 25.9675 116 58 116 C70.3073 116 81.4342 111.917 90.7803 105 C91.1624 104.717 91.5412 104.43 91.9167 104.138 C106.262 92.9928 115.736 75.4023 115.5 58 C115.5 40.7314 106.023 23.0115 91.5096 11.7811 C90.37 10.8994 89.1995 10.0576 88 9.2593 C79.2633 3.4441 69.0997 0 58 0 Z M58 10 C31.4903 10 10 31.4903 10 58 C10 84.5097 31.4903 106 58 106 C84.5097 106 106 84.5097 106 58 C106 31.4903 84.5097 10 58 10 Z M268 106 C294.51 106 316 84.5097 316 58 C316 31.4903 294.51 10 268 10 C255.003 10 243.387 15.3537 234.951 23.809 C233.116 25.6484 231.493 27.5723 229.97 29.6897 C219.389 44.6725 218.351 62.5569 224.36 77.1145 C226.816 83.034 230.369 88.263 234.837 92.6286 C243.333 100.929 255.027 106 268 106 Z M230 58 C230 37.0132 247.013 20 268 20 C288.987 20 306 37.0132 306 58 C306 78.9868 288.987 96 268 96 C247.013 96 230 78.9868 230 58 Z M167 75.5001 C209 75.5001 234.837 92.6286 234.837 92.6286 C230.369 88.263 226.816 83.034 224.36 77.1145 C218.351 62.5569 219.389 44.6725 229.97 29.6897 C231.493 27.5723 233.116 25.6484 234.951 23.809 C234.951 23.809 209 39.9767 167 39.9767 C136.086 40.8034 102.293 19.269 91.5096 11.7811 C106.023 23.0115 115.5 40.7314 115.5 58 C115.736 75.4023 106.262 92.9928 91.9167 104.138 C98.5725 99.1859 133.366 74.6006 167 75.5001 Z"/>
                        <path id="Vector_1" transform="translate(0, 2)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 58 0 C 25.9675 0 0 25.9675 0 58 C 0 90.0325 25.9675 116 58 116 C 70.3073 116 81.4342 111.9172 90.7803 105 C 91.1624 104.7172 91.5412 104.4299 91.9167 104.1382 C 106.2621 92.9928 115.7357 75.4023 115.5 58 C 115.5 40.7314 106.0231 23.0115 91.5096 11.7811 C 90.37 10.8994 89.1995 10.0576 88 9.2593 C 79.2633 3.4441 69.0997 0 58 0 Z M 58 10 C 31.4903 10 10 31.4903 10 58 C 10 84.5097 31.4903 106 58 106 C 84.5097 106 106 84.5097 106 58 C 106 31.4903 84.5097 10 58 10 Z M 268 106 C 294.5097 106 316 84.5097 316 58 C 316 31.4903 294.5097 10 268 10 C 255.0028 10 243.3873 15.3537 234.9511 23.809 C 233.1158 25.6484 231.493 27.5723 229.9697 29.6897 C 219.3895 44.6725 218.3508 62.5569 224.3603 77.1145 C 226.816 83.034 230.3689 88.263 234.8371 92.6286 C 243.3329 100.9292 255.0268 106 268 106 Z M 230 58 C 230 37.0132 247.0132 20 268 20 C 288.9868 20 306 37.0132 306 58 C 306 78.9868 288.9868 96 268 96 C 247.0132 96 230 78.9868 230 58 Z M 167 75.5001 C 209 75.5001 234.8371 92.6286 234.8371 92.6286 C 230.3689 88.263 226.816 83.034 224.3603 77.1145 C 218.3508 62.5569 219.3895 44.6725 229.9697 29.6897 C 231.493 27.5723 233.1158 25.6484 234.9511 23.809 C 234.9511 23.809 209 39.9767 167 39.9767 C 136.0864 40.8034 102.2929 19.269 91.5096 11.7811 C 106.0231 23.0115 115.5 40.7314 115.5 58 C 115.7357 75.4023 106.2621 92.9928 91.9167 104.1382 C 98.5725 99.1859 133.366 74.6006 167 75.5001 Z M 88 9.2593 C 88 9.2593 89.2493 10.2116 91.5096 11.7811 M 90.7803 105 C 90.7803 105 91.1728 104.6917 91.9167 104.1382"/>
                    </g>
                    <g id="ar-with-terminator" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:334;y:53.500;w:110;h:13" transform="translate(334, 53.5)">
                        <path id="cu" transform="translate(0, 6.5)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 110 0 L 0 0" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:6.500;w:110;h:0"/>
                        <path id="cu-start" transform="translate(103.5, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 4.5475e-13 13 L 6.5 6.5 L 0 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:103.500;y:0;w:6.500;h:13"/>
                    </g>
                </g>
                <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:12;w:444;h:120" transform="translate(216, 12)">
                    <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:128;y:2;w:316;h:116">
                        <path id="Vector_2" transform="translate(128, 2)" fill="#e7fbf2" d="M258 116 C290.033 116 316 90.0325 316 58 C316 25.9675 290.033 0 258 0 C245.693 0 234.566 4.0829 225.22 11 C224.838 11.2828 224.459 11.5701 224.083 11.8618 C209.738 23.0072 200.264 40.5977 200.5 58 C200.5 75.2686 209.977 92.9885 224.49 104.219 C225.63 105.101 226.801 105.942 228 106.741 C236.737 112.556 246.9 116 258 116 Z M258 106 C284.51 106 306 84.5097 306 58 C306 31.4903 284.51 10 258 10 C231.49 10 210 31.4903 210 58 C210 84.5097 231.49 106 258 106 Z M48 10 C21.4903 10 0 31.4903 0 58 C0 84.5097 21.4903 106 48 106 C60.9972 106 72.6127 100.646 81.0489 92.191 C82.8842 90.3516 84.507 88.4277 86.0303 86.3103 C96.6105 71.3275 97.6492 53.4431 91.6397 38.8855 C89.184 32.966 85.6311 27.737 81.1629 23.3714 C72.6671 15.0708 60.9732 10 48 10 Z M86 58 C86 78.9868 68.9868 96 48 96 C27.0132 96 10 78.9868 10 58 C10 37.0132 27.0132 20 48 20 C68.9868 20 86 37.0132 86 58 Z M149 40.4999 C107 40.4999 81.1629 23.3714 81.1629 23.3714 C85.6311 27.737 89.184 32.966 91.6397 38.8855 C97.6492 53.4431 96.6105 71.3275 86.0303 86.3103 C84.507 88.4277 82.8842 90.3516 81.0489 92.191 C81.0489 92.191 107 76.0233 149 76.0233 C179.914 75.1966 213.707 96.731 224.49 104.219 C209.977 92.9885 200.5 75.2686 200.5 58 C200.264 40.5977 209.738 23.0072 224.083 11.8618 C217.428 16.8141 182.634 41.3994 149 40.4999 Z"/>
                        <path id="Vector_3" transform="translate(128, 2)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 258 116 C 290.0325 116 316 90.0325 316 58 C 316 25.9675 290.0325 0 258 0 C 245.6927 0 234.5658 4.0828 225.2197 11 C 224.8376 11.2828 224.4588 11.5701 224.0833 11.8618 C 209.7379 23.0072 200.2643 40.5977 200.5 58 C 200.5 75.2686 209.9769 92.9885 224.4904 104.2189 C 225.63 105.1006 226.8005 105.9424 228 106.7407 C 236.7367 112.5559 246.9003 116 258 116 Z M 258 106 C 284.5097 106 306 84.5097 306 58 C 306 31.4903 284.5097 10 258 10 C 231.4903 10 210 31.4903 210 58 C 210 84.5097 231.4903 106 258 106 Z M 48 10 C 21.4903 10 0 31.4903 0 58 C 0 84.5097 21.4903 106 48 106 C 60.9972 106 72.6127 100.6463 81.0489 92.191 C 82.8842 90.3516 84.507 88.4277 86.0303 86.3103 C 96.6105 71.3275 97.6492 53.4431 91.6397 38.8855 C 89.184 32.966 85.6311 27.737 81.1629 23.3714 C 72.6671 15.0708 60.9732 10 48 10 Z M 86 58 C 86 78.9868 68.9868 96 48 96 C 27.0132 96 10 78.9868 10 58 C 10 37.0132 27.0132 20 48 20 C 68.9868 20 86 37.0132 86 58 Z M 149 40.4999 C 107 40.4999 81.1629 23.3714 81.1629 23.3714 C 85.6311 27.737 89.184 32.966 91.6397 38.8855 C 97.6492 53.4431 96.6105 71.3275 86.0303 86.3103 C 84.507 88.4277 82.8842 90.3516 81.0489 92.191 C 81.0489 92.191 107 76.0233 149 76.0233 C 179.9136 75.1966 213.7071 96.731 224.4904 104.2189 C 209.9769 92.9885 200.5 75.2686 200.5 58 C 200.2643 40.5977 209.7379 23.0072 224.0833 11.8618 C 217.4275 16.8141 182.634 41.3994 149 40.4999 Z M 228 106.7407 C 228 106.7407 226.7507 105.7884 224.4904 104.2189 M 225.2197 11 C 225.2197 11 224.8272 11.3083 224.0833 11.8618"/>
                    </g>
                    <g id="ar-with-terminator_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:53.500;w:110;h:13" transform="translate(0, 53.5)">
                        <path id="cu_1" transform="translate(0, 6.5)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 110 0" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:6.500;w:110;h:0"/>
                        <path id="cu-start_1" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 6.5 13 L 0 6.5 L 6.5 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:6.500;h:13"/>
                    </g>
                </g>
                <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:426;y:240.200;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 426, 240.19970703125)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:426;y:120;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 426, 120)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:426;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 426, 0)" width="24" height="24" rx="0" ry="0"/>
                <g id="tx-cc-2-value" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:238;y:180;w:72;h:24" transform="translate(238, 180)">
                    <path id="rect" fill="#ff00001a" d="M0 0 L72 0 L72 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72;h:24"/>
                    <text id="100%" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">100%</text>
                </g>
                <g id="tx-cc-1-value" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:566;y:60;w:72;h:24" transform="translate(566, 60)">
                    <path id="rect_1" fill="#ff00001a" d="M0 0 L72 0 L72 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72;h:24"/>
                    <text id="100%_1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">100%</text>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:684;y:180;w:168;h:60" transform="translate(684, 180)">
                    <g id="tx-lt-2-label" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:24;y:60;w:168;h:60" transform="translate(24, 60)">
                    <g id="tx-rt-1-label" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:460;y:168;w:48;h:48" transform="translate(460, 168)">
                    <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:368;y:48;w:48;h:48" transform="translate(368, 48)">
                    <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>