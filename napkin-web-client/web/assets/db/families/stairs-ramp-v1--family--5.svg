<svg xmlns="http://www.w3.org/2000/svg" width="900" height="588">
    <g id="stairs-ramp-v1--family--5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L900 0 L900 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:900;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:900;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 36 24 0;gap:10;primary:MIN;counter:MIN" data-position="x:0;y:0;w:900;h:540">
                <g id="Frame_16" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 0 120 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:24;w:864;h:492" transform="translate(0, 24)">
                    <g id="nest-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:24;w:864;h:348" transform="translate(0, 24)">
                        <g id="g-5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:245;y:3.500;w:376;h:372" transform="translate(245, 3.5)">
                            <path id="Vector" transform="translate(44.234130859375, 226.5)" fill="#faf0ff" d="M 0 145.5 L 128.6113 69.8413 M 128.6113 69.8413 L 247.7645 0.5 L 247.7645 0 L 128.6113 0 L 128.6113 64 L 128.6113 69 L 128.6113 69.8413 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:44.234;y:226.500;w:247.765;h:145.500"/>
                            <path id="Vector_1" transform="translate(172.85000610351562, 39.5)" fill="#faf0ff" d="M 0 0 L 119.15 0 L 119.15 187 L 0 187 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:172.850;y:39.500;w:119.150;h:187"/>
                            <path id="Vector_2" transform="translate(172.84510803222656, 0)" fill="#faf0ff" d="M 119.1535 24.9758 L 76.4511 0 L 29.8698 23.5006 L 0 31.4985 L 0.0001 39.5 C 46.3276 39.5 72.826 39.5 119.1535 39.5 L 119.1535 24.9758 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:172.845;y:0;w:119.154;h:39.500"/>
                            <path id="Vector_3" transform="translate(0, 295.5)" fill="#faf0ff" d="M 44.2341 74.6587 L 0 49.1587 L 0.0001 0 L 172.8454 0 L 44.2341 74.6587 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:295.500;w:172.845;h:74.659"/>
                            <path id="Vector_4" transform="translate(0, 93.5)" fill="#faf0ff" d="M 0 0 L 172.85 0 L 172.85 202 L 0 202 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:93.500;w:172.850;h:202"/>
                            <path id="Vector_5" transform="translate(0, 31.5)" fill="#faf0ff" d="M 0 61.5001 L 16.6197 51.7677 L 43.9784 34.5 L 70.588 26.1535 L 172.8454 0 L 172.8454 62.0001 L 0 62.0001 L 0 61.5001 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:31.500;w:172.845;h:62.000"/>
                            <path id="Vector_6" transform="translate(0, 344.5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 44.2341 25.5 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:344.500;w:44.234;h:25.500"/>
                            <path id="Vector_7" transform="translate(0, 93)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 251.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:93;w:0;h:251.500"/>
                            <path id="Vector_8" transform="translate(43.9783935546875, 119.5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.2553 250.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43.978;y:119.500;w:0.255;h:250.500"/>
                            <path id="Vector_9" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 292 25.0081 L 249.2963 0 L 202.7208 23.5 L 43.9784 66 L 0 93 L 43.9784 119.5 L 91.7922 90.5 L 245.6025 48.5019 L 292 25.0081 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:292;h:119.500"/>
                            <path id="Vector_10" transform="translate(262.1861572265625, 33)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 13.3648 8.8652 L 0.2504 16.532 L 0.2045 26.5588 L 0.1586 36.5857 L 0.1126 46.6125 L 0.0667 56.6394 L 13.1811 48.9726 L 13.227 38.9458 L 13.2729 28.9189 L 13.3189 18.8921 L 13.3648 8.8652 Z M 0 16.6784 L 0.2504 16.532 L 0.2941 7 M 13.4416 8.8203 L 13.3648 8.8652 L 13.4054 0 M 0 26.6784 L 0.2045 26.5588 L 13.3189 18.8921 L 13.4416 18.8203 M 0 36.6784 L 0.1586 36.5857 L 13.2729 28.9189 L 13.4416 28.8203 M 0 46.6784 L 0.1126 46.6125 L 13.227 38.9458 L 13.4416 38.8203 M 0 56.6784 L 0.0667 56.6394 L 0.0399 62.5 M 13.4416 48.8203 L 13.1811 48.9726 L 13.1512 55.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:262.186;y:33;w:13.442;h:62.500"/>
                            <path id="Vector_76" transform="translate(12.999947547912598, 113)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 L 0 28 L 17.9185 38.5 L 17.9185 32 L 5.1962 24.5 L 5.1962 20 L 12.0001 24 L 12.0001 17.5 L 5.1962 13.5 L 5.1962 9 L 17.9186 16.5 L 17.9186 10.5 L 0.0001 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:13.000;y:113;w:17.919;h:38.500"/>
                        </g>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:204;h:60">
                            <g id="g-5_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:537;y:29;w:0;h:1" transform="translate(537, 29)">
                                <rect id="fill" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.500;y:0;w:1;h:1" fill="#faf0ff" transform="translate(-0.5, 0)" width="1" height="1" rx="0" ry="0"/>
                                <path id="Vector_11" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:1"/>
                            </g>
                            <g id="tx-rt-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:0;w:168;h:24" fill="#ff00001a" transform="translate(36, 0)">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <g id="tx-rt-5-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:36;w:168;h:24" fill="#ff00001a" transform="translate(36, 36)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 0)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="nest-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:72;w:864;h:276" transform="translate(0, 72)">
                            <g id="g-4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:289;y:-43;w:374;h:370" transform="translate(289, -43)">
                                <path id="Subtract" transform="translate(171.778564453125, -0.5)" fill="#feecf7" d="M 76.2899 0 L 29.8086 23.3664 L 0 50.0001 L 0 122.5 L 117.7188 122.5 L 117.7188 23.3664 L 76.2899 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171.779;y:-0.500;w:117.719;h:122.500"/>
                                <path id="Vector_12" transform="translate(0, 49.5)" fill="#feecf7" d="M 0 139.5006 L 171.7785 139.5006 L 171.7785 0 L 46.9865 112 L 0 139.5006 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49.500;w:171.779;h:139.501"/>
                                <path id="Vector_13" transform="translate(171.778564453125, 224)" fill="#feecf7" d="M 117.9784 1 L 0 70.3413 L 0 69.5 L 0 64.5 L 0 3.638e-12 L 117.9783 0 L 117.9784 1 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171.779;y:224;w:117.978;h:70.341"/>
                                <path id="Vector_14" transform="translate(171.77999877929688, 122)" fill="#feecf7" d="M 0 0 L 118.2199 0 L 118.22 102 L 0 101.966 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171.780;y:122;w:118.220;h:102"/>
                                <path id="Vector_15" transform="translate(0, 294)" fill="#feecf7" d="M 44.0879 76 L 0 50.6667 L 0 0 L 172.2741 0 L 172.2741 0.8358 L 44.0879 76 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:294;w:172.274;h:76"/>
                                <path id="Vector_16" transform="translate(0, 189)" fill="#feecf7" d="M 0 0 L 171.78 0 L 171.78 105 L 0 105 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:189;w:171.780;h:105"/>
                                <path id="Vector_17" transform="translate(0, 344.5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 43 24.5 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:344.500;w:43;h:24.500"/>
                                <path id="Vector_18" transform="translate(0, 189)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 156 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:189;w:0;h:156"/>
                                <path id="Vector_19" transform="translate(43, 215)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 154 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43;y:215;w:0;h:154"/>
                                <path id="Vector_20" transform="translate(0, -0.5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 189.5 C 17.0669 199.6536 26.1304 205.8463 43.1972 215.9999 C 43.1972 215.9999 53.2048 210.0681 59.6172 206.2673 L 86.8997 188.9999 C 148.2616 134.7171 244.0265 49.9999 244.0265 49.9999 L 289.7578 23.5038 L 248.0684 0 L 201.5871 23.4999 L 46.9865 161.9999 L 0 189.5 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.500;w:289.758;h:216.000"/>
                                <path id="Vector_21" transform="translate(58.9605598449707, 197.5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 13.2513 8.871 L 0.2484 17.5133 L 0.2028 27.5436 L 0.1573 37.5739 L 0.1117 47.6041 L 0.0662 57.6344 L 13.0691 48.9921 L 13.1146 38.9618 L 13.1602 28.9315 L 13.2058 18.9012 L 13.2513 8.871 Z M 0 17.6784 L 0.2484 17.5133 L 0.2916 8 M 13.3275 8.8203 L 13.2513 8.871 L 13.2916 0 M 0 27.6784 L 0.2028 27.5436 L 13.2058 18.9012 L 13.3275 18.8203 M 0 37.6784 L 0.1573 37.5739 L 13.1602 28.9315 L 13.3275 28.8203 M 0 47.6784 L 0.1117 47.6041 L 13.1146 38.9618 L 13.3275 38.8203 M 0 57.6784 L 0.0662 57.6344 L 0.0395 63.5 M 13.3275 48.8203 L 13.0691 48.9921 L 13.0395 55.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:58.961;y:197.500;w:13.328;h:63.500"/>
                                <path id="Vector_22" transform="translate(260.51171875, 46.558380126953125)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 5.9377 L 0 32.9377 L 9.75 27.6877 C 12 26.4377 16.95 23.0377 18.75 19.4377 C 20.25 16.4377 21.75 12.6877 20.25 6.6877 C 18 0.6877 13.7878 -1.1643 9.75 0.6877 L 0 5.9377 Z M 5.25 23.9377 L 5.25 8.1877 L 9.75 5.9377 C 12 5.1877 14.4 6.0877 15 9.6877 C 15.6 13.2877 14.25 16.4377 12.75 18.6877 C 11.25 20.9377 5.25 23.9377 5.25 23.9377 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:260.512;y:46.558;w:20.871;h:32.938"/>
                            </g>
                            <g id="Frame_19" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 84 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:612;y:0;w:252;h:60" transform="translate(612, 0)">
                                <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:168;h:60">
                                    <g id="g-4_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-33.250;y:-20;w:0.000;h:100" transform="translate(-33.25, -20)">
                                        <rect id="fill_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.500;y:0;w:1;h:1" fill="#feecf7" transform="translate(-0.5, 0)" width="1" height="1" rx="0" ry="0"/>
                                        <path id="Vector_23" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 100" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:100"/>
                                    </g>
                                    <g id="tx-lt-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                    </g>
                                    <g id="tx-lt-4-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                    </g>
                                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -24)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                            </g>
                            <g id="nest-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:72;w:864;h:204" transform="translate(0, 72)">
                                <g id="g-3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:332;y:7;w:373;h:274" transform="translate(332, 7)">
                                    <path id="Vector_24" transform="translate(43.858642578125, 129)" fill="#ffedeb" d="M 0 145.5 L 127.5196 69.8413 M 127.5196 69.8413 L 245.6614 0.5 L 245.6614 0 L 127.5196 0 L 127.5196 64 L 127.5196 69 L 127.5196 69.8413 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43.859;y:129;w:245.661;h:145.500"/>
                                    <path id="Vector_25" transform="translate(171.378173828125, 40)" fill="#ffedeb" d="M 0 0 L 118.1418 0 L 118.1418 89 L 0 89 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171.378;y:40;w:118.142;h:89"/>
                                    <path id="Vector_26" transform="translate(171.37786865234375, 0.5)" fill="#ffedeb" d="M 118.1421 24.9758 L 75.8021 0 L 29.6162 23.5006 L 0 31.4985 L 0.0001 39.5 C 45.9343 39.5 72.2078 39.5 118.1421 39.5 L 118.1421 24.9758 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171.378;y:0.500;w:118.142;h:39.500"/>
                                    <path id="Vector_27" transform="translate(0, 198)" fill="#ffedeb" d="M 43.8586 74.6587 L 0 49.1587 L 0.0001 0 L 171.3782 0 L 43.8586 74.6587 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:198;w:171.378;h:74.659"/>
                                    <path id="Vector_28" transform="translate(0, 94)" fill="#ffedeb" d="M 0 0 L 171.3782 0 L 171.3782 104 L 0 104 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:94;w:171.378;h:104"/>
                                    <path id="Vector_29" transform="translate(0, 32)" fill="#ffedeb" d="M 0 61.5001 L 16.4787 51.7677 L 43.6051 34.5 L 69.9888 26.1535 L 171.3782 0 L 171.3782 62.0001 L 0 62.0001 L 0 61.5001 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:32;w:171.378;h:62.000"/>
                                    <path id="Vector_30" transform="translate(0, 247)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 43.8586 25.5 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:247;w:43.859;h:25.500"/>
                                    <path id="Vector_31" transform="translate(0, 93.5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 153.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:93.500;w:0;h:153.500"/>
                                    <path id="Vector_32" transform="translate(43.60498046875, 120)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.2535 152.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43.605;y:120;w:0.254;h:152.500"/>
                                    <path id="Vector_33" transform="translate(0, 0.5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 289.5213 25.0081 L 247.1801 0 L 201 23.5 L 43.6051 66 L 0 93 L 43.6051 119.5 L 91.013 90.5 L 243.5176 48.5019 L 289.5213 25.0081 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0.500;w:289.521;h:119.500"/>
                                    <path id="Vector_34" transform="translate(259.9605712890625, 33.5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 13.2513 8.8652 L 0.2483 16.532 L 0.2028 26.5588 L 0.1572 36.5857 L 0.1117 46.6125 L 0.0662 56.6394 L 13.0692 48.9726 L 13.1147 38.9458 L 13.1603 28.9189 L 13.2058 18.8921 L 13.2513 8.8652 Z M 0 16.6784 L 0.2483 16.532 L 0.2916 7 M 13.3275 8.8203 L 13.2513 8.8652 L 13.2916 0 M 0 26.6784 L 0.2028 26.5588 L 13.2058 18.8921 L 13.3275 18.8203 M 0 36.6784 L 0.1572 36.5857 L 13.1603 28.9189 L 13.3275 28.8203 M 0 46.6784 L 0.1117 46.6125 L 13.1147 38.9458 L 13.3275 38.8203 M 0 56.6784 L 0.0662 56.6394 L 0.0395 62.5 M 13.3275 48.8203 L 13.0692 48.9726 L 13.0395 55.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:259.961;y:33.500;w:13.328;h:62.500"/>
                                    <path id="Vector_35" transform="translate(8.693216323852539, 117.125)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 23.25 26.6849 L 17.25 23.6848 C 16.2883 24.2704 13.6395 24.7213 11.0383 22.7704 C 8.0383 20.5204 6.5383 17.3423 6.0002 13.9345 C 5.6084 11.4527 6.3002 6.5845 10.5002 7.1845 C 14.7002 7.7845 16.7502 12.4345 17.2502 14.6845 L 23.25 17.6845 C 23.0001 15.6846 22.5883 10.17 17.7883 4.77 C 12.9883 -0.63 7.5384 -0.2283 5.7883 0.2717 C 3.5383 0.7717 0.0002 3.0431 9.3628e-9 10.9348 C -0.0002 19.0204 4.3495 24.2117 5.7883 25.7704 C 8.0383 28.2079 11.7883 31.1849 16.5 31.1849 C 21.3374 31.1849 23 28.4349 23.25 26.6849 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8.693;y:117.125;w:23.250;h:31.185"/>
                                </g>
                                <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:204;h:60">
                                    <g id="tx-rt-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:0;w:168;h:24" fill="#ff00001a" transform="translate(36, 0)">
                                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                    </g>
                                    <g id="tx-rt-3-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:36;w:168;h:24" fill="#ff00001a" transform="translate(36, 36)">
                                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                    </g>
                                    <g id="g-3_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:621.500;y:32.500;w:0;h:1" transform="translate(621.5, 32.5)">
                                        <rect id="fill_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.500;y:0.500;w:1;h:1" fill="#feecf7" transform="translate(-0.5, 0.5)" width="1" height="1" rx="0" ry="0"/>
                                        <path id="Vector_36" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 1" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:1"/>
                                    </g>
                                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 0)" width="24" height="24" rx="0" ry="0"/>
                                    <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, -24)" width="24" height="24" rx="0" ry="0"/>
                                </g>
                                <g id="nest-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:72;w:864;h:132" transform="translate(0, 72)">
                                    <g id="g-2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:376;y:-39;w:371;h:266" transform="translate(376, -39)">
                                        <path id="Subtract_1" transform="translate(170, -0.5)" fill="#fef2e6" d="M 75.5 0 L 29.5 23.3662 L 0 49.9997 L 0 122.499 L 116.7666 122.499 L 116.7666 23.5287 L 75.5 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:170;y:-0.500;w:116.767;h:122.499"/>
                                        <path id="Vector_37" transform="translate(0, 49.5)" fill="#fef2e6" d="M 0 139.5006 L 170 139.5006 L 170 0 L 46.5 112 L 0 139.5006 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:49.500;w:170;h:139.501"/>
                                        <path id="Vector_38" transform="translate(170, 126)" fill="#fef2e6" d="M 116.7569 1 L 0 70.3413 L 0 69.5 L 0 64.5 L 0 3.638e-12 L 116.7569 0 L 116.7569 1 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:170;y:126;w:116.757;h:70.341"/>
                                        <path id="Vector_39" transform="translate(169.999755859375, 122)" fill="#fef2e6" d="M 0.0002 0 L 116.757 0 L 116.7571 4.0007 L 0.0002 3.9994 L 0.0002 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:170.000;y:122;w:116.757;h:4.001"/>
                                        <path id="Vector_40" transform="translate(0, 196)" fill="#fef2e6" d="M 43.6314 76 L 0 50.5 L 0 0 L 170.4904 0 L 170.4904 0.3413 L 43.6314 76 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:196;w:170.490;h:76"/>
                                        <path id="Vector_41" transform="translate(0, 189)" fill="#fef2e6" d="M 0 0 L 170 0 L 170 7 L 0 7 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:189;w:170;h:7"/>
                                        <path id="Vector_42" transform="translate(0, 246.5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 43.2486 25.5 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:246.500;w:43.249;h:25.500"/>
                                        <path id="Vector_43" transform="translate(0, 189)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 58 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:189;w:0;h:58"/>
                                        <path id="Vector_44" transform="translate(42.996337890625, 216.5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.2521 55.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:42.996;y:216.500;w:0.252;h:55.500"/>
                                        <path id="Vector_45" transform="translate(0, -0.5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 189.5 C 16.8902 199.6536 25.8598 205.8463 42.75 215.9999 C 42.75 215.9999 52.654 210.0681 59 206.2673 L 86 188.9999 C 146.7265 134.7171 241.5 49.9999 241.5 49.9999 L 286.7578 23.5038 L 245.5 0 L 199.5 23.4999 L 46.5 161.9999 L 0 189.5 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.500;w:286.758;h:216.000"/>
                                        <path id="Vector_46" transform="translate(258.80419921875, 44.55859375)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 5.6488 L 0 32.6488 C 0 32.6488 12 26.6488 15 24.3988 C 18 22.1488 18.75 20.6488 18.75 17.6488 C 18.75 14.6488 15.75 13.1488 15.75 13.1488 C 15.75 13.1488 20.6129 9.1164 18.75 4.1488 C 17.9284 1.9577 15 -1.1012 10.5 0.3988 L 0 5.6488 Z M 5.25 18.3988 L 5.25 24.3988 C 5.25 24.3988 9.3525 22.3475 11.25 21.3988 C 12.75 20.6488 13.2243 19.0718 12.75 17.6488 C 12 15.3988 9.75 16.1488 9.75 16.1488 L 5.25 18.3988 Z M 5.25 7.8988 L 5.25 13.1488 C 5.25 13.1488 9 11.6488 10.5 10.8988 C 12 10.1488 13.5 9.3988 13.5 7.1488 C 13.5 4.8988 10.8147 5.1164 9.75 5.6488 C 8.25 6.3988 5.25 7.8988 5.25 7.8988 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:258.804;y:44.559;w:19.175;h:32.649"/>
                                        <path id="Vector_47" transform="translate(54.9605598449707, 199.5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 13.2513 8.871 L 0.2484 17.5133 L 0.2028 27.5436 L 0.1573 37.5739 L 0.1117 47.6041 L 0.0662 57.6344 L 13.0691 48.9921 L 13.1146 38.9618 L 13.1602 28.9315 L 13.2058 18.9012 L 13.2513 8.871 Z M 0 17.6784 L 0.2484 17.5133 L 0.2916 8 M 13.3275 8.8203 L 13.2513 8.871 L 13.2916 0 M 0 27.6784 L 0.2028 27.5436 L 13.2058 18.9012 L 13.3275 18.8203 M 0 37.6784 L 0.1573 37.5739 L 13.1602 28.9315 L 13.3275 28.8203 M 0 47.6784 L 0.1117 47.6041 L 13.1146 38.9618 L 13.3275 38.8203 M 0 57.6784 L 0.0662 57.6344 L 0.0395 63.5 M 13.3275 48.8203 L 13.0691 48.9921 L 13.0395 55.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54.961;y:199.500;w:13.328;h:63.500"/>
                                    </g>
                                    <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:696;y:0;w:168;h:60" transform="translate(696, 0)">
                                        <g id="g-2_1" data-entity-classes="FilledPart" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-33.250;y:-16;w:0.000;h:100" transform="translate(-33.25, -16)">
                                            <rect id="fill_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.500;y:1;w:1;h:1" fill="#fef2e6" transform="translate(-0.5, 1)" width="1" height="1" rx="0" ry="0"/>
                                            <path id="Vector_48" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 100" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:0;h:100"/>
                                        </g>
                                        <g id="tx-lt-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                        </g>
                                        <g id="tx-lt-2-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                            <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                        </g>
                                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                                        <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -24)" width="24" height="24" rx="0" ry="0"/>
                                    </g>
                                    <g id="nest-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:72;w:864;h:60" transform="translate(0, 72)">
                                        <g id="text_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:204;h:60">
                                            <g id="tx-rt-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:0;w:168;h:24" fill="#ff00001a" transform="translate(36, 0)">
                                                <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                            </g>
                                            <g id="tx-rt-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:36;w:168;h:24" fill="#ff00001a" transform="translate(36, 36)">
                                                <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                            </g>
                                            <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 0)" width="24" height="24" rx="0" ry="0"/>
                                            <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, -24)" width="24" height="24" rx="0" ry="0"/>
                                            <rect id="bt-cc-add-1_1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 60)" width="24" height="24" rx="0" ry="0"/>
                                        </g>
                                        <g id="g-1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:419;y:11.500;w:368;h:169" transform="translate(419, 11.5)">
                                            <path id="Vector_49" transform="translate(43.25, 29.5)" fill="#fefbdb" d="M 0 145.5 L 125.75 69.8413 M 125.75 69.8413 L 241 0.5 L 241 0 L 125.75 0 L 125.75 64 L 125.75 69 L 125.75 69.8413 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43.250;y:29.500;w:241;h:145.500"/>
                                            <path id="Vector_50" transform="translate(169, 24.500146865844727)" fill="#fefbdb" d="M 0 0 L 115.25 0 L 115.25 5 L 0 5 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:169;y:24.500;w:115.250;h:5.000"/>
                                            <path id="Vector_51" transform="translate(169, 0)" fill="#fefbdb" d="M 115.25 24 L 74.75 0 L 0 24.5 L 115.25 24.5 L 115.25 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:169;y:0;w:115.250;h:24.500"/>
                                            <path id="Vector_52" transform="translate(0, 98.5)" fill="#fefbdb" d="M 43.25 76.5 L 0 51 L 0 0 L 169 0 L 169 0.8413 L 43.25 76.5 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:98.500;w:169;h:76.500"/>
                                            <path id="Vector_53" transform="translate(0, 93.5)" fill="#fefbdb" d="M 0 0 L 169 0 L 169 5 L 0 5 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:93.500;w:169;h:5"/>
                                            <path id="Vector_54" transform="translate(0, 24.663915634155273)" fill="#fefbdb" d="M 0 68.3361 L 16.25 58.6037 L 43 41.3359 L 69.0176 32.9894 L 169 0 L 169 4.8361 L 169 68.8361 L 0 68.8361 L 0 68.3361 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:24.664;w:169;h:68.836"/>
                                            <path id="Vector_55" transform="translate(0, 30)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 284.25 0 L 43.25 145 L 0 119.5" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:30;w:284.250;h:145"/>
                                            <path id="Vector_56" transform="translate(0, 93)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 56.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:93;w:0;h:56.500"/>
                                            <path id="Vector_57" transform="translate(43, 119.5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.25 55.5 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:43;y:119.500;w:0.250;h:55.500"/>
                                            <path id="Vector_58" transform="translate(284.25, 24)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 L 0 6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:284.250;y:24;w:0;h:6"/>
                                            <path id="Vector_59" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 284.25 24 L 243.75 0 L 43 66 L 0 93 L 43 119.5 L 89.75 90.5 L 284.25 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:284.250;h:119.500"/>
                                            <path id="Vector_60" transform="translate(10.5, 116.625)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 5.25 27 L 0 24 L 7.5 0 L 13.5 3.75 L 21 35.25 L 13.5 31.5 L 12.75 27 L 6 23.25 L 5.25 27 Z M 7.5 18 L 12.75 21 L 9.75 9.75 L 7.5 18 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10.500;y:116.625;w:21;h:35.250"/>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                    <g id="g-0" data-entity-classes="KeepStroke" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:281;y:50;w:25;h:75" transform="translate(281, 50)">
                        <path id="Vector_61" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 16 70 C 16 72.7614 12.4183 75 8 75 C 3.5817 75 0 72.7614 0 70 C 0 67.2386 3.5817 65 8 65 C 12.4183 65 16 67.2386 16 70 Z M 8.125 9.75 L 8.125 25.7173 L 25 15.9673 L 25 0 L 8.125 9.75 Z M 8.125 25.9673 L 8.125 70.25" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:25;h:75"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>