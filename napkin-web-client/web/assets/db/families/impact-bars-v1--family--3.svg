<svg xmlns="http://www.w3.org/2000/svg" width="612" height="288">
    <g id="impact-bars-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L612 0 L612 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:612;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:612;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:24;primary:MIN;counter:MAX" data-position="x:0;y:0;w:612;h:240">
                <g id="visual" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:300;h:192" transform="translate(24, 24)">
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:216;y:36;w:60;h:156" transform="translate(216, 36)">
                        <g id="cu_Rectangle_613" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:156">
                            <path id="Rectangle_613" fill="#ffedeb" d="M 8.839e-17 30 C -3.9786e-8 13.4315 13.4315 3.9786e-8 30 8.839e-17 C 46.5685 -3.9786e-8 60 13.4315 60 30 L 60 126 C 60 142.5685 46.5685 156 30 156 C 13.4315 156 2.7031e-7 142.5685 2.3053e-7 126 L 8.839e-17 30 Z"/>
                            <path id="Rectangle_613_1" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 8.839e-17 30 C -3.9786e-8 13.4315 13.4315 3.9786e-8 30 8.839e-17 C 46.5685 -3.9786e-8 60 13.4315 60 30 L 60 126 C 60 142.5685 46.5685 156 30 156 C 13.4315 156 2.7031e-7 142.5685 2.3053e-7 126 L 8.839e-17 30 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:141.084;y:113.084;w:74.689;h:78.985" transform="translate(141.08364868164062, 113.08363342285156)">
                        <g id="cu_Rectangle_613_1" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.000;y:0;w:74.689;h:78.985">
                            <path id="Rectangle_613_2" fill="#fef2e6" d="M 20.9591 11.6444 C 31.0974 -1.4602 49.9395 -3.8649 63.0441 6.2734 C 76.1488 16.4117 78.5534 35.2539 68.4151 48.3585 L 53.7295 67.3409 C 43.5911 80.4455 24.749 82.8502 11.6444 72.7119 C -1.4602 62.5735 -3.8649 43.7314 6.2734 30.6268 L 20.9591 11.6444 Z"/>
                            <path id="Rectangle_613_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 20.9591 11.6444 C 31.0974 -1.4602 49.9395 -3.8649 63.0441 6.2734 C 76.1488 16.4117 78.5534 35.2539 68.4151 48.3585 L 53.7295 67.3409 C 43.5911 80.4455 24.749 82.8502 11.6444 72.7119 C -1.4602 62.5735 -3.8649 43.7314 6.2734 30.6268 L 20.9591 11.6444 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:23;y:132;w:60;h:60" transform="translate(23, 132)">
                        <g id="cu_Ellipse_532" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:60">
                            <path id="Ellipse_532" fill="#fefbdb" d="M 60 30 C 60 46.5685 46.5685 60 30 60 C 13.4315 60 0 46.5685 0 30 C 0 13.4315 13.4315 0 30 0 C 46.5685 0 60 13.4315 60 30 Z"/>
                            <path id="Ellipse_532_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 60 30 C 60 46.5685 46.5685 60 30 60 C 13.4315 60 0 46.5685 0 30 C 0 13.4315 13.4315 0 30 0 C 46.5685 0 60 13.4315 60 30 Z"/>
                        </g>
                    </g>
                    <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:144;w:36;h:36" fill="#33de7b1a" transform="translate(36, 144)">
                        <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                            <path id="icon_1" transform="translate(8, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.0086 4.0934 14.0001 8.2145 21 12.3217 C 16.9726 14.5481 10.4706 18.1014 5.7676 20.6673 C 2.4311 22.4876 0 23.8109 0 23.8109 L 0.0001 0 Z M 21 12.3217 C 16.6911 16.8448 12.4614 21.4408 8.1924 26 C 7.3859 24.2222 6.6079 22.4301 5.7676 20.6673" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:5;w:21;h:26"/>
                        </g>
                    </g>
                    <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:153;y:144;w:36;h:36" fill="#33de7b1a" transform="translate(153, 144)">
                        <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                            <path id="icon_3" transform="translate(8, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.0086 4.0934 14.0001 8.2145 21 12.3217 C 16.9726 14.5481 10.4706 18.1014 5.7676 20.6673 C 2.4311 22.4876 0 23.8109 0 23.8109 L 0.0001 0 Z M 21 12.3217 C 16.6911 16.8448 12.4614 21.4408 8.1924 26 C 7.3859 24.2222 6.6079 22.4301 5.7676 20.6673" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:5;w:21;h:26"/>
                        </g>
                    </g>
                    <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:144;w:36;h:36" fill="#33de7b1a" transform="translate(228, 144)">
                        <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                            <path id="icon_5" transform="translate(8, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.0086 4.0934 14.0001 8.2145 21 12.3217 C 16.9726 14.5481 10.4706 18.1014 5.7676 20.6673 C 2.4311 22.4876 0 23.8109 0 23.8109 L 0.0001 0 Z M 21 12.3217 C 16.6911 16.8448 12.4614 21.4408 8.1924 26 C 7.3859 24.2222 6.6079 22.4301 5.7676 20.6673" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:8;y:5;w:21;h:26"/>
                        </g>
                    </g>
                    <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:24;y:0;w:180;h:60" transform="translate(24, 0)">
                        <g id="tx-lt-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-1-desc" data-entity-classes="Description" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:36;w:180;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                    </g>
                </g>
                <g id="row" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:348;y:72;w:240;h:144" transform="translate(348, 72)">
                    <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:240;h:60">
                        <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:24" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:240;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, -24)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:84;w:240;h:60" transform="translate(0, 84)">
                        <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:24" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:240;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 108, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>