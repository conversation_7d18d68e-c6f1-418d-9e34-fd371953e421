<svg xmlns="http://www.w3.org/2000/svg" width="360" height="360">    <g id="funnel-cone-v1--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L360 0 L360 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:360;h:24"></path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:360;h:0">            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:360;h:312">                <g id="visual" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 0 24 0;gap:0;primary:MIN;counter:CENTER" data-position="x:13.500;y:0;w:333;h:144" transform="translate(13.5, 0)">                    <g id="visual_1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:24;w:333;h:96" transform="translate(0, 24)">                        <g id="g-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.500;y:16.723;w:156.027;h:63.150" transform="translate(0.5, 16.72265625)">                            <g id="cu_Vector"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:156.027;h:63.150" >                                <path id="Vector" fill="#fef2e6" d="M156.027 31.5752 C156.027 49.0137 150.642 63.1504 144 63.1504 C143.675 63.1504 143.353 63.1166 143.035 63.0502 C142.999 63.0429 142.964 63.0351 142.929 63.0269 C136.788 61.6041 131.973 48.066 131.973 31.5752 C131.973 15.0844 136.788 1.5462 142.929 0.1235 C142.964 0.1153 142.999 0.1075 143.035 0.1002 C143.353 0.0338 143.675 0 144 0 C150.642 0 156.027 14.1367 156.027 31.5752 Z M131.973 31.5752 C131.973 15.0844 136.788 1.5462 142.929 0.1235 L0 31.575 L142.929 63.0269 C136.788 61.6041 131.973 48.066 131.973 31.5752 Z"></path>
                                <path id="Vector_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 156.0271 31.5752 C 156.0271 49.0137 150.6423 63.1504 143.9999 63.1504 C 143.675 63.1504 143.3532 63.1166 143.0349 63.0502 C 142.9994 63.0428 142.9641 63.0351 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 C 142.9641 0.1153 142.9995 0.1075 143.0349 0.1002 C 143.3532 0.0338 143.675 0 143.9999 0 C 150.6423 0 156.0271 14.1367 156.0271 31.5752 Z M 131.9727 31.5752 C 131.9727 15.0844 136.7881 1.5462 142.9288 0.1235 L 0 31.575 L 142.9287 63.0269 C 136.7881 61.6041 131.9727 48.066 131.9727 31.5752 Z M 143.0349 0.1002 L 142.9288 0.1235 M 143.0349 63.0502 L 142.9287 63.0269"></path></g></g>
                        <g id="g-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:156.703;y:-20.232;w:173.806;h:137.061" transform="translate(156.703125, -20.232421875)">                            <g id="cu_Vector_1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:173.806;h:137.061" >                                <path id="Vector_2" fill="#fefbdb" d="M173.806 68.5303 C173.806 89.8896 171.238 108.967 167.213 121.535 C164.106 131.237 160.13 137.061 155.797 137.061 C155.445 137.061 155.095 137.022 154.748 136.946 C154.743 136.945 154.738 136.944 154.733 136.943 C145.282 134.849 137.788 105.02 137.788 68.5303 C137.788 32.0404 145.283 2.2115 154.734 0.1175 C154.738 0.1164 154.743 0.1153 154.748 0.1142 C155.095 0.0384 155.445 0 155.797 0 C160.13 0 164.105 5.8232 167.213 15.5252 C171.238 28.0934 173.806 47.1707 173.806 68.5303 Z M10.9814 31.746 C4.8465 33.0557 0 49.0238 0 68.5298 C0 88.0357 4.8465 104.004 10.9814 105.314 L154.733 136.943 C145.282 134.849 137.788 105.02 137.788 68.5303 C137.788 32.0404 145.283 2.2115 154.734 0.1175 L10.9814 31.746 Z"></path>
                                <path id="Vector_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 173.8057 68.5303 C 173.8057 89.8896 171.2378 108.9667 167.2128 121.5349 C 164.1055 131.2372 160.1299 137.0605 155.7969 137.0605 C 155.4449 137.0605 155.0953 137.0221 154.7483 136.9463 C 154.7434 136.9452 154.7384 136.9441 154.7335 136.9431 C 145.2825 134.8489 137.7881 105.0201 137.7881 68.5303 C 137.7881 32.0404 145.2825 2.2115 154.7336 0.1175 C 154.7385 0.1164 154.7434 0.1153 154.7483 0.1142 C 155.0953 0.0384 155.4449 0 155.7969 0 C 160.1298 0 164.1054 5.8232 167.2126 15.5252 C 171.2377 28.0934 173.8057 47.1707 173.8057 68.5303 Z M 10.9814 31.746 C 4.8465 33.0557 0 49.0238 0 68.5298 C 0 88.0357 4.8465 104.0039 10.9814 105.3136 L 154.7335 136.9431 C 145.2825 134.8489 137.7881 105.0201 137.7881 68.5303 C 137.7881 32.0404 145.2825 2.2115 154.7336 0.1175 L 10.9814 31.746 Z M 11.7973 105.4004 C 11.5231 105.4004 11.251 105.3712 10.9814 105.3136 M 11.7973 31.6592 C 11.5231 31.6592 11.251 31.6884 10.9814 31.746 M 154.7483 0.1142 L 154.7336 0.1175 M 154.7483 136.9463 L 154.7335 136.9431"></path></g></g></g></g>
                <g id="Frame_597" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 12 12 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:144;w:360;h:168" transform="translate(0, 144)">                    <g id="g-1_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:83;primary:MAX;counter:CENTER" data-position="x:12;y:0;w:168;h:84" transform="translate(12, 0)">                        <g id="bubble-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:168;h:84">                            <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">                                <g id="tx-ct-1" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                    <text id="Label"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                                <g id="tx-ct-1-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                    <text id="Label_1"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g></g></g>
                    <g id="g-2_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:36 0 36 0;gap:24;primary:MAX;counter:CENTER" data-position="x:180;y:0;w:168;h:156" transform="translate(180, 0)">                        <g id="bubble-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:12 12 12 12;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:36;w:168;h:84" transform="translate(0, 36)">                            <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:12;y:12;w:144;h:60" transform="translate(12, 12)">                                <g id="tx-ct-2" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">                                    <text id="Label_2"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:TOP"></text></g>
                                <g id="tx-ct-2-desc" data-entity-classes="Description"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">                                    <text id="Label_3"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:CENTER;vertical:TOP"></text></g></g></g></g>
                    <rect id="bt-cc-add-1"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 0, 12)" width="24" height="24" rx="0" ry="0"></rect>
                    <rect id="bt-cc-add-2"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:168;y:48;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 168, 48)" width="24" height="24" rx="0" ry="0"></rect>
                    <rect id="bt-cc-add-3"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:336;y:84;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 84)" width="24" height="24" rx="0" ry="0"></rect></g></g></g></g></svg>