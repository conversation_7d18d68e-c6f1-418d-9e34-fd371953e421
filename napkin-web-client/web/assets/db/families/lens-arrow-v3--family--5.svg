<svg xmlns="http://www.w3.org/2000/svg" width="1020" height="768">
    <g id="lens-arrow-v3--family--5" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1020 0 L1020 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1020;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1020;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:1020;h:720">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:228;y:24;w:781.000;h:673" transform="translate(228, 24)">
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.062;y:336.230;w:559.944;h:335.997" transform="translate(0.062255859375, 336.23046875)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:559.944;h:335.997">
                            <path id="Subtract" fill="#faf0ff" d="M 282.3765 186.1963 L 462.8773 0 L 474.233 11.7143 C 496.7153 34.9066 527.637 47.9972 559.9377 47.9972 L 559.944 47.9972 C 553.6213 47.9972 547.5684 50.5596 543.1676 55.0993 L 351.3047 253.0161 C 299.8851 306.0582 229.1644 335.9972 155.2899 335.9972 L 0 335.9972 L 0 335.9273 L 30.9377 287.9971 L 0 240.0668 L 0 239.9972 L 155.2899 239.9972 C 203.1866 239.9972 249.0385 220.5862 282.3765 186.1963 Z"/>
                            <path id="Subtract_1" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 282.3765 186.1963 L 462.8773 0 L 474.233 11.7143 C 496.7153 34.9066 527.637 47.9972 559.9377 47.9972 L 559.944 47.9972 C 553.6213 47.9972 547.5684 50.5596 543.1676 55.0993 L 351.3047 253.0161 C 299.8851 306.0582 229.1644 335.9972 155.2899 335.9972 L 0 335.9972 L 0 335.9273 L 30.9377 287.9971 L 0 240.0668 L 0 239.9972 L 155.2899 239.9972 C 203.1866 239.9972 249.0385 220.5862 282.3765 186.1963 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.062;y:336.211;w:374.737;h:192.015" transform="translate(0.062255859375, 336.2109375)">
                        <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:374.737;h:192.015">
                            <path id="Subtract_2" fill="#feecf7" d="M 374.737 48.0154 C 334.6919 48.0154 296.356 31.7881 268.4807 3.0378 L 265.5354 0 L 187.7908 80.2073 C 177.9965 90.3119 164.5249 96.0154 150.4525 96.0154 L 0 96.0154 L 0 96.0852 L 30.9377 144.0154 L 0 191.9456 L 0 192.0154 L 150.4525 192.0154 C 190.5047 192.0154 228.8468 175.7823 256.7231 147.023 L 337.368 63.8235 C 347.1623 53.7189 360.6339 48.0154 374.7063 48.0154 L 374.737 48.0154 Z"/>
                            <path id="Subtract_3" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 374.737 48.0154 C 334.6919 48.0154 296.356 31.7881 268.4807 3.0378 L 265.5354 0 L 187.7908 80.2073 C 177.9965 90.3119 164.5249 96.0154 150.4525 96.0154 L 0 96.0154 L 0 96.0852 L 30.9377 144.0154 L 0 191.9456 L 0 192.0154 L 150.4525 192.0154 C 190.5047 192.0154 228.8468 175.7823 256.7231 147.023 L 337.368 63.8235 C 347.1623 53.7189 360.6339 48.0154 374.7063 48.0154 L 374.737 48.0154 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:288.227;w:265.598;h:96" transform="translate(0, 288.2265625)">
                        <g id="cu_Subtract_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:265.598;h:96">
                            <path id="Subtract_4" fill="#ffedeb" d="M 0 95.9997 L 31 48 L 0 0.0003 L 219.0735 0 L 265.5977 47.9846 L 219.0566 96 L 0 95.9997 Z"/>
                            <path id="Subtract_5" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9997 L 31 48 L 0 0.0003 L 219.0735 0 L 265.5977 47.9846 L 219.0566 96 L 0 95.9997 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.006;y:144.227;w:462.933;h:240" transform="translate(0.00634765625, 144.2265625)">
                        <g id="cu_Subtract_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:462.933;h:240">
                            <path id="Subtract_6" fill="#fef2e6" d="M 0 95.9926 L 30.9937 48 L 0 0.0074 L 0 0 L 150.5168 0 C 190.5619 0 228.8979 16.2274 256.7732 44.9777 L 337.4596 128.1971 C 347.2536 138.2985 360.723 144 374.7929 144 L 416.3996 144 L 462.9332 192.0029 L 416.4043 240 L 374.7929 240 C 334.7478 240 296.4119 223.7727 268.5366 195.0223 L 187.8501 111.803 C 178.0561 101.7015 164.5867 96 150.5168 96 L 0 96 L 0 95.9926 Z"/>
                            <path id="Subtract_7" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9926 L 30.9937 48 L 0 0.0074 L 0 0 L 150.5168 0 C 190.5619 0 228.8979 16.2274 256.7732 44.9777 L 337.4596 128.1971 C 347.2536 138.2985 360.723 144 374.7929 144 L 416.3996 144 L 462.9332 192.0029 L 416.4043 240 L 374.7929 240 C 334.7478 240 296.4119 223.7727 268.5366 195.0223 L 187.8501 111.803 C 178.0561 101.7015 164.5867 96 150.5168 96 L 0 96 L 0 95.9926 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.062;y:0.227;w:780.938;h:384" transform="translate(0.062255859375, 0.2265625)">
                        <g id="cu_Subtract_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:780.938;h:384">
                            <path id="Subtract_8" fill="#fefbdb" d="M 0 95.9302 L 30.9377 48 L 0 0.0698 L 0 0 L 155.2884 0 C 229.1639 0 299.8856 29.9398 351.3054 82.9832 L 543.162 280.898 C 547.5626 285.4377 553.6152 288 559.9377 288 L 749.9375 288 L 780.9377 336 L 749.9376 383.9999 L 559.9377 384 C 527.637 384 496.7153 370.9094 474.233 347.7171 L 282.3763 149.8023 C 249.0383 115.4115 203.1857 96 155.2884 96 L 0 96 L 0 95.9302 Z"/>
                            <path id="Subtract_9" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 95.9302 L 30.9377 48 L 0 0.0698 L 0 0 L 155.2884 0 C 229.1639 0 299.8856 29.9398 351.3054 82.9832 L 543.162 280.898 C 547.5626 285.4377 553.6152 288 559.9377 288 L 749.9375 288 L 780.9377 336 L 749.9376 383.9999 L 559.9377 384 C 527.637 384 496.7153 370.9094 474.233 347.7171 L 282.3763 149.8023 C 249.0383 115.4115 203.1857 96 155.2884 96 L 0 96 L 0 95.9302 Z"/>
                        </g>
                    </g>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:60;h:48" fill="#ff00001a" transform="translate(12, 48)">
                    <text id="1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:0.227;w:48;h:48" fill="#d1bd08" transform="translate(6, 0.2265625)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:48.227;w:144;h:48" transform="translate(72, 48.2265625)">
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="ic-cc-end" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:925;y:336.227;w:48;h:48" fill="#33de7b1a" transform="translate(925, 336.2265625)">
                    <g id="icon" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12.800;y:12.801;w:22.800;h:22.800" transform="translate(12.800048828125, 12.80078125)">
                        <path id="icon_1" transform="translate(0, -3.19921875)" fill="none" stroke="#484848" stroke-width="1.600000023841858" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 7.7428 4.5342 15.4667 9.0991 23.2 13.6487 C 18.7507 16.1148 11.5676 20.0508 6.3718 22.893 C 2.6858 24.9093 0 26.3751 0 26.3751 L 0.0002 0 Z M 23.2 13.6487 C 18.4396 18.6589 13.7669 23.7498 9.0506 28.8 C 8.1597 26.8307 7.3001 24.8457 6.3718 22.893" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-3.199;w:23.200;h:28.800"/>
                    </g>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:192;w:60;h:48" fill="#ff00001a" transform="translate(12, 192)">
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#db8333" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:192.227;w:144;h:48" transform="translate(72, 192.2265625)">
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:336;w:60;h:48" fill="#ff00001a" transform="translate(12, 336)">
                    <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#df5e59" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:336.227;w:144;h:48" transform="translate(72, 336.2265625)">
                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:480;w:60;h:48" fill="#ff00001a" transform="translate(12, 480)">
                    <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#d95da7" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:480.227;w:144;h:48" transform="translate(72, 480.2265625)">
                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-cc-5-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:624;w:60;h:48" fill="#ff00001a" transform="translate(12, 624)">
                    <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#b960e2" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:624.227;w:144;h:48" transform="translate(72, 624.2265625)">
                    <g id="tx-lc-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:12.000;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 12.000001907348633)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="tx-rc-end" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:768;y:348.227;w:144;h:24" fill="#ff00001a" transform="translate(768, 348.2265625)">
                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                </g>
                <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:60.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 60.2265625)">
                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:204.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 204.2265625)">
                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-3" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:348.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 348.2265625)">
                    <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-4" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:492.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 492.2265625)">
                    <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-5" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:636.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 636.2265625)">
                    <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
            </g>
        </g>
    </g>
</svg>