<svg xmlns="http://www.w3.org/2000/svg" width="804" height="600">
    <g id="impact-pool-v1--family--7" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L804 0 L804 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:804;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:804;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 24 0 24;gap:36;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:804;h:552">
                <g id="container" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:24;y:24;w:192;h:228" transform="translate(24, 24)">
                    <g id="text-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:192;h:60">
                        <g id="tx-rt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <g id="tx-rt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, 0)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:84;w:192;h:60" transform="translate(0, 84)">
                        <g id="tx-rt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <g id="tx-rt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, 0)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:168;w:192;h:60" transform="translate(0, 168)">
                        <g id="tx-rt-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <g id="tx-rt-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 192, 0)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-8" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Frame_83" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:252;y:24;w:300;h:528" transform="translate(252, 24)">
                    <g id="top" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:18;y:0;w:264;h:240" transform="translate(18, 0)">
                        <g id="g-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:156;w:84;h:84" transform="translate(90, 156)">
                            <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract" fill="#edf4ff" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_1" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:135;y:78;w:84;h:84" transform="translate(135, 78)">
                            <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract_2" fill="#f3f0ff" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_3" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:45;y:78;w:84;h:84" transform="translate(45, 78)">
                            <g id="cu_Subtract_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract_4" fill="#faf0ff" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_5" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:90;y:0;w:84;h:84" transform="translate(90, 0)">
                            <g id="cu_Subtract_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract_6" fill="#feecf7" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_7" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:0;w:84;h:84" transform="translate(180, 0)">
                            <g id="cu_Subtract_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract_8" fill="#ffedeb" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_9" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                            <g id="cu_Subtract_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Subtract_10" fill="#fef2e6" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                                <path id="Subtract_11" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z M 60 42 C 60 51.9411 51.9411 60 42 60 C 32.0589 60 24 51.9411 24 42 C 24 32.0589 32.0589 24 42 24 C 51.9411 24 60 32.0589 60 42 Z"/>
                            </g>
                        </g>
                        <g id="tx-cc-7-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:114;y:180;w:36;h:36" fill="#ff00001a" transform="translate(114, 180)">
                            <text id="6" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#4987ec" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">6</text>
                        </g>
                        <g id="tx-cc-6-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:159;y:102;w:36;h:36" fill="#ff00001a" transform="translate(159, 102)">
                            <text id="5" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#7e62ec" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                        </g>
                        <g id="tx-cc-5-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:69;y:102;w:36;h:36" fill="#ff00001a" transform="translate(69, 102)">
                            <text id="4" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#b960e2" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                        </g>
                        <g id="tx-cc-4-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:114;y:24;w:36;h:36" fill="#ff00001a" transform="translate(114, 24)">
                            <text id="3" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#d95da7" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                        </g>
                        <g id="tx-cc-3-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:24;w:36;h:36" fill="#ff00001a" transform="translate(204, 24)">
                            <text id="2" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#df5e59" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                        </g>
                        <g id="tx-cc-2-number" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:24;y:24;w:36;h:36" fill="#ff00001a" transform="translate(24, 24)">
                            <text id="1" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#db8333" font-size="25" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                        </g>
                    </g>
                    <g id="text-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:0;y:264;w:300;h:24;hMin:24" transform="translate(0, 264)">
                        <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:300;h:24" fill="#ff00001a">
                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:300;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                    </g>
                    <g id="bottom" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:102;y:312;w:96;h:216" transform="translate(102, 312)">
                        <g id="g-0c" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:0;w:84;h:110" transform="translate(6, 0)">
                            <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:33;y:102;w:18;h:8">
                                <path id="Vector" transform="translate(33, 102)" fill="#f6f6f6" d="M 12 0 L 6 0 C 2.6863 0 0 2.6863 0 6 L 0 8 L 18 8 L 18 6 C 18 2.6863 15.3137 0 12 0 Z"/>
                                <path id="Vector_1" transform="translate(33, 102)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 12 0 L 6 0 C 2.6863 0 0 2.6863 0 6 L 0 8 L 18 8 L 18 6 C 18 2.6863 15.3137 0 12 0 Z"/>
                            </g>
                            <g id="cu_Ellipse_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:84">
                                <path id="Ellipse_5" fill="#f6f6f6" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z"/>
                                <path id="Ellipse_5_1" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 84 42 C 84 65.196 65.196 84 42 84 C 18.804 84 0 65.196 0 42 C 0 18.804 18.804 0 42 0 C 65.196 0 84 18.804 84 42 Z"/>
                            </g>
                        </g>
                        <g id="g-0b" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:39;y:134;w:18;h:82" transform="translate(39, 134)">
                            <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:18;h:82">
                                <path id="Vector_2" fill="#fef2e6" d="M 18 0 L 0 0 L 0 82 L 18 82 L 18 0 Z"/>
                                <path id="Vector_3" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 18 0 L 0 0 L 0 82 L 18 82 L 18 0 Z"/>
                            </g>
                        </g>
                        <g id="g-0a" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:85.889;w:96;h:48.111" transform="translate(0, 85.888671875)">
                            <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:39;y:24.111;w:18;h:24">
                                <path id="Vector_4" transform="translate(39, 24.111328125)" fill="#f0f0f0" d="M 18 0 L 0 0 L 0 24 L 18 24 L 18 0 Z"/>
                                <path id="Vector_5" transform="translate(39, 24.111328125)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 18 0 L 0 0 L 0 24 L 18 24 L 18 0 Z"/>
                            </g>
                            <path id="Vector_6" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 19.8327 L 26.5653 14.8327 M 17.9706 37 L 30 22.3327 M 11.72 0 L 27 6.3327" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:30;h:37"/>
                            <path id="Vector_7" transform="translate(66, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 30 19.8327 L 3.4347 14.8327 M 12.0294 37 L 0 22.3327 M 18.28 0 L 3 6.3327" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:0;w:30;h:37"/>
                        </g>
                    </g>
                </g>
                <g id="list-2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:CENTER" data-position="x:588;y:24;w:192;h:228" transform="translate(588, 24)">
                    <g id="container_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:192;h:228">
                        <g id="text-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:192;h:60">
                            <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:84;w:192;h:60" transform="translate(0, 84)">
                            <g id="tx-lt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <g id="text-7" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:168;w:192;h:60" transform="translate(0, 168)">
                            <g id="tx-lt-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <g id="tx-lt-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-remove-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>