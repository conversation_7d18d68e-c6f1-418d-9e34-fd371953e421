<svg xmlns="http://www.w3.org/2000/svg" width="672" height="216">    <g id="list-postits-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L672 0 L672 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:672;h:24"></path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:672;h:0">            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:24;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:672;h:168">                <g id="column" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:24;y:24;w:192;h:108" transform="translate(24, 24)">                    <g id="bubble-1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:108">                        <path id="ct_fill" transform="translate(0, -0.0000152587890625)" fill="#fefbdb" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill" transform="translate(0, 48)" fill="#fefbdb" d="M192 12 L192 0 L0 1.2239e-6 L3.5968e-6 12 L192 12 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:12"></path>
                        <path id="cb_fill" transform="translate(0, 60)" fill="#fefbdb" d="M0 0 L0 48 L107.068 48 C132.719 48 160 48 185 42 C185 42 192 33 192 0 L0 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:60;w:192;h:48"></path>
                        <path id="ct_stroke" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke" transform="translate(192, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:12"></path>
                        <path id="rc_stroke" transform="translate(0, 48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:12"></path>
                        <path id="cb_stroke" transform="translate(0, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 107.0683 48 C 132.7191 48 160 48 185 42 C 185 42 192 33 192 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:60;w:192;h:48"></path>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:24;y:24;w:144;h:60;hMin:60;hMax:240" transform="translate(24, 24)">                            <g id="tx-cc-1" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60;hMin:60" fill="#ff00001a">                                <text id="Label"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g></g>
                        <rect id="bt-cc-add-1"  data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:-24;y:42;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, -24, 42)" width="24" height="24" rx="0" ry="0"></rect>
                        <rect id="bt-cc-remove-1"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g>
                <g id="column_1" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:12 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:240;y:24;w:192;h:120" transform="translate(240, 24)">                    <g id="bubble-2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:12;w:192;h:108" transform="translate(0, 12)">                        <path id="ct_fill_1" transform="translate(0, -0.0000152587890625)" fill="#f2fae1" d="M192 48 L0 48 C5.7939e-7 21.4903 0 0 0 0 L192 4.5776e-5 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="cc_fill_1" transform="translate(0, 48)" fill="#f2fae1" d="M192 12 L192 0 L0 1.2239e-6 L3.5968e-6 12 L192 12 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:12"></path>
                        <path id="cb_fill_1" transform="translate(0, 60)" fill="#f2fae1" d="M192 0 L192 48 L54.5 48 C44.2659 48 26.3634 47.3677 19 42 L7.5 32.5 C4 29 0.7851 24.9087 0.451 17.8146 C0.2146 12.7946 0 7.0268 0 2.6307 L0 0 L192 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:60;w:192;h:48"></path>
                        <path id="ct_stroke_1" transform="translate(0, -0.000013637953088618815)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0.0001 L 0.0001 0 L 0 47.9999"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:-0.000;w:192;h:48"></path>
                        <path id="lc_stroke_1" transform="translate(192, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:12"></path>
                        <path id="rc_stroke_1" transform="translate(0, 48)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:12"></path>
                        <path id="cb_stroke_1" transform="translate(0, 60)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 0 L 192 48 L 54.5 48 C 44.2659 48 26.3634 47.3677 19 42 L 7.5 32.5 C 4 29 0.7851 24.9087 0.451 17.8146 C 0.2146 12.7946 0 7.0268 0 2.6307 L 0 0"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:60;w:192;h:48"></path>
                        <path id="cb_corner" transform="translate(0.5, 79)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 36 28 C 23 26 20.5 20.5 17 11.5 C 17 11.5 1.5 13 0 0"  data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.500;y:79;w:36;h:28"></path>
                        <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:24;y:24;w:144;h:60;hMin:60;hMax:240" transform="translate(24, 24)">                            <g id="tx-cc-2" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60;hMin:60" fill="#ff00001a">                                <text id="Label_1"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g></g>
                        <rect id="bt-cc-add-2"  data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:-25;y:42;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, -25, 42)" width="24" height="24" rx="0" ry="0"></rect>
                        <rect id="bt-cc-remove-2"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g>
                <g id="column_2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MAX;counter:CENTER" data-position="x:456;y:24;w:192;h:108" transform="translate(456, 24)">                    <g id="bubble-3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:24 24 24 24;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:192;h:108">                        <path id="cb_fill_2" transform="translate(3.979039320256561e-12, 60)" fill="#e7fbf2" d="M4.1963e-6 0 L192 6.2562e-5 C192 26.5097 192 48 192 48 L0 48 L4.1963e-6 0 Z"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:60;w:192;h:48.000"></path>
                        <path id="cc_fill_2" transform="translate(0, 48)" fill="#e7fbf2" d="M192 12 L192 0 L0 1.2239e-6 L3.5968e-6 12 L192 12 Z"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:192;h:12"></path>
                        <path id="ct_fill_2" transform="translate(0, 0.0000058985783653042745)" fill="#e7fbf2" d="M192 48 L192 9.3602e-6 L84.9317 1.3642e-12 C60.4032 -2.1444e-6 35.9672 3.0082 12.171 8.9572 L9.0896 9.7276 C3.7476 11.0631 2.8095e-6 15.8629 2.3281e-6 21.3693 L0 48 L192 48 Z"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="cb_stroke_2" transform="translate(4.433786671143025e-12, 60)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 0 L 0 48 L 192 48 L 192 0.0001"  data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0.000;y:60;w:192;h:48.000"></path>
                        <path id="lc_stroke_2" transform="translate(192, 48)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:48;w:0;h:12"></path>
                        <path id="rc_stroke_2" transform="translate(0, 48)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 12 L 0 0"  data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:48;w:0;h:12"></path>
                        <path id="ct_stroke_2" transform="translate(0, 0.0000058985783653042745)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 48 L 192 0 L 84.9317 1.1464e-12 C 60.4032 -0 35.9672 3.0082 12.171 8.9572 L 9.0896 9.7276 C 3.7476 11.0631 0 15.8629 0 21.3693 L 0 48"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0.000;w:192;h:48.000"></path>
                        <path id="ct_corner" transform="translate(7.000000476837158, 0.0001163482666015625)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 74 0 C 57.0247 2.5591 31.9444 4.9354 12.5 16.9999 C 12.5 16.9999 9.5 6.9999 0 10.4999"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:7.000;y:0.000;w:74;h:17.000"></path>
                        <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:24;y:24;w:144;h:60;hMin:60;hMax:240" transform="translate(24, 24)">                            <g id="tx-cc-3" data-entity-classes="DescTitle"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60;hMin:60" fill="#ff00001a">                                <text id="Label_2"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:144;h:60" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g></g>
                        <rect id="bt-cc-add-3"  data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:-25;y:42;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, -25, 42)" width="24" height="24" rx="0" ry="0"></rect>
                        <rect id="bt-cc-add-4"  data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:192;y:42;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 192, 42)" width="24" height="24" rx="0" ry="0"></rect>
                        <rect id="bt-cc-remove-3"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:-12;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 84, -12)" width="24" height="24" rx="0" ry="0"></rect></g></g></g></g></g></svg>