<svg xmlns="http://www.w3.org/2000/svg" width="1044" height="444">
    <g id="list-puzzle-v1--family--6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1044 0 L1044 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1044;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1044;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:24 24 24 24;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:1044;h:396">
                <g id="Column-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:24;y:24;w:156;h:348" transform="translate(24, 24)">
                    <g id="text-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:156;h:60">
                        <g id="tx-ct-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:84;w:132;h:264" transform="translate(12, 84)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-0.000;y:131.615;w:132;h:132">
                            <path id="Subtract" transform="translate(-1.1368683772161603e-13, 131.6154327392578)" fill="#fefbdb" d="M 66 132 C 102.4508 132 132 102.4508 132 66 C 132 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132 66 132 Z M 71.9802 27.9391 C 71.5039 28.683 71.9916 30.0314 72.8712 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.954 100.198 62.4447 99.4584 61.9656 C 98.3504 61.2478 97.0312 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0312 75.1314 98.3504 74.715 99.4584 73.9972 C 100.198 73.5181 101.5385 74.0088 101.5385 74.8936 L 101.5385 104.0293 C 101.5385 104.5229 101.1407 104.9231 100.65 104.9231 L 72.8712 104.9231 C 71.9916 104.9231 71.5039 103.5747 71.9802 102.8307 C 72.6937 101.7161 73.1077 100.389 73.1077 98.9647 C 73.1077 95.0159 69.9255 91.8147 66 91.8147 C 62.0745 91.8147 58.8923 95.0159 58.8923 98.9647 C 58.8923 100.389 59.3063 101.7161 60.0198 102.8307 C 60.4961 103.5747 60.0084 104.9231 59.1287 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4615 104.5229 30.4615 104.0293 L 30.4615 74.8936 C 30.4615 74.0087 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9687 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9687 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4447 30.4615 61.9541 30.4615 61.0693 L 30.4615 30.9252 C 30.4615 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1287 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0198 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                            <path id="Subtract_1" transform="translate(-1.1368683772161603e-13, 131.6154327392578)" fill="none" stroke="#d1bd08" stroke-width="1.692307472229004" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66 132 C 102.4508 132 132 102.4508 132 66 C 132 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132 66 132 Z M 71.9802 27.9391 C 71.5039 28.683 71.9916 30.0314 72.8712 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.954 100.198 62.4447 99.4584 61.9656 C 98.3504 61.2478 97.0312 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0312 75.1314 98.3504 74.715 99.4584 73.9972 C 100.198 73.5181 101.5385 74.0088 101.5385 74.8936 L 101.5385 104.0293 C 101.5385 104.5229 101.1407 104.9231 100.65 104.9231 L 72.8712 104.9231 C 71.9916 104.9231 71.5039 103.5747 71.9802 102.8307 C 72.6937 101.7161 73.1077 100.389 73.1077 98.9647 C 73.1077 95.0159 69.9255 91.8147 66 91.8147 C 62.0745 91.8147 58.8923 95.0159 58.8923 98.9647 C 58.8923 100.389 59.3063 101.7161 60.0198 102.8307 C 60.4961 103.5747 60.0084 104.9231 59.1287 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4615 104.5229 30.4615 104.0293 L 30.4615 74.8936 C 30.4615 74.0087 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9687 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9687 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4447 30.4615 61.9541 30.4615 61.0693 L 30.4615 30.9252 C 30.4615 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1287 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0198 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                        </g>
                        <g id="ic-cc-1" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:180;w:36;h:36" transform="translate(48, 180)">
                            <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_1" transform="translate(6.75, 3.75)" fill="none" stroke="#d1bd08" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:0;w:6;h:132" transform="translate(63, 0)">
                            <path id="Subtract_2" transform="translate(3.1837141513824463, 5.571232795715332)" fill="none" stroke="#d1bd08" stroke-width="1.5918411016464233" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 0 125.9287" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.184;y:5.571;w:0.000;h:125.929"/>
                            <g id="cu_Subtract-start" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.796;y:0.796;w:4.776;h:4.776">
                                <path id="Subtract-start" transform="translate(0.7959609627723694, 0.795956015586853)" fill="#ffffff" d="M 0 2.3878 C 8.0695e-17 3.7056 1.0699 4.7755 2.3878 4.7755 C 3.7056 4.7755 4.7755 3.7056 4.7755 2.3878 C 4.7755 1.0699 3.7056 -8.0695e-17 2.3878 0 C 1.0699 8.0695e-17 -8.0695e-17 1.0699 0 2.3878 Z"/>
                                <path id="Subtract-start_1" transform="translate(0.7959609627723694, 0.795956015586853)" fill="none" stroke="#d1bd08" stroke-width="1.5918411016464233" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 2.3878 C 8.0695e-17 3.7056 1.0699 4.7755 2.3878 4.7755 C 3.7056 4.7755 4.7755 3.7056 4.7755 2.3878 C 4.7755 1.0699 3.7056 -8.0695e-17 2.3878 0 C 1.0699 8.0695e-17 -8.0695e-17 1.0699 0 2.3878 Z"/>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="Column-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:192;y:24;w:156;h:348" transform="translate(192, 24)">
                    <g id="g-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:0;w:132;h:264" transform="translate(12, 0)">
                        <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132.000;h:132.000">
                            <path id="Subtract_3" fill="#fef2e6" d="M 65.9999 131.9999 C 102.4507 131.9999 131.9999 102.4507 131.9999 65.9999 C 131.9999 29.5492 102.4507 0 65.9999 0 C 29.5492 0 0 29.5492 0 65.9999 C 0 102.4507 29.5492 131.9999 65.9999 131.9999 Z M 71.9801 27.939 C 71.5038 28.683 71.9916 30.0314 72.8712 30.0314 L 100.6499 30.0314 C 101.1406 30.0314 101.5384 30.4315 101.5384 30.9251 L 101.5384 61.0691 C 101.5384 61.954 100.1979 62.4446 99.4583 61.9655 C 98.3503 61.2478 97.0311 60.8313 95.6153 60.8313 C 91.6898 60.8313 88.5076 64.0325 88.5076 67.9813 C 88.5076 71.9302 91.6898 75.1313 95.6153 75.1313 C 97.0311 75.1313 98.3503 74.7149 99.4583 73.9971 C 100.1979 73.5181 101.5384 74.0087 101.5384 74.8936 L 101.5384 104.0292 C 101.5384 104.5228 101.1406 104.923 100.6499 104.923 L 72.8712 104.923 C 71.9916 104.923 71.5038 103.5746 71.9801 102.8306 C 72.6936 101.716 73.1076 100.3889 73.1076 98.9646 C 73.1076 95.0158 69.9254 91.8146 65.9999 91.8146 C 62.0745 91.8146 58.8922 95.0158 58.8922 98.9646 C 58.8922 100.3889 59.3062 101.716 60.0198 102.8306 C 60.496 103.5746 60.0083 104.923 59.1287 104.923 L 31.35 104.923 C 30.8593 104.923 30.4615 104.5228 30.4615 104.0292 L 30.4615 74.8935 C 30.4615 74.0086 31.8019 73.518 32.5415 73.9971 C 33.6495 74.7149 34.9687 75.1313 36.3846 75.1313 C 40.3101 75.1313 43.4923 71.9302 43.4923 67.9813 C 43.4923 64.0325 40.3101 60.8313 36.3846 60.8313 C 34.9687 60.8313 33.6495 61.2478 32.5415 61.9656 C 31.8019 62.4447 30.4615 61.954 30.4615 61.0692 L 30.4615 30.9251 C 30.4615 30.4315 30.8593 30.0314 31.35 30.0314 L 59.1287 30.0314 C 60.0083 30.0314 60.496 28.683 60.0198 27.939 C 59.3062 26.8244 58.8922 25.4974 58.8922 24.0731 C 58.8922 20.1242 62.0745 16.9231 65.9999 16.9231 C 69.9254 16.9231 73.1076 20.1242 73.1076 24.0731 C 73.1076 25.4974 72.6936 26.8244 71.9801 27.939 Z"/>
                            <path id="Subtract_4" fill="none" stroke="#db8333" stroke-width="1.692309021949768" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 65.9999 131.9999 C 102.4507 131.9999 131.9999 102.4507 131.9999 65.9999 C 131.9999 29.5492 102.4507 0 65.9999 0 C 29.5492 0 0 29.5492 0 65.9999 C 0 102.4507 29.5492 131.9999 65.9999 131.9999 Z M 71.9801 27.939 C 71.5038 28.683 71.9916 30.0314 72.8712 30.0314 L 100.6499 30.0314 C 101.1406 30.0314 101.5384 30.4315 101.5384 30.9251 L 101.5384 61.0691 C 101.5384 61.954 100.1979 62.4446 99.4583 61.9655 C 98.3503 61.2478 97.0311 60.8313 95.6153 60.8313 C 91.6898 60.8313 88.5076 64.0325 88.5076 67.9813 C 88.5076 71.9302 91.6898 75.1313 95.6153 75.1313 C 97.0311 75.1313 98.3503 74.7149 99.4583 73.9971 C 100.1979 73.5181 101.5384 74.0087 101.5384 74.8936 L 101.5384 104.0292 C 101.5384 104.5228 101.1406 104.923 100.6499 104.923 L 72.8712 104.923 C 71.9916 104.923 71.5038 103.5746 71.9801 102.8306 C 72.6936 101.716 73.1076 100.3889 73.1076 98.9646 C 73.1076 95.0158 69.9254 91.8146 65.9999 91.8146 C 62.0745 91.8146 58.8922 95.0158 58.8922 98.9646 C 58.8922 100.3889 59.3062 101.716 60.0198 102.8306 C 60.496 103.5746 60.0083 104.923 59.1287 104.923 L 31.35 104.923 C 30.8593 104.923 30.4615 104.5228 30.4615 104.0292 L 30.4615 74.8935 C 30.4615 74.0086 31.8019 73.518 32.5415 73.9971 C 33.6495 74.7149 34.9687 75.1313 36.3846 75.1313 C 40.3101 75.1313 43.4923 71.9302 43.4923 67.9813 C 43.4923 64.0325 40.3101 60.8313 36.3846 60.8313 C 34.9687 60.8313 33.6495 61.2478 32.5415 61.9656 C 31.8019 62.4447 30.4615 61.954 30.4615 61.0692 L 30.4615 30.9251 C 30.4615 30.4315 30.8593 30.0314 31.35 30.0314 L 59.1287 30.0314 C 60.0083 30.0314 60.496 28.683 60.0198 27.939 C 59.3062 26.8244 58.8922 25.4974 58.8922 24.0731 C 58.8922 20.1242 62.0745 16.9231 65.9999 16.9231 C 69.9254 16.9231 73.1076 20.1242 73.1076 24.0731 C 73.1076 25.4974 72.6936 26.8244 71.9801 27.939 Z"/>
                        </g>
                        <g id="ic-cc-2" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48.000;y:48;w:36;h:36" transform="translate(47.9998779296875, 48)">
                            <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_3" transform="translate(6.75, 3.75)" fill="none" stroke="#df914a" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63.000;y:131;w:6;h:133" transform="translate(62.9998779296875, 131)">
                            <path id="Subtract_5" transform="translate(3.187742233276367, 0.7974296808242798)" fill="none" stroke="#db8333" stroke-width="1.5939677953720093" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0001 127.2026 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.188;y:0.797;w:0.000;h:127.203"/>
                            <path id="Subtract-start_2" transform="translate(0.7969163060188293, 127.99990844726562)" fill="none" stroke="#db8333" stroke-width="1.5939677953720093" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 2.3315e-15 2.3908 C 5.7678e-8 3.7103 1.0713 4.7816 2.3908 4.7816 C 3.7103 4.7816 4.7816 3.7103 4.7816 2.3908 C 4.7816 1.0713 3.7103 -5.7678e-8 2.3908 0 C 1.0713 5.7678e-8 -5.7678e-8 1.0713 2.3315e-15 2.3908 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.797;y:128.000;w:4.782;h:4.782"/>
                        </g>
                    </g>
                    <g id="text-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:288;w:156;h:60" transform="translate(0, 288)">
                        <g id="tx-ct-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Column-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:360;y:24;w:156;h:348" transform="translate(360, 24)">
                    <g id="text-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:156;h:60">
                        <g id="tx-ct-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                    </g>
                    <g id="g-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:84;w:132;h:264" transform="translate(12, 84)">
                        <g id="cu_Subtract_2" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:131.615;w:132.000;h:132.000">
                            <path id="Subtract_6" transform="translate(0, 131.61489868164062)" fill="#ffedeb" d="M 66.0001 132.0001 C 102.4509 132.0001 132.0001 102.4509 132.0001 66.0001 C 132.0001 29.5492 102.4509 0 66.0001 0 C 29.5492 0 0 29.5492 0 66.0001 C 0 102.4509 29.5492 132.0001 66.0001 132.0001 Z M 71.9802 27.9391 C 71.504 28.683 71.9917 30.0314 72.8713 30.0314 L 100.6501 30.0314 C 101.1408 30.0314 101.5386 30.4316 101.5386 30.9252 L 101.5386 61.0692 C 101.5386 61.9541 100.1981 62.4447 99.4585 61.9657 C 98.3505 61.2479 97.0313 60.8315 95.6155 60.8315 C 91.69 60.8315 88.5078 64.0326 88.5078 67.9815 C 88.5078 71.9303 91.69 75.1315 95.6155 75.1315 C 97.0313 75.1315 98.3505 74.715 99.4585 73.9973 C 100.1981 73.5182 101.5386 74.0088 101.5386 74.8937 L 101.5386 104.0294 C 101.5386 104.523 101.1408 104.9232 100.6501 104.9232 L 72.8713 104.9232 C 71.9917 104.9232 71.504 103.5748 71.9802 102.8308 C 72.6938 101.7162 73.1078 100.3891 73.1078 98.9648 C 73.1078 95.016 69.9255 91.8148 66.0001 91.8148 C 62.0746 91.8148 58.8924 95.016 58.8924 98.9648 C 58.8924 100.3891 59.3064 101.7162 60.0199 102.8308 C 60.4961 103.5748 60.0084 104.9232 59.1288 104.9232 L 31.35 104.9232 C 30.8593 104.9232 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5182 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1315 36.3846 75.1315 C 40.3101 75.1315 43.4923 71.9303 43.4923 67.9815 C 43.4923 64.0326 40.3101 60.8315 36.3846 60.8315 C 34.9688 60.8315 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9542 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3064 26.8244 58.8924 25.4974 58.8924 24.0731 C 58.8924 20.1242 62.0746 16.9231 66.0001 16.9231 C 69.9255 16.9231 73.1078 20.1242 73.1078 24.0731 C 73.1078 25.4974 72.6938 26.8244 71.9802 27.9391 Z"/>
                            <path id="Subtract_7" transform="translate(0, 131.61489868164062)" fill="none" stroke="#df5e59" stroke-width="1.6923030614852905" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66.0001 132.0001 C 102.4509 132.0001 132.0001 102.4509 132.0001 66.0001 C 132.0001 29.5492 102.4509 0 66.0001 0 C 29.5492 0 0 29.5492 0 66.0001 C 0 102.4509 29.5492 132.0001 66.0001 132.0001 Z M 71.9802 27.9391 C 71.504 28.683 71.9917 30.0314 72.8713 30.0314 L 100.6501 30.0314 C 101.1408 30.0314 101.5386 30.4316 101.5386 30.9252 L 101.5386 61.0692 C 101.5386 61.9541 100.1981 62.4447 99.4585 61.9657 C 98.3505 61.2479 97.0313 60.8315 95.6155 60.8315 C 91.69 60.8315 88.5078 64.0326 88.5078 67.9815 C 88.5078 71.9303 91.69 75.1315 95.6155 75.1315 C 97.0313 75.1315 98.3505 74.715 99.4585 73.9973 C 100.1981 73.5182 101.5386 74.0088 101.5386 74.8937 L 101.5386 104.0294 C 101.5386 104.523 101.1408 104.9232 100.6501 104.9232 L 72.8713 104.9232 C 71.9917 104.9232 71.504 103.5748 71.9802 102.8308 C 72.6938 101.7162 73.1078 100.3891 73.1078 98.9648 C 73.1078 95.016 69.9255 91.8148 66.0001 91.8148 C 62.0746 91.8148 58.8924 95.016 58.8924 98.9648 C 58.8924 100.3891 59.3064 101.7162 60.0199 102.8308 C 60.4961 103.5748 60.0084 104.9232 59.1288 104.9232 L 31.35 104.9232 C 30.8593 104.9232 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5182 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1315 36.3846 75.1315 C 40.3101 75.1315 43.4923 71.9303 43.4923 67.9815 C 43.4923 64.0326 40.3101 60.8315 36.3846 60.8315 C 34.9688 60.8315 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9542 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3064 26.8244 58.8924 25.4974 58.8924 24.0731 C 58.8924 20.1242 62.0746 16.9231 66.0001 16.9231 C 69.9255 16.9231 73.1078 20.1242 73.1078 24.0731 C 73.1078 25.4974 72.6938 26.8244 71.9802 27.9391 Z"/>
                        </g>
                        <g id="ic-cc-3" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:180;w:36;h:36" transform="translate(48, 180)">
                            <rect id="Rectangle_7_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_5" transform="translate(6.75, 3.75)" fill="none" stroke="#df5e59" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator_2" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:0;w:6;h:133" transform="translate(63, 0)">
                            <path id="Subtract_8" transform="translate(3.1878600120544434, 5.500211238861084)" fill="none" stroke="#df5e59" stroke-width="1.594042181968689" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 0 126" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.188;y:5.500;w:0;h:126"/>
                            <g id="cu_Subtract-start_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.797;y:0.797;w:4.781;h:4.781">
                                <path id="Subtract-start_3" transform="translate(0.7968043088912964, 0.7968140840530396)" fill="#ffffff" d="M 0 2.3906 C 8.0791e-17 3.7101 1.0712 4.7813 2.3906 4.7813 C 3.7101 4.7813 4.7813 3.7101 4.7813 2.3906 C 4.7813 1.0712 3.7101 -8.0791e-17 2.3906 0 C 1.0712 8.0791e-17 -8.0791e-17 1.0712 0 2.3906 Z"/>
                                <path id="Subtract-start_4" transform="translate(0.7968043088912964, 0.7968140840530396)" fill="none" stroke="#df5e59" stroke-width="1.594042181968689" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 2.3906 C 8.0791e-17 3.7101 1.0712 4.7813 2.3906 4.7813 C 3.7101 4.7813 4.7813 3.7101 4.7813 2.3906 C 4.7813 1.0712 3.7101 -8.0791e-17 2.3906 0 C 1.0712 8.0791e-17 -8.0791e-17 1.0712 0 2.3906 Z"/>
                            </g>
                        </g>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, -24)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="Column-4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:528;y:24;w:156;h:348" transform="translate(528, 24)">
                    <g id="g-4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:0;w:132;h:264" transform="translate(12, 0)">
                        <g id="cu_Subtract_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132.000;h:132.000">
                            <path id="Subtract_9" fill="#feecf7" d="M 66 132.0001 C 102.4508 132.0001 132.0001 102.4508 132.0001 66 C 132.0001 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132.0001 66 132.0001 Z M 71.9802 27.9391 C 71.5039 28.683 71.9917 30.0314 72.8713 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.9541 100.198 62.4447 99.4585 61.9656 C 98.3505 61.2479 97.0313 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0313 75.1314 98.3505 74.715 99.4585 73.9972 C 100.198 73.5182 101.5385 74.0088 101.5385 74.8937 L 101.5385 104.0294 C 101.5385 104.523 101.1407 104.9231 100.65 104.9231 L 72.8713 104.9231 C 71.9917 104.9231 71.5039 103.5747 71.9802 102.8308 C 72.6937 101.7161 73.1077 100.3891 73.1077 98.9648 C 73.1077 95.0159 69.9255 91.8148 66 91.8148 C 62.0745 91.8148 58.8923 95.0159 58.8923 98.9648 C 58.8923 100.3891 59.3063 101.7161 60.0199 102.8308 C 60.4961 103.5747 60.0084 104.9231 59.1288 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9688 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9541 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                            <path id="Subtract_10" fill="none" stroke="#d95da7" stroke-width="1.6923054456710815" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66 132.0001 C 102.4508 132.0001 132.0001 102.4508 132.0001 66 C 132.0001 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132.0001 66 132.0001 Z M 71.9802 27.9391 C 71.5039 28.683 71.9917 30.0314 72.8713 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.9541 100.198 62.4447 99.4585 61.9656 C 98.3505 61.2479 97.0313 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0313 75.1314 98.3505 74.715 99.4585 73.9972 C 100.198 73.5182 101.5385 74.0088 101.5385 74.8937 L 101.5385 104.0294 C 101.5385 104.523 101.1407 104.9231 100.65 104.9231 L 72.8713 104.9231 C 71.9917 104.9231 71.5039 103.5747 71.9802 102.8308 C 72.6937 101.7161 73.1077 100.3891 73.1077 98.9648 C 73.1077 95.0159 69.9255 91.8148 66 91.8148 C 62.0745 91.8148 58.8923 95.0159 58.8923 98.9648 C 58.8923 100.3891 59.3063 101.7161 60.0199 102.8308 C 60.4961 103.5747 60.0084 104.9231 59.1288 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9688 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9541 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                        </g>
                        <g id="ic-cc-4" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:48;w:36;h:36" transform="translate(48, 48)">
                            <rect id="Rectangle_7_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_7" transform="translate(6.75, 3.75)" fill="none" stroke="#d95da7" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator_3" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:131;w:6;h:133" transform="translate(63, 131)">
                            <path id="Subtract_11" transform="translate(3.1876330375671387, 0.7969588041305542)" fill="none" stroke="#d95da7" stroke-width="1.5938643217086792" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 127.2031 L 0 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.188;y:0.797;w:0.000;h:127.203"/>
                            <path id="Subtract-start_5" transform="translate(0.7968368530273438, 128.0001983642578)" fill="none" stroke="#d95da7" stroke-width="1.5938643217086792" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 2.3315e-15 2.3908 C 5.7677e-8 3.7103 1.0713 4.7815 2.3908 4.7815 C 3.7103 4.7815 4.7815 3.7103 4.7815 2.3908 C 4.7815 1.0713 3.7103 -5.7677e-8 2.3908 0 C 1.0713 5.7677e-8 -5.7677e-8 1.0713 2.3315e-15 2.3908 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.797;y:128.000;w:4.782;h:4.782"/>
                        </g>
                    </g>
                    <g id="text-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:288;w:156;h:60" transform="translate(0, 288)">
                        <g id="tx-ct-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
                <g id="Column-5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:696;y:24;w:156;h:348" transform="translate(696, 24)">
                    <g id="text-5" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:156;h:60">
                        <g id="tx-ct-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="g-5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:84;w:132;h:264" transform="translate(12, 84)">
                        <g id="cu_Subtract_4" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:131.615;w:132.000;h:132.000">
                            <path id="Subtract_12" transform="translate(0, 131.61489868164062)" fill="#ffedeb" d="M 66.0001 132.0001 C 102.4509 132.0001 132.0001 102.4509 132.0001 66.0001 C 132.0001 29.5492 102.4509 0 66.0001 0 C 29.5492 0 0 29.5492 0 66.0001 C 0 102.4509 29.5492 132.0001 66.0001 132.0001 Z M 71.9802 27.9391 C 71.504 28.683 71.9917 30.0314 72.8713 30.0314 L 100.6501 30.0314 C 101.1408 30.0314 101.5386 30.4316 101.5386 30.9252 L 101.5386 61.0692 C 101.5386 61.9541 100.1981 62.4447 99.4585 61.9657 C 98.3505 61.2479 97.0313 60.8315 95.6155 60.8315 C 91.69 60.8315 88.5078 64.0326 88.5078 67.9815 C 88.5078 71.9303 91.69 75.1315 95.6155 75.1315 C 97.0313 75.1315 98.3505 74.715 99.4585 73.9973 C 100.1981 73.5182 101.5386 74.0088 101.5386 74.8937 L 101.5386 104.0294 C 101.5386 104.523 101.1408 104.9232 100.6501 104.9232 L 72.8713 104.9232 C 71.9917 104.9232 71.504 103.5748 71.9802 102.8308 C 72.6938 101.7162 73.1078 100.3891 73.1078 98.9648 C 73.1078 95.016 69.9255 91.8148 66.0001 91.8148 C 62.0746 91.8148 58.8924 95.016 58.8924 98.9648 C 58.8924 100.3891 59.3064 101.7162 60.0199 102.8308 C 60.4961 103.5748 60.0084 104.9232 59.1288 104.9232 L 31.35 104.9232 C 30.8593 104.9232 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5182 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1315 36.3846 75.1315 C 40.3101 75.1315 43.4923 71.9303 43.4923 67.9815 C 43.4923 64.0326 40.3101 60.8315 36.3846 60.8315 C 34.9688 60.8315 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9542 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3064 26.8244 58.8924 25.4974 58.8924 24.0731 C 58.8924 20.1242 62.0746 16.9231 66.0001 16.9231 C 69.9255 16.9231 73.1078 20.1242 73.1078 24.0731 C 73.1078 25.4974 72.6938 26.8244 71.9802 27.9391 Z"/>
                            <path id="Subtract_13" transform="translate(0, 131.61489868164062)" fill="none" stroke="#b960e2" stroke-width="1.6923030614852905" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66.0001 132.0001 C 102.4509 132.0001 132.0001 102.4509 132.0001 66.0001 C 132.0001 29.5492 102.4509 0 66.0001 0 C 29.5492 0 0 29.5492 0 66.0001 C 0 102.4509 29.5492 132.0001 66.0001 132.0001 Z M 71.9802 27.9391 C 71.504 28.683 71.9917 30.0314 72.8713 30.0314 L 100.6501 30.0314 C 101.1408 30.0314 101.5386 30.4316 101.5386 30.9252 L 101.5386 61.0692 C 101.5386 61.9541 100.1981 62.4447 99.4585 61.9657 C 98.3505 61.2479 97.0313 60.8315 95.6155 60.8315 C 91.69 60.8315 88.5078 64.0326 88.5078 67.9815 C 88.5078 71.9303 91.69 75.1315 95.6155 75.1315 C 97.0313 75.1315 98.3505 74.715 99.4585 73.9973 C 100.1981 73.5182 101.5386 74.0088 101.5386 74.8937 L 101.5386 104.0294 C 101.5386 104.523 101.1408 104.9232 100.6501 104.9232 L 72.8713 104.9232 C 71.9917 104.9232 71.504 103.5748 71.9802 102.8308 C 72.6938 101.7162 73.1078 100.3891 73.1078 98.9648 C 73.1078 95.016 69.9255 91.8148 66.0001 91.8148 C 62.0746 91.8148 58.8924 95.016 58.8924 98.9648 C 58.8924 100.3891 59.3064 101.7162 60.0199 102.8308 C 60.4961 103.5748 60.0084 104.9232 59.1288 104.9232 L 31.35 104.9232 C 30.8593 104.9232 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5182 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1315 36.3846 75.1315 C 40.3101 75.1315 43.4923 71.9303 43.4923 67.9815 C 43.4923 64.0326 40.3101 60.8315 36.3846 60.8315 C 34.9688 60.8315 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9542 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3064 26.8244 58.8924 25.4974 58.8924 24.0731 C 58.8924 20.1242 62.0746 16.9231 66.0001 16.9231 C 69.9255 16.9231 73.1078 20.1242 73.1078 24.0731 C 73.1078 25.4974 72.6938 26.8244 71.9802 27.9391 Z"/>
                        </g>
                        <g id="ic-cc-5" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:180;w:36;h:36" transform="translate(48, 180)">
                            <rect id="Rectangle_7_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_9" transform="translate(6.75, 3.75)" fill="none" stroke="#b960e2" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator_4" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:-1;w:6;h:134" transform="translate(63, -1)">
                            <path id="Subtract_14" transform="translate(3.1878600120544434, 3.188687801361084)" fill="none" stroke="#b960e2" stroke-width="1.594042181968689" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 0 129.8647" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.188;y:3.189;w:0;h:129.865"/>
                            <g id="cu_Subtract-start_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.797;y:0.797;w:4.781;h:4.781">
                                <path id="Subtract-start_6" transform="translate(0.7968043088912964, 0.7968140840530396)" fill="#ffffff" d="M 0 2.3906 C 8.0791e-17 3.7101 1.0712 4.7813 2.3906 4.7813 C 3.7101 4.7813 4.7813 3.7101 4.7813 2.3906 C 4.7813 1.0712 3.7101 -8.0791e-17 2.3906 0 C 1.0712 8.0791e-17 -8.0791e-17 1.0712 0 2.3906 Z"/>
                                <path id="Subtract-start_7" transform="translate(0.7968043088912964, 0.7968140840530396)" fill="none" stroke="#b960e2" stroke-width="1.594042181968689" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 2.3906 C 8.0791e-17 3.7101 1.0712 4.7813 2.3906 4.7813 C 3.7101 4.7813 4.7813 3.7101 4.7813 2.3906 C 4.7813 1.0712 3.7101 -8.0791e-17 2.3906 0 C 1.0712 8.0791e-17 -8.0791e-17 1.0712 0 2.3906 Z"/>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="Column-6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:864;y:24;w:156;h:348" transform="translate(864, 24)">
                    <g id="g-6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-position="x:12;y:0;w:132;h:264" transform="translate(12, 0)">
                        <g id="cu_Subtract_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:132.000;h:132.000">
                            <path id="Subtract_15" fill="#f3f0ff" d="M 66 132.0001 C 102.4508 132.0001 132.0001 102.4508 132.0001 66 C 132.0001 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132.0001 66 132.0001 Z M 71.9802 27.9391 C 71.5039 28.683 71.9917 30.0314 72.8713 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.9541 100.198 62.4447 99.4585 61.9656 C 98.3505 61.2479 97.0313 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0313 75.1314 98.3505 74.715 99.4585 73.9972 C 100.198 73.5182 101.5385 74.0088 101.5385 74.8937 L 101.5385 104.0294 C 101.5385 104.523 101.1407 104.9231 100.65 104.9231 L 72.8713 104.9231 C 71.9917 104.9231 71.5039 103.5747 71.9802 102.8308 C 72.6937 101.7161 73.1077 100.3891 73.1077 98.9648 C 73.1077 95.0159 69.9255 91.8148 66 91.8148 C 62.0745 91.8148 58.8923 95.0159 58.8923 98.9648 C 58.8923 100.3891 59.3063 101.7161 60.0199 102.8308 C 60.4961 103.5747 60.0084 104.9231 59.1288 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9688 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9541 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                            <path id="Subtract_16" fill="none" stroke="#7e62ec" stroke-width="1.6923054456710815" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 66 132.0001 C 102.4508 132.0001 132.0001 102.4508 132.0001 66 C 132.0001 29.5492 102.4508 0 66 0 C 29.5492 0 0 29.5492 0 66 C 0 102.4508 29.5492 132.0001 66 132.0001 Z M 71.9802 27.9391 C 71.5039 28.683 71.9917 30.0314 72.8713 30.0314 L 100.65 30.0314 C 101.1407 30.0314 101.5385 30.4316 101.5385 30.9252 L 101.5385 61.0692 C 101.5385 61.9541 100.198 62.4447 99.4585 61.9656 C 98.3505 61.2479 97.0313 60.8314 95.6154 60.8314 C 91.6899 60.8314 88.5077 64.0326 88.5077 67.9814 C 88.5077 71.9303 91.6899 75.1314 95.6154 75.1314 C 97.0313 75.1314 98.3505 74.715 99.4585 73.9972 C 100.198 73.5182 101.5385 74.0088 101.5385 74.8937 L 101.5385 104.0294 C 101.5385 104.523 101.1407 104.9231 100.65 104.9231 L 72.8713 104.9231 C 71.9917 104.9231 71.5039 103.5747 71.9802 102.8308 C 72.6937 101.7161 73.1077 100.3891 73.1077 98.9648 C 73.1077 95.0159 69.9255 91.8148 66 91.8148 C 62.0745 91.8148 58.8923 95.0159 58.8923 98.9648 C 58.8923 100.3891 59.3063 101.7161 60.0199 102.8308 C 60.4961 103.5747 60.0084 104.9231 59.1288 104.9231 L 31.35 104.9231 C 30.8593 104.9231 30.4616 104.523 30.4616 104.0294 L 30.4616 74.8936 C 30.4616 74.0088 31.802 73.5181 32.5415 73.9972 C 33.6496 74.715 34.9688 75.1314 36.3846 75.1314 C 40.3101 75.1314 43.4923 71.9303 43.4923 67.9814 C 43.4923 64.0326 40.3101 60.8314 36.3846 60.8314 C 34.9688 60.8314 33.6496 61.2479 32.5415 61.9657 C 31.802 62.4448 30.4616 61.9541 30.4616 61.0693 L 30.4616 30.9252 C 30.4616 30.4316 30.8593 30.0314 31.35 30.0314 L 59.1288 30.0314 C 60.0084 30.0314 60.4961 28.683 60.0199 27.9391 C 59.3063 26.8244 58.8923 25.4974 58.8923 24.0731 C 58.8923 20.1242 62.0745 16.9231 66 16.9231 C 69.9255 16.9231 73.1077 20.1242 73.1077 24.0731 C 73.1077 25.4974 72.6937 26.8244 71.9802 27.9391 Z"/>
                        </g>
                        <g id="ic-cc-6" data-entity-classes="NotInside" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:48;w:36;h:36" transform="translate(48, 48)">
                            <rect id="Rectangle_7_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36" fill="#33de7b1a" width="36" height="36" rx="0" ry="0"/>
                            <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:36;h:36">
                                <path id="icon_11" transform="translate(6.75, 3.75)" fill="none" stroke="#7e62ec" stroke-width="1.5" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0001 0 C 7.2589 4.2508 14.5001 8.5304 21.75 12.7957 C 17.5787 15.1076 10.8446 18.7976 5.9736 21.4622 C 2.5179 23.3525 0 24.7267 0 24.7267 L 0.0001 0 Z M 21.75 12.7957 C 17.2872 17.4927 12.9065 22.2654 8.485 27 C 7.6497 25.1538 6.8439 23.2928 5.9736 21.4622" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6.750;y:3.750;w:21.750;h:27"/>
                            </g>
                        </g>
                        <g id="Subtract-with-terminator_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:63;y:131;w:6;h:136" transform="translate(63, 131)">
                            <path id="Subtract_17" transform="translate(3.1876275539398193, 1.5000838041305542)" fill="none" stroke="#7e62ec" stroke-width="1.5938643217086792" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 128.7677 L 0 0" data-constraints="horizontal:CENTER;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3.188;y:1.500;w:0.000;h:128.768"/>
                            <path id="Subtract-start_8" transform="translate(0.7968368530273438, 130.2682647705078)" fill="none" stroke="#7e62ec" stroke-width="1.5938643217086792" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 2.3315e-15 2.3908 C 5.7677e-8 3.7103 1.0713 4.7815 2.3908 4.7815 C 3.7103 4.7815 4.7815 3.7103 4.7815 2.3908 C 4.7815 1.0713 3.7103 -5.7677e-8 2.3908 0 C 1.0713 5.7677e-8 -5.7677e-8 1.0713 2.3315e-15 2.3908 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.797;y:130.268;w:4.782;h:4.782"/>
                        </g>
                    </g>
                    <g id="text-6" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:0;y:288;w:156;h:60" transform="translate(0, 288)">
                        <g id="tx-ct-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <g id="tx-ct-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-6" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>