<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="diverge3-v5--family--4">        <g id="lines">            <g id="g-4">                <g id="ar-with-terminator">                    <path id="line" marker-end="url(#arrow)" transform="matrix(-0.8660253286361694, -0.5000001192092896, 0.5000001192092896, -0.8660253286361694, 309.6461182236665, 288.0000227219003)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-3">                <g id="ar-with-terminator_1">                    <path id="line_1" marker-end="url(#arrow)" transform="matrix(-0.866025447845459, 0.49999991059303284, -0.49999991059303284, -0.866025447845459, 309.6461597532022, 360.0000270598948)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-2">                <g id="ar-with-terminator_2">                    <path id="line_2" marker-end="url(#arrow)" transform="matrix(0.8660253882408142, 0.5000000596046448, -0.5000000596046448, 0.8660253882408142, 434.3537717759623, 359.9999642936141)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-1">                <g id="ar-with-terminator_3">                    <path id="line_3" marker-end="url(#arrow)" transform="matrix(0.8660253882408142, -0.5, 0.5, 0.8660253882408142, 434.35377502441406, 288.000018721521)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g></g>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 96, 216)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 96, 360)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="translate(492, 360)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 492, 216)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 120)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-start" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 287.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-17, -2.7755575615628914e-17, 1, 264, 257)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-17, 2.7755575615628914e-17, 1, 264, 367)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-17, -2.7755575615628914e-17, 1, 456, 367)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-17, 2.7755575615628914e-17, 1, 456, 257)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 276, 312)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 444, 312)" width="24" height="24" rx="0" ry="0"></rect></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>