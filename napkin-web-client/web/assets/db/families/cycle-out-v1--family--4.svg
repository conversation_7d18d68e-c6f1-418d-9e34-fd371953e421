<svg xmlns="http://www.w3.org/2000/svg" width="720" height="584">
    <g id="cycle-out-v1--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L720 0 L720 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:720;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:720;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:720;h:536">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:163;y:11;w:465;h:345" transform="translate(163, 11)">
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:171;y:0;w:294;h:294" transform="translate(171, 0)">
                        <g id="cu_Union" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:1;y:1;w:291.882;h:291.765">
                            <path id="Union" transform="translate(1, 1)" fill="#e8f9ff" d="M0 0 C38.0703 0 75.0648 12.6306 105.186 35.9124 C134.964 58.9285 156.361 91.0684 166.115 127.393 L166.153 127.383 C171.695 147.215 183.574 164.69 199.978 177.139 C216.115 189.385 235.76 196.106 256 196.32 L256 179.236 C256 178.181 257.423 177.845 257.894 178.789 L291.776 234.553 C291.917 234.834 291.917 235.166 291.776 235.447 L257.894 291.211 C257.423 292.155 256 291.819 256 290.764 L256 273.997 C218.803 273.78 182.667 261.51 153.022 239.012 C123.111 216.313 101.449 184.449 91.3447 148.285 C86.0831 128.081 74.2039 110.136 57.685 97.3685 C41.1662 84.6005 20.8781 77.6738 0 77.6738 L0 0 Z"/>
                            <path id="Union_1" transform="translate(1, 1)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 0 C 38.0703 0 75.0648 12.6306 105.1863 35.9124 C 134.9641 58.9285 156.3611 91.0684 166.1152 127.3935 L 166.1533 127.3828 C 171.6947 147.2155 183.5739 164.69 199.9775 177.1385 C 216.1154 189.3855 235.76 196.106 256 196.3204 L 256 179.2361 C 256 178.1808 257.4225 177.845 257.8944 178.7888 L 291.7764 234.5528 C 291.9172 234.8343 291.9172 235.1657 291.7764 235.4472 L 257.8944 291.2112 C 257.4225 292.155 256 291.8192 256 290.7639 L 256 273.9966 C 218.8028 273.7805 182.6675 261.5103 153.0218 239.0125 C 123.1106 216.313 101.4493 184.449 91.3447 148.2849 C 86.0831 128.0808 74.2039 110.1364 57.685 97.3685 C 41.1662 84.6005 20.8781 77.6738 0 77.6738 L 0 0 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:1;w:172;h:172.000" transform="translate(0, 1)">
                        <g id="cu_Ellipse_462" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:172;h:172.000">
                            <path id="Ellipse_462" fill="#e7fbf2" d="M0 172 C3.988e-6 126.383 18.1214 82.6339 50.3776 50.3776 C82.6339 18.1214 126.383 -5.4398e-7 172 0 L172 77.6738 C146.983 77.6738 122.991 87.6117 105.301 105.301 C87.6117 122.991 77.6738 146.983 77.6738 172 L0 172 Z"/>
                            <path id="Ellipse_462_1" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 172 C 0 126.3828 18.1214 82.6339 50.3776 50.3776 C 82.6339 18.1214 126.3828 -5.4398e-7 172 1.2247e-14 L 172 77.6738 C 146.9831 77.6738 122.9909 87.6117 105.3013 105.3013 C 87.6117 122.9909 77.6738 146.9831 77.6738 172 L 0 172 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:173;w:172;h:172.000" transform="translate(0, 173)">
                        <g id="cu_Ellipse_463" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:172;h:172.000">
                            <path id="Ellipse_463" fill="#f2fae1" d="M172 172 C149.413 172 127.046 167.551 106.178 158.907 C85.3104 150.263 66.3493 137.594 50.3776 121.622 C34.4059 105.651 21.7365 86.6896 13.0927 65.8216 C4.4489 44.9536 -1.9747e-6 22.5874 1.819e-12 0 L77.6738 6.7905e-6 C77.6738 12.3871 80.1136 24.6529 84.854 36.0971 C89.5943 47.5413 96.5423 57.9397 105.301 66.6987 C114.06 75.4577 124.459 82.4057 135.903 87.1461 C147.347 91.8864 159.613 94.3262 172 94.3262 L172 172 Z"/>
                            <path id="Ellipse_463_1" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 172 172 C 149.4126 172 127.0464 167.5511 106.1784 158.9073 C 85.3104 150.2635 66.3493 137.5941 50.3776 121.6224 C 34.4059 105.6507 21.7365 86.6896 13.0927 65.8216 C 4.4489 44.9536 -0 22.5874 6.5734e-13 0 L 77.6738 0 C 77.6738 12.3871 80.1136 24.6529 84.854 36.0971 C 89.5943 47.5413 96.5423 57.9397 105.3013 66.6987 C 114.0603 75.4577 124.4587 82.4057 135.9029 87.1461 C 147.3471 91.8864 159.6129 94.3262 172 94.3262 L 172 172 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:172.002;y:149.285;w:157.102;h:195.715" transform="translate(172.001953125, 149.28515625)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:157.102;h:195.715">
                            <path id="Subtract" fill="#fefbdb" d="M91.3428 -3.8624e-5 C94.9806 13.9485 95.3015 28.4555 92.4186 42.5794 C89.5356 56.7033 83.4539 69.9785 74.6405 81.3855 C65.8271 92.7925 54.5165 102.028 41.5775 108.382 C28.6384 114.736 14.4151 118.04 0 118.041 L0.0016 195.715 C26.287 195.714 52.2226 189.689 75.8164 178.102 C99.4103 166.516 120.035 149.675 136.105 128.875 C144.506 118.003 151.545 106.2 157.102 93.7312 C155.728 92.751 154.367 91.7496 153.02 90.727 C123.109 68.0275 101.447 36.1641 91.3428 -3.8624e-5 Z"/>
                            <path id="Subtract_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 91.3428 0 C 94.9806 13.9485 95.3015 28.4555 92.4186 42.5794 C 89.5356 56.7033 83.4539 69.9785 74.6405 81.3855 C 65.8271 92.7925 54.5165 102.0278 41.5775 108.3821 C 28.6384 114.7363 14.4151 118.0405 0 118.0408 L 0.0016 195.7146 C 26.287 195.7141 52.2226 189.6891 75.8164 178.1023 C 99.4103 166.5156 120.0346 149.6755 136.1055 128.8753 C 144.5058 118.0029 151.5452 106.1997 157.1021 93.7312 C 155.7283 92.7511 154.3674 91.7496 153.0199 90.727 C 123.1087 68.0276 101.4473 36.1641 91.3428 0 Z"/>
                        </g>
                    </g>
                </g>
                <g id="row" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:36;y:456;w:672;h:60" transform="translate(36, 456)">
                    <g id="text-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:144;h:60">
                        <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0)" width="24" height="24" rx="0" ry="0"/>
                        <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:-60;w:48;h:48" transform="translate(48, -60)">
                            <path id="rect" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                            <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d1bd08" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 60, -84)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:168;y:0;w:144;h:60" transform="translate(168, 0)">
                        <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#93c332" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 60, -84)" width="24" height="24" rx="0" ry="0"/>
                        <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:-60;w:48;h:48" transform="translate(48, -60)">
                            <path id="rect_1" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                            <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#93c332" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                        </g>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="text-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:336;y:0;w:144;h:60" transform="translate(336, 0)">
                        <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#3cc583" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 60, -84)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0)" width="24" height="24" rx="0" ry="0"/>
                        <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:-60;w:48;h:48" transform="translate(48, -60)">
                            <path id="rect_2" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                            <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#3cc583" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                        </g>
                    </g>
                    <g id="text-4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:504;y:0;w:144;h:60" transform="translate(504, 0)">
                        <g id="tx-lt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                            <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 60, -84)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0)" width="24" height="24" rx="0" ry="0"/>
                        <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:-60;w:48;h:48" transform="translate(48, -60)">
                            <path id="rect_3" fill="#ff00001a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48"/>
                            <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#17aee1" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                        </g>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:442;y:132;w:48;h:48" transform="translate(442, 132)">
                    <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(10, 6)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:215;y:67;w:48;h:48" transform="translate(215, 67)">
                    <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(10, 6)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:215;y:252;w:48;h:48" transform="translate(215, 252)">
                    <rect id="Rectangle_7_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(10, 6)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:406;y:252;w:48;h:48" transform="translate(406, 252)">
                    <rect id="Rectangle_7_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(10, 6)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>