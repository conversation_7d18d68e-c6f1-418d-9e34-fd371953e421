<svg xmlns="http://www.w3.org/2000/svg" width="649" height="516">
    <g id="stairs-tape-v1--family--4" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L649 0 L649 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:649;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:649;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:649;h:468">
                <g id="lines" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:MIN" data-position="x:48;y:60;w:192;h:312" transform="translate(48, 60)">
                    <g id="g-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 144;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:0;w:192;h:60">
                        <path id="top-fill" transform="translate(384, -12)" fill="#feecf7" d="M 132 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 60 Z M 108 14 C 108 6.27 114.27 0 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66 L 108 66 L 108 14 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:-12;w:132;h:66"/>
                        <path id="sides-fill" transform="translate(384, 54)" fill="#feecf7" d="M 0 0 L 108 0 L 108 6 L 0 6 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:54;w:108;h:6"/>
                        <path id="bottom-fill" transform="translate(384, 60)" fill="#feecf7" d="M 0 12 L 74 12 C 79.52 12 84 16.48 84 22 L 84 62 C 84 67.52 88.48 72 94 72 C 97.86 72 101.36 70.43 103.9 67.9 C 106.43 65.36 108 61.86 108 58 L 108 0 L 0 0 L 0 12 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:60;w:108;h:72"/>
                        <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:144;y:0;w:192;h:60" transform="translate(144, 0)">
                            <g id="tx-rt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <g id="tx-rt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                            <path id="bt-cc-remove-4" transform="translate(192, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24"/>
                            <g id="ic-cc-4" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:60;w:48;h:48" fill="#33de7b1a" transform="translate(360, 60)">
                                <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_1" transform="translate(9, 6)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:6;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <path id="top-stroke" transform="translate(384, -12)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 60 L 108 60 Z M 108 14 L 108 66 M 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:-12;w:132;h:66"/>
                        <path id="sides-stroke" transform="translate(384, 54)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 6 M 108 0 L 108 6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:54;w:108;h:6"/>
                        <path id="bottom-stroke" transform="translate(384, 60)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 108 0 L 108 58 C 108 61.86 106.43 65.36 103.9 67.9 C 101.36 70.43 97.86 72 94 72 C 88.48 72 84 67.52 84 62 L 84 22 C 84 16.48 79.52 12 74 12 L 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:384;y:60;w:108;h:72"/>
                        <g id="number" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:CENTER" data-position="x:414;y:6;w:48;h:48" transform="translate(414, 6)">
                            <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#ff00001a">
                                <text id="4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:6;w:36;h:36" fill="#d95da7" transform="translate(6, 6)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                            </g>
                        </g>
                    </g>
                    <g id="g-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 96;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:84;w:192;h:60" transform="translate(0, 84)">
                        <path id="top-fill_1" transform="translate(336, -12)" fill="#ffedeb" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 C 108 6.27 114.27 0 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66 L 108 66 L 108 14 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:-12;w:142;h:66"/>
                        <path id="sides-fill_1" transform="translate(336, 54)" fill="#ffedeb" d="M 0 0 L 108 0 L 108 6 L 0 6 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:54;w:108;h:6"/>
                        <path id="bottom-fill_1" transform="translate(336, 60)" fill="#ffedeb" d="M 0 12 L 74 12 C 79.52 12 84 16.48 84 22 L 84 62 C 84 67.52 88.48 72 94 72 C 97.86 72 101.36 70.43 103.9 67.9 C 106.43 65.36 108 61.86 108 58 L 108 0 L 0 0 L 0 12 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:60;w:108;h:72"/>
                        <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:96;y:0;w:192;h:60" transform="translate(96, 0)">
                            <g id="tx-rt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <g id="tx-rt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                            <path id="bt-cc-remove-3" transform="translate(192, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24"/>
                            <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:60;w:48;h:48" fill="#33de7b1a" transform="translate(360, 60)">
                                <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_3" transform="translate(9, 6)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:6;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <path id="top-stroke_1" transform="translate(336, -12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 L 108 66 M 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:-12;w:142;h:66"/>
                        <path id="sides-stroke_1" transform="translate(336, 54)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 6 M 108 0 L 108 6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:54;w:108;h:6"/>
                        <path id="bottom-stroke_1" transform="translate(336, 60)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 108 0 L 108 58 C 108 61.86 106.43 65.36 103.9 67.9 C 101.36 70.43 97.86 72 94 72 C 88.48 72 84 67.52 84 62 L 84 22 C 84 16.48 79.52 12 74 12 L 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336;y:60;w:108;h:72"/>
                        <g id="number_1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:CENTER" data-position="x:366;y:6;w:48;h:48" transform="translate(366, 6)">
                            <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#ff00001a">
                                <text id="3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:6;w:36;h:36" fill="#df5e59" transform="translate(6, 6)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                            </g>
                        </g>
                    </g>
                    <g id="g-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 48;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:168;w:192;h:60" transform="translate(0, 168)">
                        <path id="top-fill_2" transform="translate(288, -12)" fill="#fef2e6" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 C 108 6.27 114.27 0 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66 L 108 66 L 108 14 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:-12;w:142;h:66"/>
                        <path id="sides-fill_2" transform="translate(288, 54)" fill="#fef2e6" d="M 0 0 L 108 0 L 108 6 L 0 6 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:54;w:108;h:6"/>
                        <path id="bottom-fill_2" transform="translate(288, 60)" fill="#fef2e6" d="M 0 12 L 74 12 C 79.52 12 84 16.48 84 22 L 84 62 C 84 67.52 88.48 72 94 72 C 97.86 72 101.36 70.43 103.9 67.9 C 106.43 65.36 108 61.86 108 58 L 108 0 L 0 0 L 0 12 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:60;w:108;h:72"/>
                        <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:48;y:0;w:192;h:60" transform="translate(48, 0)">
                            <g id="tx-rt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <g id="tx-rt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                            <path id="bt-cc-remove-2" transform="translate(192, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24"/>
                            <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:60;w:48;h:48" fill="#33de7b1a" transform="translate(360, 60)">
                                <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_5" transform="translate(9, 6)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:6;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <path id="top-stroke_2" transform="translate(288, -12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 L 108 66 M 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 66" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:-12;w:142;h:66"/>
                        <path id="sides-stroke_2" transform="translate(288, 54)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 6 M 108 0 L 108 6" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:54;w:108;h:6"/>
                        <path id="bottom-stroke_2" transform="translate(288, 60)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 108 0 L 108 58 C 108 61.86 106.43 65.36 103.9 67.9 C 101.36 70.43 97.86 72 94 72 C 88.48 72 84 67.52 84 62 L 84 22 C 84 16.48 79.52 12 74 12 L 0 12 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:288;y:60;w:108;h:72"/>
                        <g id="number_2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:CENTER" data-position="x:318;y:6;w:48;h:48" transform="translate(318, 6)">
                            <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#ff00001a">
                                <text id="2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:6;w:36;h:36" fill="#db8333" transform="translate(6, 6)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                            </g>
                        </g>
                    </g>
                    <g id="g-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:48;primary:CENTER;counter:MIN" data-position="x:0;y:252;w:192;h:60" transform="translate(0, 252)">
                        <path id="top-fill_3" transform="translate(240, -12)" fill="#fefbdb" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 C 108 6.27 114.27 0 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 72 L 108 72 L 108 14 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:-12;w:142;h:72"/>
                        <path id="sides-fill_3" transform="translate(240, 60)" fill="#fefbdb" d="M 0 0 L 108 0 L 108 36 L 0 36 L 0 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:60;w:108;h:36"/>
                        <path id="bottom-fill_3" transform="translate(240, 96)" fill="#fefbdb" d="M 0 0 L 108 0 L 108 12 L 0 12 L 0 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:96;w:108;h:12"/>
                        <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:0;y:0;w:192;h:60">
                            <g id="tx-rt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                                <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <g id="tx-rt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                            </g>
                            <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 84, -24)" width="24" height="24" rx="0" ry="0"/>
                            <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 36, 60)" width="24" height="24" rx="0" ry="0"/>
                            <path id="bt-cc-remove-1" transform="translate(192, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:0;w:24;h:24"/>
                            <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:360;y:60;w:48;h:48" fill="#33de7b1a" transform="translate(360, 60)">
                                <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_7" transform="translate(9, 6)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:6;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                        <path id="top-stroke_3" transform="translate(240, -12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 142 60 L 108 60 L 108 14 C 108 6.27 114.27 0 122 0 C 127.52 0 132 4.48 132 10 L 132 50 C 132 55.52 136.48 60 142 60 Z M 108 14 L 108 72 M 122 0 L 14 0 C 6.27 0 0 6.27 0 14 L 0 72" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:-12;w:142;h:72"/>
                        <path id="sides-stroke_3" transform="translate(240, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 36 M 108 0 L 108 36" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:60;w:108;h:36"/>
                        <path id="bottom-stroke_3" transform="translate(240, 96)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 L 108 12 L 108 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:240;y:96;w:108;h:12"/>
                        <g id="number_3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:CENTER" data-position="x:270;y:6;w:48;h:48" transform="translate(270, 6)">
                            <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#ff00001a">
                                <text id="1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:6;w:36;h:36" fill="#d1bd08" transform="translate(6, 6)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>