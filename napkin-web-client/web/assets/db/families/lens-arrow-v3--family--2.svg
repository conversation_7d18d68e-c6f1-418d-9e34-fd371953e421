<svg xmlns="http://www.w3.org/2000/svg" width="802" height="360">
    <g id="lens-arrow-v3--family--2" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L802 0 L802 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:802;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:802;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:802;h:312">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:225;y:35.227;w:579;h:242" transform="translate(225, 35.2265625)">
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3;y:121;w:360;h:120" transform="translate(3, 121)">
                        <g id="cu_Subtract" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:360;h:120">
                            <path id="Subtract" fill="#fef2e6" d="M 360 47.9919 L 360 48.0081 C 345.9276 48.0081 332.4561 53.7116 322.6617 63.8162 L 311.6397 75.0076 C 283.7635 103.7669 245.4214 120 205.3692 120 L 0.0001 120 L 31.0002 72.0001 L 0 24.0001 L 205.3692 24 C 219.4416 24 232.9131 18.2965 242.7075 8.1919 L 250.7754 0 L 253.7295 2.9995 C 281.6057 31.7588 319.9478 47.9919 360 47.9919 Z"/>
                            <path id="Subtract_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 360 47.9919 L 360 48.0081 C 345.9276 48.0081 332.4561 53.7116 322.6617 63.8162 L 311.6397 75.0076 C 283.7635 103.7669 245.4214 120 205.3692 120 L 0.0001 120 L 31.0002 72.0001 L 0 24.0001 L 205.3692 24 C 219.4416 24 232.9131 18.2965 242.7075 8.1919 L 250.7754 0 L 253.7295 2.9995 C 281.6057 31.7588 319.9478 47.9919 360 47.9919 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:3;y:1;w:574;h:167.992" transform="translate(3, 1)">
                        <g id="cu_Subtract_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:574;h:167.992">
                            <path id="Subtract_2" fill="#fefbdb" d="M 542.8556 167.9919 L 360 167.9919 C 319.9478 167.9919 281.6057 151.7588 253.7295 122.9995 L 242.7075 111.8081 C 232.9131 101.7035 219.4416 96 205.3692 96 L 0 95.9999 L 31.0002 47.9999 L 0.0001 0 L 205.3692 0 C 245.4214 0 283.7635 16.2331 311.6397 44.9924 L 322.6617 56.1838 C 332.4561 66.2884 345.9276 71.9919 360 71.9919 L 542.8556 71.9919 L 574 120.0401 L 542.8556 167.9919 Z"/>
                            <path id="Subtract_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 542.8556 167.9919 L 360 167.9919 C 319.9478 167.9919 281.6057 151.7588 253.7295 122.9995 L 242.7075 111.8081 C 232.9131 101.7035 219.4416 96 205.3692 96 L 0 95.9999 L 31.0002 47.9999 L 0.0001 0 L 205.3692 0 C 245.4214 0 283.7635 16.2331 311.6397 44.9924 L 322.6617 56.1838 C 332.4561 66.2884 345.9276 71.9919 360 71.9919 L 542.8556 71.9919 L 574 120.0401 L 542.8556 167.9919 Z"/>
                        </g>
                    </g>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:48;w:60;h:48" fill="#ff00001a" transform="translate(12, 48)">
                    <text id="1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:6;y:0.227;w:48;h:48" fill="#d1bd08" transform="translate(6, 0.2265625)" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:48.227;w:144;h:48" transform="translate(72, 48.2265625)">
                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:216;w:60;h:48" fill="#ff00001a" transform="translate(12, 216)">
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:48" fill="#db8333" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:72;y:216.227;w:144;h:48" transform="translate(72, 216.2265625)">
                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                    </g>
                </g>
                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 12)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:144.227;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 144.2265625)" width="24" height="24" rx="0" ry="0"/>
                <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:276;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 30, 276)" width="24" height="24" rx="0" ry="0"/>
                <g id="tx-rc-end" data-entity-classes="DescTitle NotInside" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:564;y:144.227;w:144;h:24" fill="#ff00001a" transform="translate(564, 144.2265625)">
                    <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                </g>
                <g id="tx-lc-1" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:72;w:144;h:24" fill="#ff00001a" transform="translate(276, 72)">
                    <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="tx-lc-2" data-entity-classes="DescTitle" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:276;y:216.227;w:144;h:24" fill="#ff00001a" transform="translate(276, 216.2265625)">
                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                </g>
                <g id="ic-cc-end" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:720;y:132.227;w:48;h:48" fill="#33de7b1a" transform="translate(720, 132.2265625)">
                    <g id="icon" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12.800;y:12.801;w:22.800;h:22.800" transform="translate(12.79998779296875, 12.80078125)">
                        <path id="icon_1" transform="translate(0, -3.19921875)" fill="none" stroke="#484848" stroke-width="1.600000023841858" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 7.7428 4.5342 15.4667 9.0991 23.2 13.6487 C 18.7507 16.1148 11.5676 20.0508 6.3718 22.893 C 2.6858 24.9093 0 26.3751 0 26.3751 L 0.0002 0 Z M 23.2 13.6487 C 18.4396 18.6589 13.7669 23.7498 9.0506 28.8 C 8.1597 26.8307 7.3001 24.8457 6.3718 22.893" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-3.199;w:23.200;h:28.800"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>