<svg xmlns="http://www.w3.org/2000/svg" width="1008" height="672">    <g id="bullseye-v5--family--6">        <g id="lines">            <g id="g-6">                <g id="cu" >                    <path id="Vector" transform="translate(420, 84)" fill="#edf4ff" d="M576 288 C576 447.058 447.058 576 288 576 C128.942 576 0 447.058 0 288 C0 128.942 128.942 0 288 0 C447.058 0 576 128.942 576 288 Z M528 288 C528 420.548 420.548 528 288 528 C155.452 528 48 420.548 48 288 C48 155.452 155.452 48 288 48 C420.548 48 528 155.452 528 288 Z"></path>
                    <path id="Vector_1" transform="translate(420, 84)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 576 288 C 576 447.058 447.058 576 288 576 C 128.942 576 0 447.058 0 288 C 0 128.942 128.942 0 288 0 C 447.058 0 576 128.942 576 288 Z M 528 288 C 528 420.5483 420.5483 528 288 528 C 155.4517 528 48 420.5483 48 288 C 48 155.4517 155.4517 48 288 48 C 420.5483 48 528 155.4517 528 288 Z"></path></g>
                <g id="ar-with-terminator">                    <path id="line" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(373, 156)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0 L 96.5 55.5"></path></g>
                <g id="Frame_1" transform="translate(357, 194)"></g></g>
            <g id="g-5">                <g id="cu_1" >                    <path id="Vector_2" transform="translate(468, 132)" fill="#e8f9ff" d="M480 240 C480 372.548 372.548 480 240 480 C107.452 480 0 372.548 0 240 C0 107.452 107.452 0 240 0 C372.548 0 480 107.452 480 240 Z M240 432 C346.039 432 432 346.039 432 240 C432 133.961 346.039 48 240 48 C133.961 48 48 133.961 48 240 C48 346.039 133.961 432 240 432 Z"></path>
                    <path id="Vector_3" transform="translate(468, 132)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 480 240 C 480 372.5483 372.5483 480 240 480 C 107.4517 480 0 372.5483 0 240 C 0 107.4517 107.4517 0 240 0 C 372.5483 0 480 107.4517 480 240 Z M 240 432 C 346.0387 432 432 346.0387 432 240 C 432 133.9613 346.0387 48 240 48 C 133.9613 48 48 133.9613 48 240 C 48 346.0387 133.9613 432 240 432 Z"></path></g>
                <g id="ar-with-terminator_1">                    <path id="line_1" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(372, 240)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0 L 114 40.5"></path></g></g>
            <g id="g-4">                <g id="cu_2" >                    <path id="Vector_4" transform="translate(516, 180)" fill="#f2fae1" d="M384 192 C384 298.039 298.039 384 192 384 C85.9613 384 0 298.039 0 192 C0 85.9613 85.9613 0 192 0 C298.039 0 384 85.9613 384 192 Z M192 336 C271.529 336 336 271.529 336 192 C336 112.471 271.529 48 192 48 C112.471 48 48 112.471 48 192 C48 271.529 112.471 336 192 336 Z"></path>
                    <path id="Vector_5" transform="translate(516, 180)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 384 192 C 384 298.0387 298.0387 384 192 384 C 85.9613 384 0 298.0387 0 192 C 0 85.9613 85.9613 0 192 0 C 298.0387 0 384 85.9613 384 192 Z M 192 336 C 271.529 336 336 271.529 336 192 C 336 112.471 271.529 48 192 48 C 112.471 48 48 112.471 48 192 C 48 271.529 112.471 336 192 336 Z"></path></g>
                <g id="ar-with-terminator_2">                    <path id="line_2" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(372, 324)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0 L 146 19.5"></path></g></g>
            <g id="g-3">                <g id="cu_3" >                    <path id="Vector_6" transform="translate(564, 228)" fill="#fefbdb" d="M288 144 C288 223.529 223.529 288 144 288 C64.471 288 0 223.529 0 144 C0 64.471 64.471 0 144 0 C223.529 0 288 64.471 288 144 Z M144 240 C197.019 240 240 197.019 240 144 C240 90.9807 197.019 48 144 48 C90.9807 48 48 90.9807 48 144 C48 197.019 90.9807 240 144 240 Z"></path>
                    <path id="Vector_7" transform="translate(564, 228)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 288 144 C 288 223.529 223.529 288 144 288 C 64.471 288 0 223.529 0 144 C 0 64.471 64.471 0 144 0 C 223.529 0 288 64.471 288 144 Z M 144 240 C 197.0193 240 240 197.0193 240 144 C 240 90.9807 197.0193 48 144 48 C 90.9807 48 48 90.9807 48 144 C 48 197.0193 90.9807 240 144 240 Z"></path></g>
                <g id="ar-with-terminator_3">                    <path id="line_3" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="matrix(1, 0, 0, -1, 372, 407.99997329711914)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0 L 193 20"></path></g></g>
            <g id="g-2">                <g id="cu_4" >                    <path id="Vector_8" transform="translate(612, 276)" fill="#fef2e6" d="M192 96 C192 149.019 149.019 192 96 192 C42.9807 192 0 149.019 0 96 C0 42.9807 42.9807 0 96 0 C149.019 0 192 42.9807 192 96 Z M96 144 C122.51 144 144 122.51 144 96 C144 69.4903 122.51 48 96 48 C69.4903 48 48 69.4903 48 96 C48 122.51 69.4903 144 96 144 Z"></path>
                    <path id="Vector_9" transform="translate(612, 276)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 192 96 C 192 149.0193 149.0193 192 96 192 C 42.9807 192 0 149.0193 0 96 C 0 42.9807 42.9807 0 96 0 C 149.0193 0 192 42.9807 192 96 Z M 96 144 C 122.5097 144 144 122.5097 144 96 C 144 69.4903 122.5097 48 96 48 C 69.4903 48 48 69.4903 48 96 C 48 122.5097 69.4903 144 96 144 Z"></path></g>
                <g id="ar-with-terminator_4">                    <path id="line_4" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(372, 405.00000286102295)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 87 L 246 0"></path></g></g>
            <g id="g-1">                <g id="cu_5" >                    <path id="Vector_10" transform="translate(660, 324)" fill="#ffedeb" d="M96 48 C96 74.5097 74.5097 96 48 96 C21.4903 96 0 74.5097 0 48 C0 21.4903 21.4903 0 48 0 C74.5097 0 96 21.4903 96 48 Z"></path>
                    <path id="Vector_11" transform="translate(660, 324)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 96 48 C 96 74.5097 74.5097 96 48 96 C 21.4903 96 0 74.5097 0 48 C 0 21.4903 21.4903 0 48 0 C 74.5097 0 96 21.4903 96 48 Z"></path></g>
                <g id="ar-with-terminator_5">                    <path id="line_5" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(372, 397.00000381469727)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 179 L 295.5 0"></path></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 204, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-1" transform="translate(12, 552)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-2" transform="translate(12, 468)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 12, 384)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 12, 300)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-5" transform="translate(12, 216)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 12, 132)" fill="#ff00001a" d="M0 0 L288 0 L288 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 552)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 468)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 384)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 300)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 216)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 312, 132)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-0" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 684, 348)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 564)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 480)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 396)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 312)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 228)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 144)" width="24" height="24" rx="0" ry="0"></rect></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>