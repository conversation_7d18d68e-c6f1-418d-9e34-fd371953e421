<svg xmlns="http://www.w3.org/2000/svg" width="600" height="468">    <g id="pillars-lock-v3--family--5">        <g id="lines">            <g id="g-0">                <ellipse id="cr-big" stroke="#bcbcbc" fill="#f0f0f0" stroke-width="2" stroke-linejoin="miter"  transform="translate(240.00017070770264, 203.99996948242188)" cx="60" cy="60" rx="60" ry="60"></ellipse>
                <g id="cu" >                    <path id="vector" transform="translate(228.00018310546875, 72)" fill="#f0f0f0" d="M72.0034 0 C111.77 0 144.007 32.237 144.007 72.0034 L144.007 111.507 C136.809 105.064 128.742 99.5724 120.003 95.2297 L120.003 72.0034 C120.003 45.4938 98.5131 24.0034 72.0034 24.0034 C45.4938 24.0034 24.0034 45.4938 24.0034 72.0034 L24.0034 95.2297 C15.2648 99.5724 7.1978 105.064 0 111.507 L0 72.0034 C0 32.237 32.237 0 72.0034 0 Z"></path>
                    <path id="vector_1" transform="translate(228.00018310546875, 72)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 72.0034 0 C 111.7698 0 144.0068 32.237 144.0068 72.0034 L 144.0068 111.5066 C 136.8091 105.0639 128.7421 99.5724 120.0034 95.2297 L 120.0034 72.0034 C 120.0034 45.4938 98.5131 24.0034 72.0034 24.0034 C 45.4938 24.0034 24.0034 45.4938 24.0034 72.0034 L 24.0034 95.2297 C 15.2648 99.5724 7.1978 105.0639 0 111.5066 L 0 72.0034 C 0 32.237 32.237 0 72.0034 0 Z"></path></g></g>
            <g id="g-5">                <g id="cu_3" >                    <path id="cu_1" transform="translate(197.25691318312238, 155.99606323242188)" fill="#fef2e6" d="M102.743 47.9764 C76.0777 47.9875 53.4792 65.3936 45.6793 89.4631 L0 74.621 C14.0608 31.3106 54.7455 0 102.743 0 C102.743 0 102.743 0 102.743 0 L102.743 47.9764 Z"></path>
                    <path id="cu_2" transform="translate(197.25691318312238, 155.99606323242188)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 102.7432 47.9764 C 76.0777 47.9875 53.4792 65.3936 45.6793 89.4631 L 0 74.621 C 14.0608 31.3106 54.7455 0 102.7431 0 L 102.7431 0 L 102.7432 47.9764 Z"></path></g></g>
            <g id="g-4">                <g id="cu_6" >                    <path id="cu_4" transform="translate(191.99981689453125, 230.5979916425834)" fill="#e8f9ff" d="M44.5189 120.781 C17.5381 101.147 0 69.3204 0 33.3981 C0 21.7419 1.8466 10.517 5.2631 0 L50.9445 14.8358 C49.0498 20.6739 48.0258 26.9044 48.0258 33.3741 C48.0258 53.3296 57.7679 71.0098 72.7554 81.918 L44.5189 120.781 Z"></path>
                    <path id="cu_5" transform="translate(191.99981689453125, 230.5979916425834)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 44.5189 120.7811 C 17.5381 101.1466 0 69.3204 0 33.3981 C 0 21.7419 1.8466 10.517 5.2631 0 L 50.9445 14.8358 C 49.0498 20.6739 48.0258 26.9044 48.0258 33.3741 C 48.0258 53.3296 57.7679 71.0098 72.7554 81.918 L 44.5189 120.7811 Z"></path></g></g>
            <g id="g-3">                <g id="cu_9" >                    <path id="cu_7" transform="translate(236.51864032108278, 312.5161895751953)" fill="#ffedeb" d="M0 38.8628 L28.2365 0 C38.1366 7.2054 50.3253 11.4561 63.5069 11.4561 C76.6862 11.4561 88.873 7.2069 98.7721 0.0037 L126.987 38.8454 C109.163 51.8233 87.2164 59.4799 63.4813 59.4799 C39.7568 59.4799 17.8191 51.8302 0 38.8628 Z"></path>
                    <path id="cu_8" transform="translate(236.51864032108278, 312.5161895751953)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 0 38.8628 L 28.2365 0 C 38.1366 7.2054 50.3253 11.4561 63.5069 11.4561 C 76.6862 11.4561 88.873 7.2069 98.7721 0.0037 L 126.9865 38.8454 C 109.1632 51.8233 87.2164 59.4799 63.4813 59.4799 C 39.7568 59.4799 17.8191 51.8302 0 38.8628 Z"></path></g></g>
            <g id="g-2">                <g id="cu_12" >                    <path id="cu_10" transform="translate(335.265869140625, 230.6161809401529)" fill="#e7fbf2" d="M28.2184 120.76 C55.1973 101.126 72.7339 69.3008 72.7339 33.3799 C72.7339 21.7304 70.8895 10.5118 67.4767 0 L21.8441 14.827 C23.7369 20.6625 24.7598 26.8898 24.7598 33.356 C24.7598 53.3249 15.0047 71.0154 0 81.9219 L28.2184 120.76 Z"></path>
                    <path id="cu_11" transform="translate(335.265869140625, 230.6161809401529)" fill="none" stroke="#3cc583" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 28.2184 120.7605 C 55.1973 101.1258 72.7339 69.3008 72.7339 33.3799 C 72.7339 21.7304 70.8895 10.5118 67.4767 0 L 21.8441 14.827 C 23.7369 20.6625 24.7598 26.8898 24.7598 33.356 C 24.7598 53.3249 15.0047 71.0154 0 81.9219 L 28.2184 120.7605 Z"></path></g></g>
            <g id="g-1">                <g id="cu_15" >                    <path id="cu_13" transform="translate(300.02569580078125, 155.99606627470507)" fill="#fefbdb" d="M102.713 74.6065 L57.0794 89.4321 C49.2677 65.3709 26.6652 47.9764 0 47.9764 L3.486e-5 0 C47.981 0.0113 88.6499 31.3114 102.713 74.6065 Z"></path>
                    <path id="cu_14" transform="translate(300.02569580078125, 155.99606627470507)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 102.7125 74.6065 L 57.0794 89.4321 C 49.2677 65.3709 26.6652 47.9764 0 47.9764 L 0 0 C 47.981 0.0113 88.6499 31.3114 102.7125 74.6065 Z"></path></g></g></g>
        <path id="ic-cc-0" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 264, 227.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-17, 2.7755575615628914e-17, 1, 288, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 210, 226)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -2.7755575615628914e-16, 2.7755575615628914e-16, 1, 237, 322)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-16, -2.7755575615628914e-16, 1, 339, 322)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 366, 226)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 192, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="translate(168, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, 1.2246468525851679e-16, -1.2246468525851679e-16, 1, 288, 372)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="translate(408, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 384, 168)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 0, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="tx-rb-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 96)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 0, 264)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 216, 396)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="translate(432, 264)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 396, 96)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path></g></svg>