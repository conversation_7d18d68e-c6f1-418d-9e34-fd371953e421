<svg xmlns="http://www.w3.org/2000/svg" width="768" height="474">
    <g id="bullseye-pins-v2--family--6" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L768 0 L768 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:768;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:768;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:768;h:426">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:180;y:90;w:408;h:324" transform="translate(180, 90)">
                    <g id="g-0a" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84;y:84;w:240;h:240" transform="translate(84, 84)">
                        <path id="Subtract" data-entity-classes="KeepStroke" transform="translate(48, 48)" fill="#f6f6f6" d="M 144 72 C 144 111.7645 111.7645 144 72 144 C 32.2355 144 0 111.7645 0 72 C 0 32.2355 32.2355 0 72 0 C 111.7645 0 144 32.2355 144 72 Z M 72 132 C 105.1371 132 132 105.1371 132 72 C 132 38.8629 105.1371 12 72 12 C 38.8629 12 12 38.8629 12 72 C 12 105.1371 38.8629 132 72 132 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:48;y:48;w:144;h:144"/>
                        <path id="Vector" data-entity-classes="KeepStroke" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 240 120 C 240 186.2742 186.2742 240 120 240 C 53.7258 240 0 186.2742 0 120 C 0 53.7258 53.7258 0 120 0 C 186.2742 0 240 53.7258 240 120 Z M 192 120 C 192 159.7645 159.7645 192 120 192 C 80.2355 192 48 159.7645 48 120 C 48 80.2355 80.2355 48 120 48 C 159.7645 48 192 80.2355 192 120 Z M 120 180 C 153.1371 180 180 153.1371 180 120 C 180 86.8629 153.1371 60 120 60 C 86.8629 60 60 86.8629 60 120 C 60 153.1371 86.8629 180 120 180 Z M 215.8713 124.9961 C 213.2714 175.6926 171.3422 215.9985 119.9991 215.9985 C 68.995 215.9985 27.281 176.2231 24.1836 125.9989" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:240"/>
                    </g>
                    <g id="g-0b" data-entity-classes="KeepStroke" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:108.184;y:108.160;w:191.565;h:90.005" transform="translate(108.18359375, 108.16015625)">
                        <path id="Ellipse_520" data-entity-classes="KeepStroke" fill="none" stroke="#000000" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 90.0054 C 1.0249 73.3698 6.2865 57.8805 14.7191 44.603 M 21.7522 34.9174 C 31.9321 22.5882 45.1312 12.844 60.2173 6.8168 M 71.5294 3.0986 C 79.2865 1.0764 87.4255 0 95.8159 0 C 103.8538 0 111.661 0.9879 119.1228 2.8488 M 130.498 6.4561 C 145.6522 12.3297 158.944 21.9393 169.2432 34.1548 M 176.3713 43.7602 C 184.9485 56.9594 190.3696 72.3985 191.5651 89.0079" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:191.565;h:90.005"/>
                    </g>
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:294;y:168.160;w:114;h:72" transform="translate(294, 168.16015625)">
                        <g id="cu_Ellipse_524" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:30;y:0;w:84;h:72">
                            <path id="Ellipse_524" transform="translate(30, 0)" fill="#f3f0ff" d="M 48 72 C 67.8823 72 84 55.8823 84 36 C 84 16.1177 67.8823 0 48 0 C 30.162 0 15.3542 12.9737 12.4977 30 L 0 36 L 12.4977 42 C 15.3542 59.0263 30.162 72 48 72 Z"/>
                            <path id="Ellipse_524_1" transform="translate(30, 0)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 48 72 C 67.8823 72 84 55.8823 84 36 C 84 16.1177 67.8823 0 48 0 C 30.162 0 15.3542 12.9737 12.4977 30 L 0 36 L 12.4977 42 C 15.3542 59.0263 30.162 72 48 72 Z"/>
                        </g>
                        <ellipse id="Ellipse_535" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:29;w:12;h:12" stroke="#7e62ec" fill="#f3f0ff" stroke-width="2" stroke-linejoin="miter" transform="translate(0, 29)" cx="6" cy="6" rx="6" ry="6"/>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:275.375;y:69.434;w:100.587;h:83.395" transform="translate(275.375, 69.43359375)">
                        <g id="cu_Ellipse_527" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:25.752;y:0;w:74.835;h:72.005">
                            <path id="Ellipse_527" transform="translate(25.751953125, 0)" fill="#faf0ff" d="M 17.6725 6.8779 C 33.7576 -4.8086 56.2709 -1.2428 67.9574 14.8423 C 79.6439 30.9274 76.0782 53.4407 59.9931 65.1272 C 45.5619 75.6121 25.9563 73.8199 13.6376 61.7244 L 0 64.2163 L 6.5842 52.0162 C -1.1127 36.5626 3.2413 17.3628 17.6725 6.8779 Z"/>
                            <path id="Ellipse_527_1" transform="translate(25.751953125, 0)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 17.6725 6.8779 C 33.7576 -4.8086 56.2709 -1.2428 67.9574 14.8423 C 79.6439 30.9274 76.0782 53.4407 59.9931 65.1272 C 45.5619 75.6121 25.9563 73.8199 13.6376 61.7244 L 0 64.2163 L 6.5842 52.0162 C -1.1127 36.5626 3.2413 17.3628 17.6725 6.8779 Z"/>
                        </g>
                        <g id="cu_Ellipse_537" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:71.395;w:12.001;h:12.001">
                            <path id="Ellipse_537" transform="translate(0, 71.39453125)" fill="#faf0ff" d="M 1.1463 9.5271 C -0.8014 6.8463 -0.2071 3.0941 2.4737 1.1463 C 5.1546 -0.8014 8.9068 -0.2071 10.8545 2.4737 C 12.8023 5.1546 12.208 8.9068 9.5271 10.8545 C 6.8463 12.8023 3.0941 12.208 1.1463 9.5271 Z"/>
                            <path id="Ellipse_537_1" transform="translate(0, 71.39453125)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 1.1463 9.5271 C -0.8014 6.8463 -0.2071 3.0941 2.4737 1.1463 C 5.1546 -0.8014 8.9068 -0.2071 10.8545 2.4737 C 12.8023 5.1546 12.208 8.9068 9.5271 10.8545 C 6.8463 12.8023 3.0941 12.208 1.1463 9.5271 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:219.885;y:8.352;w:72.019;h:109.851" transform="translate(219.884765625, 8.3515625)">
                        <g id="cu_Ellipse_533" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72.019;h:81.660">
                            <path id="Ellipse_533" fill="#feecf7" d="M 70.2477 47.1343 C 76.3917 28.2252 66.0435 7.9156 47.1343 1.7717 C 28.2252 -4.3723 7.9156 5.976 1.7717 24.8851 C -3.7406 41.85 4.0223 59.9422 19.3326 67.9203 L 21.1769 81.6604 L 30.7453 71.6285 C 47.8209 74.1732 64.7355 64.0993 70.2477 47.1343 Z"/>
                            <path id="Ellipse_533_1" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 70.2477 47.1343 C 76.3917 28.2252 66.0435 7.9156 47.1343 1.7717 C 28.2252 -4.3723 7.9156 5.976 1.7717 24.8851 C -3.7406 41.85 4.0223 59.9422 19.3326 67.9203 L 21.1769 81.6604 L 30.7453 71.6285 C 47.8209 74.1732 64.7355 64.0993 70.2477 47.1343 Z"/>
                        </g>
                        <g id="cu_Ellipse_536" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:7.303;y:97.848;w:12.003;h:12.003">
                            <path id="Ellipse_536" transform="translate(7.302734375, 97.84765625)" fill="#feecf7" d="M 7.8557 0.2953 C 11.0072 1.3193 12.732 4.7042 11.708 7.8557 C 10.684 11.0072 7.299 12.732 4.1475 11.708 C 0.996 10.684 -0.7287 7.299 0.2953 4.1475 C 1.3193 0.996 4.7042 -0.7287 7.8557 0.2953 Z"/>
                            <path id="Ellipse_536_1" transform="translate(7.302734375, 97.84765625)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 7.8557 0.2953 C 11.0072 1.3193 12.732 4.7042 11.708 7.8557 C 10.684 11.0072 7.299 12.732 4.1475 11.708 C 0.996 10.684 -0.7287 7.299 0.2953 4.1475 C 1.3193 0.996 4.7042 -0.7287 7.8557 0.2953 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:116.082;y:8.379;w:72.019;h:110.132" transform="translate(116.08203125, 8.37890625)">
                        <g id="cu_Ellipse_528" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:72.019;h:81.660">
                            <path id="Ellipse_528" fill="#ffedeb" d="M 1.7717 47.1343 C -4.3723 28.2252 5.976 7.9156 24.8851 1.7717 C 43.7943 -4.3723 64.1038 5.976 70.2477 24.8851 C 75.76 41.85 67.9971 59.9422 52.6869 67.9203 L 50.8425 81.6604 L 41.2742 71.6285 C 24.1985 74.1732 7.2839 64.0993 1.7717 47.1343 Z"/>
                            <path id="Ellipse_528_1" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 1.7717 47.1343 C -4.3723 28.2252 5.976 7.9156 24.8851 1.7717 C 43.7943 -4.3723 64.1038 5.976 70.2477 24.8851 C 75.76 41.85 67.9971 59.9422 52.6869 67.9203 L 50.8425 81.6604 L 41.2742 71.6285 C 24.1985 74.1732 7.2839 64.0993 1.7717 47.1343 Z"/>
                        </g>
                        <g id="cu_Ellipse_522" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:51.777;y:98.129;w:12.003;h:12.003">
                            <path id="Ellipse_522" transform="translate(51.77734375, 98.12890625)" fill="#ffedeb" d="M 7.8557 11.708 C 4.7042 12.732 1.3193 11.0072 0.2953 7.8557 C -0.7287 4.7042 0.996 1.3193 4.1475 0.2953 C 7.299 -0.7287 10.684 0.996 11.708 4.1475 C 12.732 7.299 11.0072 10.684 7.8557 11.708 Z"/>
                            <path id="Ellipse_522_1" transform="translate(51.77734375, 98.12890625)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 7.8557 11.708 C 4.7042 12.732 1.3193 11.0072 0.2953 7.8557 C -0.7287 4.7042 0.996 1.3193 4.1475 0.2953 C 7.299 -0.7287 10.684 0.996 11.708 4.1475 C 12.732 7.299 11.0072 10.684 7.8557 11.708 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:32.094;y:69.422;w:99.948;h:84.216" transform="translate(32.09375, 69.421875)">
                        <g id="cu_Ellipse_532" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:74.835;h:72.005">
                            <path id="Ellipse_532" fill="#fef2e6" d="M 57.1628 6.8779 C 41.0777 -4.8086 18.5644 -1.2428 6.8779 14.8423 C -4.8086 30.9274 -1.2428 53.4407 14.8423 65.1272 C 29.2735 75.6121 48.879 73.8199 61.1978 61.7244 L 74.8354 64.2163 L 68.2512 52.0162 C 75.9481 36.5626 71.5941 17.3628 57.1628 6.8779 Z"/>
                            <path id="Ellipse_532_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 57.1628 6.8779 C 41.0777 -4.8086 18.5644 -1.2428 6.8779 14.8423 C -4.8086 30.9274 -1.2428 53.4407 14.8423 65.1272 C 29.2735 75.6121 48.879 73.8199 61.1978 61.7244 L 74.8354 64.2163 L 68.2512 52.0162 C 75.9481 36.5626 71.5941 17.3628 57.1628 6.8779 Z"/>
                        </g>
                        <g id="cu_Ellipse_531" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:87.947;y:72.215;w:12.001;h:12.001">
                            <path id="Ellipse_531" transform="translate(87.947265625, 72.21484375)" fill="#fef2e6" d="M 1.1463 2.4737 C 3.0941 -0.2071 6.8463 -0.8014 9.5271 1.1463 C 12.208 3.0941 12.8023 6.8463 10.8545 9.5271 C 8.9068 12.208 5.1546 12.8023 2.4737 10.8545 C -0.2071 8.9068 -0.8014 5.1546 1.1463 2.4737 Z"/>
                            <path id="Ellipse_531_1" transform="translate(87.947265625, 72.21484375)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 1.1463 2.4737 C 3.0941 -0.2071 6.8463 -0.8014 9.5271 1.1463 C 12.208 3.0941 12.8023 6.8463 10.8545 9.5271 C 8.9068 12.208 5.1546 12.8023 2.4737 10.8545 C -0.2071 8.9068 -0.8014 5.1546 1.1463 2.4737 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:168.160;w:114;h:72" transform="translate(0, 168.16015625)">
                        <g id="cu_Ellipse_523" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:84;h:72">
                            <path id="Ellipse_523" fill="#fefbdb" d="M 36 72 C 16.1177 72 0 55.8823 0 36 C 0 16.1177 16.1177 0 36 0 C 53.838 0 68.6458 12.9737 71.5023 30 L 84 36 L 71.5023 42 C 68.6458 59.0263 53.838 72 36 72 Z"/>
                            <path id="Ellipse_523_1" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4" d="M 36 72 C 16.1177 72 0 55.8823 0 36 C 0 16.1177 16.1177 0 36 0 C 53.838 0 68.6458 12.9737 71.5023 30 L 84 36 L 71.5023 42 C 68.6458 59.0263 53.838 72 36 72 Z"/>
                        </g>
                        <ellipse id="Ellipse_534" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:102;y:30;w:12;h:12" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="miter" transform="translate(102, 30)" cx="6" cy="6" rx="6" ry="6"/>
                    </g>
                </g>
                <g id="tx-cc-6-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:528;y:270;w:48;h:48" fill="#ff00001a" transform="translate(528, 270)">
                    <text id="6" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#7e62ec" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">6</text>
                </g>
                <g id="tx-cc-5-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:496;y:171;w:48;h:48" fill="#ff00001a" transform="translate(496, 171)">
                    <text id="5" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#b960e2" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">5</text>
                </g>
                <g id="tx-cc-4-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:412;y:111;w:48;h:48" fill="#ff00001a" transform="translate(412, 111)">
                    <text id="4" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d95da7" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">4</text>
                </g>
                <g id="tx-cc-3-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:308;y:111;w:48;h:48" fill="#ff00001a" transform="translate(308, 111)">
                    <text id="3" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#df5e59" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">3</text>
                </g>
                <g id="tx-cc-2-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:225;y:171;w:48;h:48" fill="#ff00001a" transform="translate(225, 171)">
                    <text id="2" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#db8333" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">2</text>
                </g>
                <g id="tx-cc-1-number" data-entity-classes="DescTitle" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:270;w:48;h:48" fill="#ff00001a" transform="translate(192, 270)">
                    <text id="1" data-constraints="horizontal:MAX;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#d1bd08" font-size="30" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER">1</text>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:576;y:150;w:144;h:60" transform="translate(576, 150)">
                    <g id="tx-cc-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0.000;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0.000001430511474609375)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:432;y:24;w:144;h:60" transform="translate(432, 24)">
                    <g id="tx-cc-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0.000;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0.000001430511474609375)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:192;y:24;w:144;h:60" transform="translate(192, 24)">
                    <g id="tx-cc-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24.000;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -23.999998092651367, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:48;y:150;w:144;h:60" transform="translate(48, 150)">
                    <g id="tx-cc-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24.000;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -23.999998092651367, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:24;y:282;w:144;h:60" transform="translate(24, 282)">
                    <g id="tx-cc-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24.000;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -23.999998092651367, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:CENTER" data-position="x:600;y:282;w:144;h:60" transform="translate(600, 282)">
                    <g id="tx-cc-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:24" fill="#ff00001a">
                        <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <g id="tx-cc-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:144;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_11" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                    </g>
                    <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:144;y:0.000;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 144, 0.000001430511474609375)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="ic-cc-0" data-entity-classes="NotInside" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:348;y:258;w:72;h:72" fill="#33de7b1a" transform="translate(348, 258)">
                    <g id="icon" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:12;y:12;w:48;h:48" transform="translate(12, 12)">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:336;w:24;h:24" fill="#1ac6ff33" transform="translate(204, 336)">

                </g>
                <g id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:232;w:24;h:24" fill="#1ac6ff33" transform="translate(204, 232)">

                </g>
                <g id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:150;w:24;h:24" fill="#1ac6ff33" transform="translate(276, 150)">

                </g>
                <g id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:372;y:114;w:24;h:24" fill="#1ac6ff33" transform="translate(372, 114)">

                </g>
                <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:468;y:150;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 468, 150)" width="24" height="24" rx="0" ry="0"/>
                <g id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:536;y:232;w:24;h:24" fill="#1ac6ff33" transform="translate(536, 232)">

                </g>
                <g id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:540;y:336;w:24;h:24" fill="#1ac6ff33" transform="translate(540, 336)">

                </g>
            </g>
        </g>
    </g>
</svg>