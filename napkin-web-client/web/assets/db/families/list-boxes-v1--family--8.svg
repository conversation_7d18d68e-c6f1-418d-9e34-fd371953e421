<svg xmlns="http://www.w3.org/2000/svg" width="960" height="840">
    <g id="list-boxes-v1--family--8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L960 0 L960 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:960;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:960;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:960;h:792">
                <g id="tabs" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:144;primary:MIN;counter:MIN" data-position="x:24;y:84;w:432;h:624" transform="translate(24, 84)">
                    <g id="row-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                        <g id="g-1" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                            <path id="top-fill" transform="translate(0, -48)" fill="#fefbdb" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill" transform="translate(0, -12)" fill="#fefbdb" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill" transform="translate(0, 60)" fill="#fefbdb" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke" transform="translate(0, -48)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke" transform="translate(0, -12)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke" transform="translate(0, 60)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-1" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <rect id="bt-cc-add-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                            <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="Frame_659" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="Frame_660" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="g-2" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:480;y:0;w:432;h:48" transform="translate(480, 0)">
                            <path id="top-fill_1" transform="translate(0, -48)" fill="#fef2e6" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_1" transform="translate(0, -12)" fill="#fef2e6" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_1" transform="translate(0, 60)" fill="#fef2e6" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_1" transform="translate(0, -48)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_1" transform="translate(0, -12)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_1" transform="translate(0, 60)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-2" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="Frame_659_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="Frame_660_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                            <rect id="bt-cc-add-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                    </g>
                    <g id="row-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:192;w:432;h:48" transform="translate(0, 192)">
                        <g id="g-3" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                            <path id="top-fill_2" transform="translate(0, -48)" fill="#ffedeb" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_2" transform="translate(0, -12)" fill="#ffedeb" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_2" transform="translate(0, 60)" fill="#ffedeb" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_2" transform="translate(0, -48)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_2" transform="translate(0, -12)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_2" transform="translate(0, 60)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-3" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_5" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="g-4" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:480;y:0;w:432;h:48" transform="translate(480, 0)">
                            <path id="top-fill_3" transform="translate(0, -48)" fill="#feecf7" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_3" transform="translate(0, -12)" fill="#feecf7" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_3" transform="translate(0, 60)" fill="#feecf7" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_3" transform="translate(0, -48)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_3" transform="translate(0, -12)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_3" transform="translate(0, 60)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-4" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_6" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_7" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                            <rect id="bt-cc-add-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="row-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:384;w:432;h:48" transform="translate(0, 384)">
                        <g id="g-5" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                            <path id="top-fill_4" transform="translate(0, -48)" fill="#faf0ff" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_4" transform="translate(0, -12)" fill="#faf0ff" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_4" transform="translate(0, 60)" fill="#faf0ff" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_4" transform="translate(0, -48)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_4" transform="translate(0, -12)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_4" transform="translate(0, 60)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-5" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_8" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_9" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="g-6" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:480;y:0;w:432;h:48" transform="translate(480, 0)">
                            <path id="top-fill_5" transform="translate(0, -48)" fill="#f3f0ff" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_5" transform="translate(0, -12)" fill="#f3f0ff" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_5" transform="translate(0, 60)" fill="#f3f0ff" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_5" transform="translate(0, -48)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_5" transform="translate(0, -12)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_5" transform="translate(0, 60)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-6" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text_6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_10" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_11" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                            <rect id="bt-cc-add-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <rect id="bt-cc-add-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="row-4" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:0;y:576;w:432;h:48" transform="translate(0, 576)">
                        <g id="g-7" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:0;y:0;w:432;h:48">
                            <path id="top-fill_6" transform="translate(0, -48)" fill="#edf4ff" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_6" transform="translate(0, -12)" fill="#edf4ff" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_6" transform="translate(0, 60)" fill="#edf4ff" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_6" transform="translate(0, -48)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_6" transform="translate(0, -12)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_6" transform="translate(0, 60)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-7" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text_8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_12" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_9" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_13" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="g-8" data-resize="horizontal:FIXED;vertical:FILL" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 12;gap:0;primary:MIN;counter:MIN" data-position="x:480;y:0;w:432;h:48" transform="translate(480, 0)">
                            <path id="top-fill_7" transform="translate(0, -48)" fill="#e8f9ff" d="M 168 36 L 432 36 L 432 32 C 432 20.95 421.05 12 407.55 12 L 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36 Z M 168 24 C 168 19.63 166.83 15.53 164.77 12 C 160.63 4.83 152.88 0 144 0 L 24 0 C 10.75 0 0 10.75 0 24 L 0 36 L 168 36 L 168 24 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:432;h:36"/>
                            <path id="sides-fill_7" transform="translate(0, -12)" fill="#e8f9ff" d="M 432 0 L 168 0 L 168 72 L 432 72 L 432 0 Z M 168 0 L 0 0 L 0 72 L 168 72 L 168 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:432;h:72"/>
                            <path id="bottom-fill_7" transform="translate(0, 60)" fill="#e8f9ff" d="M 168 12 C 168 16.37 166.83 20.47 164.77 24 L 407.55 24 C 421.05 24 432 15.05 432 4 L 432 0 L 168 0 L 168 12 Z M 168 0 L 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:432;h:36"/>
                            <path id="top-stroke_7" transform="translate(0, -48)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 36 L 0 24 C 0 10.75 10.75 0 24 0 L 144 0 C 152.88 0 160.63 4.83 164.77 12 C 166.83 15.53 168 19.63 168 24 L 168 36" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-48;w:168;h:36"/>
                            <path id="sides-stroke_7" transform="translate(0, -12)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 168 72 L 168 0 M 0 0 L 0 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-12;w:168;h:72"/>
                            <path id="bottom-stroke_7" transform="translate(0, 60)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="10" d="M 0 0 L 0 12 C 0 25.25 10.75 36 24 36 L 144 36 C 152.88 36 160.63 31.17 164.77 24 C 166.83 20.47 168 16.37 168 12 L 168 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:60;w:168;h:36"/>
                            <path id="bt-cc-remove-8" transform="translate(-12, 12)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:12;w:24;h:24"/>
                            <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:24;primary:CENTER;counter:CENTER" data-position="x:12;y:0;w:408;h:48" transform="translate(12, 0)">
                                <g id="text_10" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:144;h:48">
                                    <g id="tx-cc-8" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:144;h:48" fill="#ff00001a">
                                        <text id="Label_14" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:144;h:48" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:CENTER"/>
                                    </g>
                                </g>
                                <g id="text_11" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:MIN;counter:MIN" data-position="x:168;y:0;w:240;h:48" transform="translate(168, 0)">
                                    <g id="tx-lc-8-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:240;h:48" fill="#ff00001a">
                                        <text id="Label_15" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:240;h:48" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                    </g>
                                </g>
                            </g>
                            <rect id="bt-cc-add-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        </g>
                        <rect id="bt-cc-add-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-84;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -84)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-9" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:108;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, 108)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>