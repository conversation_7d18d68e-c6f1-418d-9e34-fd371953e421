<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="diverge2-v4--family--7">        <g id="lines">            <g id="g-7">                <ellipse id="cr-small" stroke="#d95da7" fill="#feecf7" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999933666608, 0, 0, 0.9999999933666608, 263.4094295939026, 136.48973998214387)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-6">                <ellipse id="cr-small_1" stroke="#93c332" fill="#f2fae1" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999904793067, 0, 0, 0.9999999904793067, 172.17483838650872, 250.20594474815903)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-5">                <ellipse id="cr-small_2" stroke="#db8333" fill="#fef2e6" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.000000009937665, 9.68847757576441e-9, -9.68847757576441e-9, 1.000000009937665, 204.3394770589348, 392.3504462413348)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-4">                <ellipse id="cr-small_3" stroke="#17aee1" fill="#e8f9ff" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000596046377, -1.370705567792169e-15, 1.370705567792169e-15, 1.0000000596046377, 335.9999994089495, 455.9999890327458)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-3">                <ellipse id="cr-small_4" stroke="#df5e59" fill="#ffedeb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000022964493, 2.7789990753035454e-8, -2.7789990753035454e-8, 1.0000000022964493, 467.29538635696645, 392.809257547367)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-2">                <ellipse id="cr-small_5" stroke="#3cc583" fill="#e7fbf2" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000142256456, 1.3255387898425397e-9, -1.3255387898425397e-9, 1.0000000142256456, 500.1208637160955, 250.77738346362094)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-1">                <ellipse id="cr-small_6" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000048149538, -8.897949044239795e-10, 8.897949044239795e-10, 1.0000000048149538, 409.11865613196665, 136.4896899962289)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-0">                <g id="cu" >                    <path id="Union" transform="matrix(1.0000000596046377, 0, 0, 1.0000000596046377, 262.8839191059956, 222.87614761307333)" fill="#f6f6f6" d="M60.7995 0 L55.2068 18.1651 L61.6144 15.0951 L72.9868 38.8311 C57.6741 47.7316 45.9434 62.1163 40.4844 79.2957 L14.8933 73.3875 L16.5076 66.395 L0 75.8156 L10.7072 91.5192 L12.3055 84.5963 L37.8939 90.5038 C37.3815 93.9695 37.1161 97.5157 37.1161 101.124 C37.1161 115.949 41.5966 129.727 49.2774 141.179 L28.7139 157.477 L24.2563 151.853 L21.3199 170.631 L40.2726 172.061 L35.8594 166.493 L56.4252 150.192 C68.3531 162.995 84.8915 171.446 103.396 172.9 L103.396 199.159 L96.22 199.159 L109.113 213.124 L122.005 199.159 L114.9 199.159 L114.9 172.895 C133.3 171.432 149.751 163.053 161.653 150.357 L182.222 166.776 L177.745 172.385 L196.702 171.022 L193.832 152.233 L189.399 157.786 L168.829 141.365 C176.587 129.877 181.116 116.029 181.116 101.124 C181.116 97.6221 180.866 94.1788 180.383 90.8108 L206.092 84.9698 L207.682 91.9679 L218.444 76.3018 L201.969 66.8236 L203.544 73.7521 L177.841 79.5915 C172.462 62.4041 160.812 47.9881 145.572 39.0221 L157.051 15.2773 L163.512 18.4007 L157.983 0.2162 L140.297 7.1783 L146.694 10.2706 L135.221 34.0023 C127.128 30.8522 118.324 29.1238 109.116 29.1238 C100.042 29.1238 91.3601 30.8024 83.3637 33.866 L71.9887 10.1246 L78.4606 7.0237 L60.7995 0 Z"></path>
                    <path id="Union_1" transform="matrix(1.0000000596046377, 0, 0, 1.0000000596046377, 262.8839191059956, 222.87614761307333)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 60.7995 0 L 55.2068 18.1651 L 61.6144 15.0951 L 72.9868 38.8311 C 57.6741 47.7316 45.9434 62.1163 40.4844 79.2957 L 14.8933 73.3875 L 16.5076 66.3951 L 0 75.8156 L 10.7072 91.5192 L 12.3055 84.5963 L 37.8939 90.5038 C 37.3815 93.9695 37.1161 97.5157 37.1161 101.1238 C 37.1161 115.9487 41.5966 129.7272 49.2774 141.1788 L 28.7139 157.4772 L 24.2563 151.8531 L 21.3199 170.6315 L 40.2727 172.0607 L 35.8594 166.4925 L 56.4252 150.1922 C 68.3532 162.9951 84.8915 171.4456 103.3964 172.9 L 103.3964 199.1588 L 96.22 199.1588 L 109.1125 213.1242 L 122.0051 199.1588 L 114.9001 199.1588 L 114.9001 172.8949 C 133.2997 171.4323 149.7511 163.0526 161.6534 150.3566 L 182.222 166.7764 L 177.7448 172.3849 L 196.7024 171.0218 L 193.8316 152.2333 L 189.3989 157.786 L 168.8292 141.3655 C 176.5867 129.877 181.1161 116.0294 181.1161 101.1238 C 181.1161 97.6221 180.8661 94.1788 180.383 90.8108 L 206.0921 84.9698 L 207.6821 91.9679 L 218.444 76.3018 L 201.9694 66.8236 L 203.5435 73.7521 L 177.8412 79.5915 C 172.4616 62.4041 160.8124 47.9881 145.572 39.0221 L 157.0507 15.2773 L 163.5118 18.4007 L 157.9825 0.2162 L 140.297 7.1783 L 146.6938 10.2706 L 135.2214 34.0023 C 127.1279 30.8522 118.3238 29.1238 109.1161 29.1238 C 100.0421 29.1238 91.3601 30.8024 83.3637 33.866 L 71.9887 10.1246 L 78.4607 7.0237 L 60.7995 0 Z"></path></g></g></g>
        <path id="ic-cc-start" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 287.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-7" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 275.409423828125, 148.48974609375)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-6" transform="translate(184.30569458007812, 262.2059326171875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 216.3394775390625, 404.3504333496094)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(0.9999999403953552, -2.958222971160071e-25, 2.958222971160071e-25, 0.9999999403953552, 348.0001220703125, 467.9976806640625)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-3" transform="matrix(0.9999999403953552, -1.6653345369377348e-16, 1.6653345369377348e-16, 0.9999999403953552, 479.29541015625, 404.809326171875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, 2.7755575615628914e-17, -2.7755575615628914e-17, 1, 511.8251953125, 262.77734375)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(0.9999999403953552, -1.1102230246251565e-16, 1.1102230246251565e-16, 0.9999999403953552, 421.11865234375, 148.7440185546875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(1, -1.1102230246251565e-16, 1.1102230246251565e-16, 1, 269.259765625, 122.61370849609375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="translate(155.38165283203125, 264.7601318359375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 195.42572021484375, 442.4404296875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -2.958222971160071e-25, 2.958222971160071e-25, 0.9999999403953552, 360.0023193359375, 521.9976806640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -1.6653345369377348e-16, 1.6653345369377348e-16, 0.9999999403953552, 524.120849609375, 443.010498046875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, 2.7755575615628914e-17, -2.7755575615628914e-17, 1, 564.7808837890625, 265.47021484375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -1.1102230246251565e-16, 1.1102230246251565e-16, 0.9999999403953552, 451.3963623046875, 122.9296875)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rb-7" transform="translate(96, 96)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 6, 240)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rt-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 42, 432)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 540)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lt-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 546, 432)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="translate(582, 240)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 492, 96)" fill="#ff00001a" d="M0 0 L144 0 L144 72 L0 72 L0 0 Z"></path>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 24)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-add-8" fill="#1ac6ff33" transform="translate(360, 144)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 228, 207.64739990234375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, 8.326672684688674e-17, -8.326672684688674e-17, 1, 196.17486572265625, 349.9999694824219)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="matrix(1, 1.6653345369377348e-16, -1.6653345369377348e-16, 1, 286.881103515625, 463.253662109375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 432.59039306640625, 463.508056640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="translate(523, 350)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 491.660400390625, 207.64739990234375)" width="24" height="24" rx="0" ry="0"></rect></g></svg>