<svg xmlns="http://www.w3.org/2000/svg" width="984" height="624">    <g id="cycle-oval-v1--family--2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:2;y:11569.193;w:984;h:624">        <g id="lines"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:271;y:72;w:441.072;h:536.423" transform="translate(271, 72)">            <g id="g-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:297.103;h:496.141">                <g id="cu_vector"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:297.103;h:496.141" >                    <path id="vector" fill="#fef2e6" d="M297.103 102 L253.026 57.9229 L195.103 0 L195.103 56.9917 C85.284 69.602 0 162.88 0 276.077 C0 392.949 90.9114 488.588 205.875 496.133 L144.164 434.423 L197.456 381.13 C149.157 370.568 113 327.545 113 276.077 C113 225.448 147.987 182.992 195.103 171.566 L195.103 204 L230.139 168.964 L297.103 102 Z"></path>
                    <path id="vector_1" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 297.103 102 L 195.103 0 L 195.103 56.9917 C 85.284 69.602 0 162.8797 0 276.0769 C 0 392.9491 90.9114 488.5878 205.8746 496.1334 L 144.1641 434.4229 L 197.4565 381.1304 C 149.1574 370.568 113 327.545 113 276.0769 C 113 225.4484 147.9875 182.9917 195.103 171.5662 L 195.103 204 L 297.103 102 Z M 205.882 496.1407 L 205.8746 496.1334"></path></g></g>
            <g id="g-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:144.164;y:57.923;w:296.908;h:478.500" transform="translate(144.1640625, 57.9228515625)">                <g id="cu_vector_1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:296.908;h:478.500" >                    <path id="vector_2" fill="#fefbdb" d="M108.862 0 L152.939 44.0772 L85.975 111.041 C140.867 115.899 183.908 162 183.908 218.154 C183.908 268.713 149.017 311.122 102 322.618 L102 274.5 L53.2924 323.208 L0 376.5 L61.7106 438.211 L61.7179 438.218 L102 478.5 L102 437.217 C211.725 424.519 296.908 331.285 296.908 218.154 C296.908 107.442 215.189 15.7682 108.862 0 Z"></path>
                    <path id="vector_3" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 108.8618 0 L 152.939 44.0772 L 85.975 111.0411 C 140.8668 115.8987 183.9082 162.0001 183.9082 218.1543 C 183.9082 268.713 149.0171 311.1225 102 322.6175 L 102 274.5 L 0 376.5 L 102 478.5 L 102 437.2171 C 211.7251 424.5189 296.9082 331.2846 296.9082 218.1543 C 296.9082 107.4416 215.1887 15.7682 108.8618 0 Z"></path></g></g></g>
        <g id="tx-cb-title"  data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:84;y:0;w:816;h:48" transform="translate(84, 0)">            <path id="rect" fill="#ff00001a" d="M0 0 L816 0 L816 48 L0 48 L0 0 Z"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:816;h:48"></path>
            <text id="Label"  data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:358;y:16;w:87.500;h:16" fill="#484848" transform="translate(358, 16)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:CENTER;vertical:CENTER"></text></g>
        <rect id="ic-cc-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:306;y:330;w:48;h:48" fill="#33de7b1a" transform="translate(306, 330)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="ic-cc-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:631;y:330;w:48;h:48" fill="#33de7b1a" transform="translate(631, 330)" width="48" height="48" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:568;y:161;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 568, 161)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:403;y:495;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 403, 495)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3"  data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:544;y:161;w:24;h:24" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 544, 161)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MIN" data-position="x:756;y:323.807;w:192;h:60" transform="translate(756, 323.806640625)">            <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_1"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
            <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_2"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:LEFT;vertical:TOP"></text></g></g>
        <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:CENTER;counter:MAX" data-position="x:36;y:323.807;w:192;h:60" transform="translate(36, 323.806640625)">            <g id="tx-rt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">                <text id="Label_3"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:RIGHT;vertical:TOP"></text></g>
            <g id="tx-rt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH"  data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">                <text id="Label_4"  data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT"  data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" data-align="horizontal:RIGHT;vertical:TOP"></text></g></g></g></svg>