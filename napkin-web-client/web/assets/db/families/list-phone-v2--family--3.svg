<svg xmlns="http://www.w3.org/2000/svg" width="972" height="398">
    <g id="list-phone-v2--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L972 0 L972 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:972;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:972;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 12 144 12;gap:0;primary:MAX;counter:CENTER" data-position="x:0;y:0;w:972;h:350">
                <g id="Frame_666" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:396;primary:CENTER;counter:MAX" data-position="x:12;y:0;w:948;h:132" transform="translate(12, 0)">
                    <g id="list-right-icons-buttons-right" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:36 84 36 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:0;w:276;h:132">
                        <g id="container" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:48;primary:CENTER;counter:MAX" data-position="x:0;y:36;w:168;h:60" transform="translate(0, 36)">
                            <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:168;h:60">
                                <ellipse id="vector" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-24;w:72;h:72" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="round" transform="translate(192, -24)" cx="36" cy="36" rx="36" ry="36"/>
                                <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:-12;w:48;h:48" fill="#33de7b1a" transform="translate(204, -12)">
                                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                        <path id="icon_1" transform="translate(10, 6)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                                    </g>
                                </g>
                                <g id="tx-rt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                    <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                </g>
                                <g id="tx-rt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                                </g>
                                <rect id="bt-cc-remove-1" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 252, 0)" width="24" height="24" rx="0" ry="0"/>
                                <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -36)" width="24" height="24" rx="0" ry="0"/>
                                <g id="g-1" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:12;w:60;h:54" transform="translate(276, 12)">
                                    <path id="line" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 48 0 C 54.6274 0 60 5.3726 60 12" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:60;h:12"/>
                                    <path id="line-end" transform="translate(5.643432814395055e-7, -6.49969482421875)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 6.4553 0 L 0 6.5444 L 6.5444 12.9997" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.000;y:-6.500;w:6.544;h:13.000"/>
                                    <path id="line_1" transform="matrix(1, -3.42089165883408e-8, 3.42089165883408e-8, 1, 60, 12)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 4.1051e-7 42" data-constraints="horizontal:MAX;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:12;w:0.000;h:42"/>
                                </g>
                            </g>
                        </g>
                    </g>
                    <g id="visual" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:-80;w:396;h:430" transform="translate(276, -80)">
                        <g id="g-common" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:125.315;y:112.316;w:145.370;h:126.706" transform="translate(125.31494140625, 112.31640625)">
                            <path id="Subtract" fill="#e8f9ff" d="M 138.2088 104.184 C 142.7983 94.6545 145.3701 83.9701 145.3701 72.6851 C 145.3701 42.2559 126.6714 16.194 100.1376 5.363 C 100.7386 6.24 101.2089 7.2286 101.5129 8.3088 L 113.2746 50.0955 L 115.6847 47.6855 C 118.1851 45.1837 122.0847 44.1855 125.6847 46.1855 C 129.2847 48.1855 130.518 51.6867 130.6847 53.1873 C 131.018 54.5206 130.8847 58.0873 127.6847 61.6873 C 124.4847 65.2873 120.5183 69.854 119.185 71.6873 L 120.685 76.6873 C 122.185 75.1873 126.67 74.2785 130.6847 76.6873 C 135.6847 79.6873 136.018 83.354 135.6847 84.6873 C 135.3513 86.0206 133.704 89.6614 130.6847 94.1873 C 129.9111 95.3468 129.3038 96.3735 128.7598 97.2932 C 128.2253 98.1969 127.7518 98.9974 127.242 99.7186 L 128.5979 104.5358 C 130.937 106.2219 132.5638 109.1607 133.6849 111.6834 C 133.7329 111.7913 133.7804 111.8983 133.8274 112.0044 C 134.3846 111.1398 134.9239 110.2626 135.4449 109.3734 C 135.6829 107.2191 136.4698 105.303 138.2088 104.184 Z M 24.0546 126.7062 C 9.2854 113.4022 0 94.128 0 72.6851 C 0 55.5139 5.9543 39.7334 15.9108 27.2957 C 15.5136 28.9158 15.5169 30.6576 16.0011 32.3777 L 32.5051 91.0128 C 30.2139 89.2036 27.9945 88.0595 26.6851 87.6853 C 23.8851 86.8853 22.8517 89.0187 22.6851 90.1853 L 24.0546 126.7062 Z M 22.3971 20.204 C 22.5682 20.1458 22.7417 20.0919 22.9177 20.0424 L 88.2157 1.6629 C 83.2118 0.5738 78.0154 0 72.6851 0 C 53.1714 0 35.4538 7.6897 22.3971 20.204 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:145.370;h:126.706"/>
                        </g>
                        <g id="g-common_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60;y:105;w:288;h:142.935" transform="translate(60, 105)">
                            <path id="Subtract_1" transform="translate(58, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 31.5738 142.9353 C 12.3928 128.4211 0 105.4078 0 79.5 C 0 35.5934 35.5934 0 79.5 0 C 123.4066 0 159 35.5934 159 79.5 C 159 90.4846 156.7722 100.9488 152.7438 110.4656" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:58;y:0;w:159;h:142.935"/>
                            <path id="Line_1" transform="matrix(1, 8.742276236262114e-8, -8.742276236262114e-8, 1, 0, 80)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 58 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:80;w:58;h:0"/>
                            <path id="Line_2" transform="translate(217, 80)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 71 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:217;y:80;w:71;h:0"/>
                        </g>
                        <g id="g-common_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:140.939;y:113.332;w:112.973;h:183.333" transform="translate(140.939453125, 113.33203125)">
                            <g id="cu_mobile" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:112.973;h:183.333">
                                <path id="mobile" fill="#f6f6f6" d="M 7.2931 19.0267 C 1.9769 20.5231 -1.1198 26.0458 0.3766 31.3621 L 16.8806 89.9972 C 20.7135 93.0238 24.7476 97.9117 25.0605 104.1703 C 25.2522 108.0033 25.2969 111.1017 25.3355 113.7753 C 25.3745 116.4784 25.4073 118.7472 25.5793 120.9018 L 41.0989 176.0396 C 42.5953 181.3559 48.118 184.4525 53.4343 182.9561 L 77.1755 176.2699 C 74.6054 173.9963 71.3796 170.9236 69.2674 167.9861 C 60.5272 155.8309 57.767 143.4402 50.9909 131.4027 C 49.8971 129.4596 50.0296 126.9327 51.8736 125.6791 C 53.4657 124.5967 55.5567 123.668 58.0605 123.668 C 64.5605 124.168 68.3939 128.6681 70.0605 130.168 L 52.7827 84.4256 C 51.7827 81.4256 49.493 74.9872 56.1286 71.8569 C 62.8173 68.7014 66.198 74.5048 67.3647 77.1715 L 84.4751 122.4372 C 82.5079 117.0491 80.7527 107.2207 87.5604 104.6678 C 95.5604 101.6678 100.2271 113.5011 101.5604 117.6678 C 101.2441 114.0305 100.6278 105.5916 105.2639 103.0293 L 103.5605 103.668 C 103.5605 103.668 98.1564 104.168 95.5604 100.6717 C 90.7343 94.1718 99.0602 83.6717 99.0602 83.6717 L 105.0605 75.6717 C 105.0795 75.6527 105.0405 75.6905 105.0605 75.6717 L 103.5605 70.6717 L 97.6501 49.0799 L 85.8884 7.2931 C 84.392 1.9769 78.8693 -1.1198 73.553 0.3766 L 7.2931 19.0267 Z M 111.6175 98.703 C 110.6342 100.0939 109.5154 101.1907 107.5605 102.1681 L 106.997 102.3794 C 109.3792 101.7819 111.3482 102.3487 112.9734 103.5202 L 111.6175 98.703 Z M 84.4751 122.4372 L 84.5604 122.6678 C 84.532 122.5919 84.5035 122.515 84.4751 122.4372 Z"/>
                                <path id="mobile_1" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 7.2931 19.0267 C 1.9769 20.5231 -1.1198 26.0458 0.3766 31.3621 L 16.8806 89.9972 C 20.7135 93.0238 24.7476 97.9117 25.0605 104.1703 C 25.2522 108.0033 25.2969 111.1017 25.3355 113.7753 C 25.3745 116.4784 25.4073 118.7472 25.5793 120.9018 L 41.0989 176.0396 C 42.5953 181.3559 48.118 184.4525 53.4343 182.9561 L 77.1755 176.2699 C 74.6054 173.9963 71.3796 170.9236 69.2674 167.9861 C 60.5272 155.8309 57.767 143.4402 50.9909 131.4027 C 49.8971 129.4596 50.0296 126.9327 51.8736 125.6791 C 53.4657 124.5967 55.5567 123.668 58.0605 123.668 C 64.5605 124.168 68.3939 128.6681 70.0605 130.168 L 52.7827 84.4256 C 51.7827 81.4256 49.493 74.9872 56.1286 71.8569 C 62.8173 68.7014 66.198 74.5048 67.3647 77.1715 L 84.4751 122.4372 C 82.5079 117.0491 80.7527 107.2207 87.5604 104.6678 C 95.5604 101.6678 100.2271 113.5011 101.5604 117.6678 C 101.2441 114.0305 100.6278 105.5916 105.2639 103.0293 L 103.5605 103.668 C 103.5605 103.668 98.1564 104.168 95.5604 100.6717 C 90.7343 94.1718 99.0602 83.6717 99.0602 83.6717 L 105.0605 75.6717 C 105.0795 75.6527 105.0405 75.6905 105.0605 75.6717 L 103.5605 70.6717 L 97.6501 49.0799 L 85.8884 7.2931 C 84.392 1.9769 78.8693 -1.1198 73.553 0.3766 L 7.2931 19.0267 Z M 111.6175 98.703 C 110.6342 100.0939 109.5154 101.1907 107.5605 102.1681 L 106.997 102.3794 C 109.3792 101.7819 111.3482 102.3487 112.9734 103.5202 L 111.6175 98.703 Z M 84.4751 122.4372 L 84.5604 122.6678 C 84.532 122.5919 84.5035 122.515 84.4751 122.4372 Z"/>
                            </g>
                            <path id="Vector" transform="translate(9.4716796875, 11.078125)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 9.6672 29.5363 C 9.3621 28.4748 9.9753 27.3668 11.0369 27.0617 L 22.052 23.8953 C 23.1136 23.5902 24.2216 24.2034 24.5267 25.265 L 27.693 36.2801 C 27.9982 37.3417 27.385 38.4497 26.3234 38.7548 L 15.3082 41.9211 C 14.2467 42.2263 13.1387 41.6131 12.8335 40.5515 L 9.6672 29.5363 Z M 30.1669 23.3091 C 29.8618 22.2475 30.475 21.1396 31.5365 20.8344 L 42.5517 17.6681 C 43.6133 17.3629 44.7212 17.9761 45.0264 19.0377 L 48.1927 30.0529 C 48.4979 31.1145 47.8847 32.2224 46.8231 32.5276 L 35.8079 35.6939 C 34.7464 35.9991 33.6384 35.3859 33.3332 34.3243 L 30.1669 23.3091 Z M 50.1671 17.3092 C 49.8619 16.2476 50.4751 15.1397 51.5367 14.8345 L 62.5519 11.6682 C 63.6134 11.363 64.7214 11.9762 65.0266 13.0378 L 68.1929 24.053 C 68.498 25.1146 67.8848 26.2225 66.8232 26.5277 L 55.8081 29.694 C 54.7465 29.9992 53.6386 29.386 53.3334 28.3244 L 50.1671 17.3092 Z M 16.6672 51.5363 C 16.3621 50.4748 16.9753 49.3668 18.0369 49.0617 L 29.052 45.8953 C 30.1136 45.5902 31.2216 46.2034 31.5267 47.265 L 34.693 58.2801 C 34.9982 59.3417 34.385 60.4497 33.3234 60.7548 L 22.3082 63.9211 C 21.2467 64.2263 20.1387 63.6131 19.8335 62.5515 L 16.6672 51.5363 Z M 37.1669 45.3091 C 36.8618 44.2475 37.475 43.1396 38.5365 42.8344 L 49.5517 39.6681 C 50.6133 39.3629 51.7212 39.9761 52.0264 41.0377 L 55.1927 52.0529 C 55.4979 53.1145 54.8847 54.2224 53.8231 54.5276 L 42.8079 57.6939 C 41.7464 57.9991 40.6384 57.3858 40.3332 56.3243 L 37.1669 45.3091 Z M 57.1671 39.3092 C 56.8619 38.2476 57.4751 37.1397 58.5367 36.8345 L 69.5519 33.6682 C 70.6134 33.363 71.7214 33.9762 72.0266 35.0378 L 75.1929 46.053 C 75.498 47.1146 74.8848 48.2225 73.8232 48.5277 L 62.8081 51.694 C 61.7465 51.9992 60.6386 51.386 60.3334 50.3244 L 57.1671 39.3092 Z M 22.6672 73.5363 C 22.3621 72.4748 22.9753 71.3668 24.0369 71.0617 L 35.052 67.8953 C 36.1136 67.5902 37.2216 68.2034 37.5267 69.265 L 40.693 80.2801 C 40.9982 81.3417 40.385 82.4497 39.3234 82.7548 L 28.3082 85.9211 C 27.2467 86.2263 26.1387 85.6131 25.8335 84.5515 L 22.6672 73.5363 Z M 63.1671 61.3092 C 62.8619 60.2476 63.4751 59.1397 64.5367 58.8345 L 75.5519 55.6682 C 76.6134 55.363 77.7214 55.9762 78.0266 57.0378 L 81.1929 68.053 C 81.498 69.1146 80.8848 70.2225 79.8232 70.5277 L 68.8081 73.694 C 67.7465 73.9992 66.6386 73.386 66.3334 72.3244 L 63.1671 61.3092 Z M 89.351 73.0898 L 69.7928 4.3593 C 68.8858 1.1722 65.5669 -0.6763 62.3797 0.2306 L 4.3593 16.7412 C 1.1722 17.6481 -0.6763 20.9671 0.2306 24.1543 L 38.0011 156.8848 C 38.908 160.072 42.227 161.9205 45.4141 161.0135 L 59.923 156.8848" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9.472;y:11.078;w:89.351;h:161.244"/>
                        </g>
                        <g id="g-common_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:133;y:301.211;w:225.500;h:128.789" transform="translate(133, 301.2109375)">
                            <g id="cu_blouse" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:225.500;h:128.789">
                                <path id="blouse" fill="#edf4ff" d="M 143.5 128.7891 L 106.7737 37.7748 C 106.0872 36.1647 106.9192 34.3112 108.5786 33.7542 L 110.5 33.1092 L 106.5969 22.7101 C 105.9983 21.1151 106.8464 19.3424 108.4638 18.8076 L 164.8848 0.1522 C 166.3639 -0.3368 167.9711 0.3876 168.5844 1.8196 L 173 12.1302 L 174.3794 11.6672 C 175.8601 11.1702 177.4744 11.8924 178.0907 13.3276 L 225.5 128.7891 L 143.5 128.7891 Z M 0 128.7891 L 3.3515 35.131 C 3.4347 33.5372 4.7513 32.2875 6.3474 32.2875 L 12 32.2875 L 13.6942 17.04 C 13.8669 15.4856 15.2048 14.3246 16.7681 14.3727 L 76.0923 16.1981 C 77.7124 16.2479 79 17.5757 79 19.1967 L 79 32.2875 L 81.4549 32.2875 C 83.1293 32.2875 84.4795 33.6581 84.4546 35.3323 L 83.5 128.7891 L 0 128.7891 Z M 110.5 33.1092 L 173 12.1302 M 12 32.2875 L 79 32.2875"/>
                                <path id="blouse_1" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 143.5 128.7891 L 106.7737 37.7748 C 106.0872 36.1647 106.9192 34.3112 108.5786 33.7542 L 110.5 33.1092 L 106.5969 22.7101 C 105.9983 21.1151 106.8464 19.3424 108.4638 18.8076 L 164.8848 0.1522 C 166.3639 -0.3368 167.9711 0.3876 168.5844 1.8196 L 173 12.1302 L 174.3794 11.6672 C 175.8601 11.1702 177.4744 11.8924 178.0907 13.3276 L 225.5 128.7891 L 143.5 128.7891 Z M 0 128.7891 L 3.3515 35.131 C 3.4347 33.5372 4.7513 32.2875 6.3474 32.2875 L 12 32.2875 L 13.6942 17.04 C 13.8669 15.4856 15.2048 14.3246 16.7681 14.3727 L 76.0923 16.1981 C 77.7124 16.2479 79 17.5757 79 19.1967 L 79 32.2875 L 81.4549 32.2875 C 83.1293 32.2875 84.4795 33.6581 84.4546 35.3323 L 83.5 128.7891 L 0 128.7891 Z M 110.5 33.1092 L 173 12.1302 M 12 32.2875 L 79 32.2875"/>
                            </g>
                        </g>
                        <g id="g-common_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:148;y:157.438;w:149.000;h:160.776" transform="translate(148, 157.4375)">
                            <g id="cu_hands" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:-0.000;w:149.000;h:160.776">
                                <path id="hands" transform="translate(0, -3.514287527650595e-8)" fill="#fef2e6" d="M 8.5 142.0643 L 8 158.3306 L 59 159.8306 L 59.5 147.5648 L 61 145.5648 L 70.1081 132.1613 C 63.9402 134.0112 52.5 137.0625 45.5 139.0625 C 39 140.9196 35.1667 134.0627 34 130.5625 L 28 110.5625 C 25.5 102.2292 21 85.0625 19.5 80.5625 C 17.7322 75.2592 18.5 70.0648 18 60.0648 C 17.5 50.0648 7.5 43.5643 4 42.5643 C 1.2 41.7643 0.1667 43.8976 0 45.0643 L 1.5 85.0643 C 1.6667 98.0643 2.1 125.4643 2.5 131.0643 C 2.9 136.6643 6.6667 140.7309 8.5 142.0643 Z M 98.5 160.7762 L 94.5 151.5642 L 79 139.5642 C 77.486 138.1449 73.8877 135.5001 70.1081 132.1613 C 67.2304 129.6192 64.2477 126.6748 62 123.5625 C 53.6237 111.9645 50.718 99.475 43.9042 87.2858 C 42.8216 85.3491 42.9562 82.8367 44.7681 81.5561 C 46.2888 80.4813 48.2426 79.5625 50.5 79.5625 C 57 79.5625 61.3333 84.5644 63 86.0642 L 45.5 39.5662 C 44.5 36.5662 42.7 30.3645 49.5 27.5645 C 56.3 24.7645 59.3333 30.8992 60.4999 33.5659 L 77.4146 78.3317 C 75.4474 72.9436 73.6922 63.1152 80.4999 60.5623 C 88.4999 57.5623 93.1665 69.3956 94.4999 73.5623 C 94.1665 69.729 93.4999 60.5623 98.9999 58.5623 C 105.3912 56.2382 108.9999 62.0623 110.9999 66.5623 C 112.5999 70.1623 113.6665 72.729 113.9999 73.5623 C 112.6665 69.229 110.9999 60.7623 116.4999 58.5623 C 121.4999 56.5623 125.9999 58.0623 127.9999 63.5623 L 137.9999 88.0639 C 140.3332 95.396 144.9999 111.3607 144.9999 116.5623 L 144.9999 134.0623 L 148.9999 144.2123 L 98.5 160.7762 Z M 92.9996 2.5644 L 90.4996 5.0644 L 96.4999 26.5662 C 97.8332 24.7329 101.7996 20.1662 104.9996 16.5662 C 108.1996 12.9662 108.3329 9.3995 107.9996 8.0662 C 107.8329 6.5656 106.5996 3.0644 102.9996 1.0644 C 99.3996 -0.9356 95.5 0.0626 92.9996 2.5644 Z M 97.9999 31.5662 L 91.9996 39.5662 C 91.9996 39.5662 83.6737 50.0663 88.4999 56.5662 C 91.0959 60.0625 96.5 59.5625 96.5 59.5625 L 100.5 58.0626 C 104.5 56.0626 104.9999 53.5625 107.9996 49.0662 C 111.019 44.5403 112.6663 40.8995 112.9996 39.5662 C 113.3329 38.2329 112.9996 34.5662 107.9996 31.5662 C 103.9849 29.1574 99.4999 30.0662 97.9999 31.5662 Z M 63 86.0642 L 71.5 105.5625 M 77.4146 78.3317 L 77.4999 78.5623 C 77.4714 78.4864 77.443 78.4095 77.4146 78.3317 Z"/>
                                <path id="hands_1" transform="translate(0, -3.514287527650595e-8)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 8.5 142.0643 L 8 158.3306 L 59 159.8306 L 59.5 147.5648 L 61 145.5648 L 70.1081 132.1613 C 63.9402 134.0112 52.5 137.0625 45.5 139.0625 C 39 140.9196 35.1667 134.0627 34 130.5625 L 28 110.5625 C 25.5 102.2292 21 85.0625 19.5 80.5625 C 17.7322 75.2592 18.5 70.0648 18 60.0648 C 17.5 50.0648 7.5 43.5643 4 42.5643 C 1.2 41.7643 0.1667 43.8976 0 45.0643 L 1.5 85.0643 C 1.6667 98.0643 2.1 125.4643 2.5 131.0643 C 2.9 136.6643 6.6667 140.7309 8.5 142.0643 Z M 98.5 160.7762 L 94.5 151.5642 L 79 139.5642 C 77.486 138.1449 73.8877 135.5001 70.1081 132.1613 C 67.2304 129.6192 64.2477 126.6748 62 123.5625 C 53.6237 111.9645 50.718 99.475 43.9042 87.2858 C 42.8216 85.3491 42.9562 82.8367 44.7681 81.5561 C 46.2888 80.4813 48.2426 79.5625 50.5 79.5625 C 57 79.5625 61.3333 84.5644 63 86.0642 L 45.5 39.5662 C 44.5 36.5662 42.7 30.3645 49.5 27.5645 C 56.3 24.7645 59.3333 30.8992 60.4999 33.5659 L 77.4146 78.3317 C 75.4474 72.9436 73.6922 63.1152 80.4999 60.5623 C 88.4999 57.5623 93.1665 69.3956 94.4999 73.5623 C 94.1665 69.729 93.4999 60.5623 98.9999 58.5623 C 105.3912 56.2382 108.9999 62.0623 110.9999 66.5623 C 112.5999 70.1623 113.6665 72.729 113.9999 73.5623 C 112.6665 69.229 110.9999 60.7623 116.4999 58.5623 C 121.4999 56.5623 125.9999 58.0623 127.9999 63.5623 L 137.9999 88.0639 C 140.3332 95.396 144.9999 111.3607 144.9999 116.5623 L 144.9999 134.0623 L 148.9999 144.2123 L 98.5 160.7762 Z M 92.9996 2.5644 L 90.4996 5.0644 L 96.4999 26.5662 C 97.8332 24.7329 101.7996 20.1662 104.9996 16.5662 C 108.1996 12.9662 108.3329 9.3995 107.9996 8.0662 C 107.8329 6.5656 106.5996 3.0644 102.9996 1.0644 C 99.3996 -0.9356 95.5 0.0626 92.9996 2.5644 Z M 97.9999 31.5662 L 91.9996 39.5662 C 91.9996 39.5662 83.6737 50.0663 88.4999 56.5662 C 91.0959 60.0625 96.5 59.5625 96.5 59.5625 L 100.5 58.0626 C 104.5 56.0626 104.9999 53.5625 107.9996 49.0662 C 111.019 44.5403 112.6663 40.8995 112.9996 39.5662 C 113.3329 38.2329 112.9996 34.5662 107.9996 31.5662 C 103.9849 29.1574 99.4999 30.0662 97.9999 31.5662 Z M 63 86.0642 L 71.5 105.5625 M 77.4146 78.3317 L 77.4999 78.5623 C 77.4714 78.4864 77.443 78.4095 77.4146 78.3317 Z"/>
                            </g>
                        </g>
                    </g>
                    <g id="list-left-icons-buttons-left" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:672;y:24;w:276;h:108" transform="translate(672, 24)">
                        <g id="container_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:48;primary:CENTER;counter:MIN" data-position="x:108;y:69;w:168;h:60" transform="translate(108, 69)">
                            <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:0;y:0;w:168;h:60">
                                <ellipse id="vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-96;y:-24;w:72;h:72" stroke="#db8333" fill="#fef2e6" stroke-width="2" stroke-linejoin="round" transform="translate(-96, -24)" cx="36" cy="36" rx="36" ry="36"/>
                                <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-84;y:-12;w:48;h:48" transform="translate(-84, -12)">
                                    <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                        <path id="icon_3" transform="translate(10, 6)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                                    </g>
                                </g>
                                <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:24" fill="#ff00001a">
                                    <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:168;h:24" fill="#ff00001a" transform="translate(0, 36)">
                                    <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                                </g>
                                <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-108;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -108, 0)" width="24" height="24" rx="0" ry="0"/>
                                <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -36)" width="24" height="24" rx="0" ry="0"/>
                                <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:72;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, 72)" width="24" height="24" rx="0" ry="0"/>
                                <g id="g-0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-156;y:5.500;w:48.001;h:13.000" transform="translate(-156, 5.5)">
                                    <path id="line_2" transform="translate(0, 6.5)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 48 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:6.500;w:48;h:0"/>
                                    <path id="line-end_1" transform="translate(41.45703125, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.089 12.9997 L 6.5444 6.4553 L 0 0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:41.457;y:0;w:6.544;h:13.000"/>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="Frame_667" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:12 0 0 0;gap:612;primary:MIN;counter:MIN" data-position="x:12;y:132;w:948;h:74" transform="translate(12, 132)">
                    <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:12;w:168;h:62" transform="translate(0, 12)">
                        <ellipse id="vector_2" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:192;y:-24;w:72;h:72" stroke="#df5e59" fill="#ffedeb" stroke-width="2" stroke-linejoin="round" transform="translate(192, -24)" cx="36" cy="36" rx="36" ry="36"/>
                        <g id="tx-rt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:26" fill="#ff00001a">
                            <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:26" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <g id="tx-rt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:38;w:168;h:24" fill="#ff00001a" transform="translate(0, 38)">
                            <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:252;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 252, 0)" width="24" height="24" rx="0" ry="0"/>
                        <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:-12;w:48;h:48" fill="#33de7b1a" transform="translate(204, -12)">
                            <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <path id="icon_5" transform="translate(10, 6)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:10;y:6;w:29;h:36"/>
                            </g>
                        </g>
                        <g id="g-3" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:-42;w:60.001;h:60.500" transform="translate(276, -42)">
                            <path id="line_3" transform="translate(0.000762939453125, 42)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 12 L 48.0002 12 C 54.6276 12 60.0002 6.6274 60.0002 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.001;y:42;w:60.000;h:12"/>
                            <path id="line-end_2" transform="translate(0.0005550384521484375, 47.5)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 6.4553 12.9997 L 0 6.4553 L 6.5444 0" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.001;y:47.500;w:6.544;h:13.000"/>
                            <path id="line_4" transform="translate(60.000972747802734, 0)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 0 42" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:60.001;y:0;w:0.000;h:42"/>
                        </g>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:72;y:-36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 72, -36)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>