<svg xmlns="http://www.w3.org/2000/svg" width="672" height="588">    <g id="volcano-v3--family--4">        <g id="lines">            <g id="common">                <g id="cu" data-entity-classes="Decor">                    <g id="cu_1" >                        <path id="fill" transform="translate(384.826171875, 215.40234375)" fill="#f6f6f6" d="M57.3469 7.4265 L45.991 7.4265 L35.9513 0 L23.1782 15.5537 L17.2343 15.5537 L0 36.5398 L59.3386 36.5398 L86.4739 36.5398 L82.0979 31.1366 L76.1997 31.1366 L70.2971 23.1958 L57.3469 7.4265 Z"></path>
                        <path id="fill_1" transform="translate(384.826171875, 215.40234375)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 45.991 7.4265 L 57.3469 7.4265 L 70.2971 23.1958 L 76.1997 31.1366 L 82.0979 31.1366 L 86.4739 36.5398 L 59.3386 36.5398 L 0 36.5398 L 17.2343 15.5537 L 23.1782 15.5537 L 35.9513 0 L 45.991 7.4265 Z"></path></g>
                    <g id="cu_2" >                        <path id="fill_2" transform="translate(423.1817932128906, 145.115234375)" fill="#f6f6f6" d="M83.8076 79.8979 L83.8076 0 L72.6508 0 L65.7445 9.3811 L55.6501 12.9393 L31.9415 41.809 L20.983 41.809 L0 67.3598 L8.4272 73.9137 L20.983 73.9137 L37.0541 93.4833 L46.6099 93.5194 L52.0764 97.5191 L72.6352 97.5191 L83.8076 79.8979 Z"></path>
                        <path id="fill_3" transform="translate(423.1817932128906, 145.115234375)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 83.8076 0 L 83.8076 79.8979 L 72.6352 97.5191 L 52.0764 97.5191 L 46.6099 93.5194 L 37.0541 93.4833 L 20.983 73.9137 L 8.4272 73.9137 L 0 67.3598 L 20.983 41.809 L 31.9415 41.809 L 55.6501 12.9393 L 65.7445 9.3811 L 72.6508 0 L 83.8076 0 Z"></path></g>
                    <g id="cu_3" >                        <path id="fill_4" transform="translate(527.9765625, 183.81149291992188)" fill="#f6f6f6" d="M23.6147 68.1311 L31.9679 68.1311 L63.7542 68.1311 L108.647 68.1311 L93.24 49.2884 L87.1302 49.2884 L56.9469 11.4422 L49.9401 11.4422 L44.6272 0 L33.0387 0 L13.8132 23.4107 L6.9014 23.4107 L0 28.7185 L0 41.2016 L23.6147 68.1311 Z"></path>
                        <path id="fill_5" transform="translate(527.9765625, 183.81149291992188)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 63.7542 68.1311 L 108.6473 68.1311 L 93.24 49.2884 L 87.1302 49.2884 L 56.9469 11.4422 L 49.9401 11.4422 L 44.6272 0 L 33.0387 0 L 13.8132 23.4107 L 6.9014 23.4107 L 0 28.7185 L 0 41.2016 L 23.6147 68.1311 L 31.9679 68.1311 L 63.7542 68.1311 Z"></path></g>
                    <g id="cu_4" >                        <path id="fill_6" transform="translate(527.9765625, 145.115234375)" fill="#f6f6f6" d="M13.8132 54.3436 L31.08 33.9649 L15.4064 0 L0 0 L0 54.3436 L13.8132 54.3436 Z"></path>
                        <path id="fill_7" transform="translate(527.9765625, 145.115234375)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 31.08 33.9649 L 13.8132 54.3436 L 0 54.3436 L 0 0 L 15.4064 0 L 31.08 33.9649 Z"></path></g>
                    <path id="line" transform="translate(408, 210.51000213623047)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 20.5 L 18.5 20.5 L 25.0158 12.8672 M 48 0 L 36 0 L 28.4305 8.8672"></path>
                    <path id="line_1" transform="matrix(1, 0, 0, -1, 570.283203125, 233.095703125)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 45.5033 0 L 31.7807 0 L 20.7115 12.9668 L 9.0098 12.9668 L 0 23.9442"></path></g>
                <g id="cu_5" data-entity-classes="Decor">                    <g id="cu_6" >                        <path id="Vector" transform="translate(460.470703125, 16)" fill="#f6f6f6" d="M101.927 68.7666 C101.419 68.7666 100.923 68.7155 100.445 68.6181 C99.3285 74.2559 95.0084 78.7413 89.4609 80.0978 C89.4445 84.1682 86.1397 87.4629 82.0654 87.4629 C80.8772 87.4629 79.7545 87.1827 78.7597 86.6847 C76.8713 89.2336 73.8415 90.8857 70.4258 90.8857 C69.4119 90.8857 68.432 90.7402 67.5059 90.4688 L67.5059 81.4926 C66.8098 81.7286 66.0808 81.9009 65.3248 82.0019 C60.9809 82.5819 56.8803 80.6541 54.482 77.334 C53.4755 78.0371 52.2904 78.5166 50.9894 78.6903 C49.4029 78.9021 47.8633 78.63 46.5195 77.9876 L46.5195 91.2775 C43.5785 89.733 41.2212 87.2273 39.8651 84.178 C38.8752 84.6698 37.7594 84.9463 36.5791 84.9463 C32.6032 84.9463 29.3602 81.8089 29.1906 77.8748 C28.2299 78.0709 27.2354 78.1738 26.2168 78.1738 C18.0479 78.1738 11.4258 71.5517 11.4258 63.3828 C11.4258 63.1578 11.4308 62.9339 11.4408 62.7113 C8.0945 62.3278 5.4272 59.7109 4.9669 56.3887 C2.0456 55.1323 0 52.2284 0 48.8467 C0 44.3146 3.674 40.6406 8.2061 40.6406 C9.0179 40.6406 9.8023 40.7585 10.5429 40.9781 C11.0532 37.3916 14.1367 34.6338 17.8643 34.6338 C18.7087 34.6338 19.5201 34.7753 20.276 35.036 C20.6731 33.9109 21.259 32.875 21.9955 31.9661 C20.8855 30.8741 20.1973 29.3549 20.1973 27.6748 C20.1973 24.3503 22.8923 21.6553 26.2168 21.6553 C26.9251 21.6553 27.6049 21.7776 28.236 22.0023 C28.2323 21.8871 28.2305 21.7714 28.2305 21.6553 C28.2305 15.8029 32.9748 11.0586 38.8271 11.0586 C39.4434 11.0586 40.0473 11.1112 40.6347 11.2122 C41.6128 6.5038 45.7848 2.9658 50.7832 2.9658 C51.539 2.9658 52.276 3.0467 52.9858 3.2003 C54.3199 1.267 56.5505 0 59.0771 0 C62.7739 0 65.8371 2.7124 66.3853 6.2555 C67.0682 6.0475 67.793 5.9356 68.5439 5.9356 C71.8821 5.9356 74.7037 8.1472 75.6233 11.1852 C80.2127 11.7876 84.1397 14.494 86.404 18.3041 C87.8476 17.388 89.5601 16.8574 91.3965 16.8574 C96.4362 16.8574 100.543 20.8533 100.721 25.8496 L100.727 25.8496 C104.051 25.8496 106.746 28.5446 106.746 31.8691 C106.746 34.5952 104.934 36.898 102.449 37.6387 C102.465 37.9196 102.473 38.2025 102.473 38.4873 C102.473 39.2212 102.419 39.9425 102.316 40.6478 C107.156 40.826 111.148 44.3227 112.085 48.9298 C115.086 49.8716 117.264 52.6752 117.264 55.9873 C117.264 60.0717 113.953 63.3828 109.868 63.3828 C109.594 63.3828 109.324 63.3679 109.058 63.3389 C108.196 66.4681 105.33 68.7666 101.927 68.7666 Z"></path>
                        <path id="Vector_1" transform="translate(460.470703125, 16)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 100.4446 68.6181 C 100.9235 68.7155 101.4192 68.7666 101.9268 68.7666 C 105.3298 68.7666 108.196 66.4681 109.0576 63.3389 C 109.3238 63.3679 109.5942 63.3828 109.8682 63.3828 C 113.9526 63.3828 117.2637 60.0717 117.2637 55.9873 C 117.2637 52.6752 115.0864 49.8716 112.085 48.9298 C 111.1484 44.3227 107.156 40.826 102.316 40.6478 C 102.4192 39.9425 102.4727 39.2212 102.4727 38.4873 C 102.4727 38.2025 102.4646 37.9196 102.4487 37.6387 C 104.934 36.898 106.7461 34.5952 106.7461 31.8691 C 106.7461 28.5446 104.0511 25.8496 100.7266 25.8496 L 100.7206 25.8496 C 100.5427 20.8533 96.4362 16.8574 91.3965 16.8574 C 89.5601 16.8574 87.8476 17.388 86.404 18.3041 C 84.1397 14.494 80.2127 11.7876 75.6233 11.1852 C 74.7037 8.1472 71.8821 5.9355 68.5439 5.9355 C 67.793 5.9355 67.0682 6.0475 66.3853 6.2555 C 65.8371 2.7124 62.7739 0 59.0771 0 C 56.5505 0 54.3199 1.267 52.9858 3.2003 C 52.276 3.0467 51.539 2.9658 50.7832 2.9658 C 45.7848 2.9658 41.6128 6.5038 40.6347 11.2122 C 40.0473 11.1112 39.4434 11.0586 38.8271 11.0586 C 32.9748 11.0586 28.2305 15.8029 28.2305 21.6553 C 28.2305 21.7714 28.2323 21.8871 28.236 22.0023 C 27.6049 21.7776 26.9251 21.6553 26.2168 21.6553 C 22.8923 21.6553 20.1973 24.3503 20.1973 27.6748 C 20.1973 29.3549 20.8855 30.8741 21.9955 31.9661 C 21.259 32.875 20.6731 33.9109 20.276 35.036 C 19.5201 34.7753 18.7087 34.6338 17.8643 34.6338 C 14.1367 34.6338 11.0532 37.3916 10.5429 40.9781 C 9.8023 40.7585 9.0179 40.6406 8.2061 40.6406 C 3.674 40.6406 0 44.3146 0 48.8467 C 0 52.2284 2.0456 55.1323 4.9669 56.3887 C 5.4272 59.7109 8.0945 62.3278 11.4408 62.7113 C 11.4308 62.9339 11.4258 63.1578 11.4258 63.3828 C 11.4258 71.5517 18.0479 78.1738 26.2168 78.1738 C 27.2354 78.1738 28.2299 78.0709 29.1906 77.8748 C 29.3602 81.8089 32.6032 84.9463 36.5791 84.9463 C 37.7594 84.9463 38.8752 84.6698 39.8651 84.178 C 41.2212 87.2273 43.5785 89.733 46.5195 91.2775 L 46.5195 77.9876 C 47.8633 78.63 49.4029 78.9021 50.9894 78.6903 C 52.2904 78.5166 53.4755 78.0371 54.482 77.334 C 56.8803 80.6541 60.9809 82.5819 65.3248 82.0019 C 66.0808 81.9009 66.8098 81.7286 67.5059 81.4926 L 67.5059 90.4688 C 68.432 90.7402 69.4119 90.8857 70.4258 90.8857 C 73.8415 90.8857 76.8713 89.2336 78.7597 86.6847 C 79.7545 87.1827 80.8772 87.4629 82.0654 87.4629 C 86.1397 87.4629 89.4445 84.1682 89.4609 80.0978 C 95.0084 78.7413 99.3285 74.2559 100.4446 68.6181 Z"></path></g>
                    <g id="cu_7" >                        <path id="fill_8" transform="translate(506.990234375, 93.333984375)" fill="#f6f6f6" d="M14.415 38.7135 C10.4766 38.7135 7.1943 41.5227 6.4627 45.2468 C5.4859 44.3975 4.21 43.8834 2.814 43.8834 C1.7873 43.8834 0.8256 44.1615 0 44.6464 L0 0.6536 C1.3438 1.296 2.8834 1.5681 4.4699 1.3563 C5.7708 1.1826 6.9559 0.7031 7.9625 0 C10.3607 3.3201 14.4613 5.2479 18.8053 4.6679 C19.5613 4.5669 20.2903 4.3946 20.9863 4.1586 L20.9863 42.0734 C19.5141 40.0379 17.1192 38.7135 14.415 38.7135 Z"></path>
                        <path id="fill_9" transform="translate(506.990234375, 93.333984375)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.415 38.7135 C 10.4766 38.7135 7.1943 41.5227 6.4627 45.2468 C 5.4859 44.3975 4.21 43.8834 2.814 43.8834 C 1.7873 43.8834 0.8256 44.1615 0 44.6464 L 0 0.6536 C 1.3438 1.296 2.8834 1.5681 4.4699 1.3563 C 5.7708 1.1826 6.9559 0.7031 7.9625 0 C 10.3607 3.3201 14.4613 5.2479 18.8053 4.6679 C 19.5613 4.5669 20.2903 4.3946 20.9863 4.1586 L 20.9863 42.0734 C 19.5141 40.0379 17.1192 38.7135 14.415 38.7135 Z"></path></g>
                    <path id="line_2" transform="translate(502.537109375, 80.96875)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 6.9009 C 0.3687 9.662 2.1145 11.9013 4.4532 13.0193 C 5.797 13.6616 7.3366 13.9337 8.9231 13.7219 C 10.2241 13.5482 11.4092 13.0688 12.4157 12.3656 C 14.814 15.6858 18.9146 17.6135 23.2585 17.0335 C 24.0145 16.9326 24.7435 16.7603 25.4396 16.5243 C 28.7948 15.3867 31.3852 12.7697 32.552 9.5346 C 33.866 10.0184 35.3138 10.1982 36.7983 10 C 41.6706 9.3495 45.093 4.8723 44.4425 0"></path>
                    <path id="line_3" transform="matrix(-0.9659258127212524, -0.2588190734386444, 0.2588190734386444, -0.9659258127212524, 498.716796875, 55.783203125)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 11.2219 C 3.5382 11.2219 6.6077 9.2153 8.1328 6.278 C 9.1198 6.7988 10.2446 7.0936 11.4382 7.0936 C 15.3559 7.0936 18.5319 3.9177 18.5319 0"></path>
                    <path id="line_4" transform="matrix(-0.9659258127212524, -0.2588190734386444, 0.2588190734386444, -0.9659258127212524, 562.662109375, 56.501953125)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 9.2319 5.6267 C 8.3626 6.076 7.3758 6.3297 6.3297 6.3297 C 2.8339 6.3297 0 3.4958 0 0"></path>
                    <path id="line_5" transform="matrix(0.8660253882408142, -0.5, 0.5, 0.8660253882408142, 472.044921875, 79.25390625)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 9.2319 5.6267 C 8.3626 6.076 7.3758 6.3297 6.3297 6.3297 C 2.8339 6.3297 0 3.4958 0 0"></path>
                    <path id="line_6" transform="matrix(0.9905956387519836, -0.1368217021226883, 0.1368217021226883, 0.9905956387519836, 492.314453125, 83.1875)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 9.5688 5.832 C 8.6677 6.2977 7.6449 6.5607 6.5607 6.5607 C 2.9373 6.5607 0 3.6234 0 0"></path>
                    <path id="line_7" transform="matrix(-0.29726967215538025, -0.9547935724258423, -0.9547935724258423, 0.29726967215538025, 550.22265625, 96.0263671875)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 0 4.629 3.0546 8.5446 7.2581 9.841"></path>
                    <path id="line_8" transform="matrix(-1, 0, 0, 1, 543.970703125, 34.2607421875)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 13.7049 C 2.4584e-7 10.8928 1.8633 8.5159 4.4226 7.7413 C 4.5809 3.4391 8.1186 -3.7949e-7 12.4595 3.1407e-14 C 16.3953 3.4408e-7 19.6708 2.8272 20.366 6.5615 C 20.4111 6.5605 20.4563 6.5599 20.5017 6.5599 C 23.5718 6.5599 26.0605 9.0487 26.0605 12.1188"></path>
                    <path id="line_9" transform="matrix(-4.371137762859689e-8, 1, -1, -4.371137762859689e-8, 520.53125, 98.1044921875)" fill="none" stroke="#b8b8b8" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 16.4648 4.3711e-7"></path>
                    <path id="line_10" transform="matrix(4.371138473402425e-8, 1, -1, 4.371138473402425e-8, 514.43359375, 107.62890625)" fill="none" stroke="#b8b8b8" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 21.25 0"></path>
                    <path id="line_11" transform="matrix(-5.8693437665624515e-8, 1, 1, 9.033917791612112e-8, 520.53125, 119.599609375)" fill="none" stroke="#b8b8b8" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 6.1789 0"></path>
                    <path id="line_12" transform="matrix(4.3711342101460104e-8, 1, -1, 4.3711342101460104e-8, 514.43359375, 93.333984375)" fill="none" stroke="#b8b8b8" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 4.3711e-7 L 8.0029 0"></path></g>
                <g id="cu_8" data-entity-classes="Decor">                    <path id="line_13" transform="translate(0, 240)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 468.7607 12 L 359.9995 12 L 347.9995 0 L 335.9995 0 L 323.9995 6.8371 L 311.9995 6.8371 L 299.9995 12 L 227.9995 12 L 215.9995 6.8371 L 203.9995 6.8371 L 197.8438 12 L 180 12 L 173.3372 6.8371 L 160.7578 12 L 0 12 M 562.523 12 L 648 12 L 660 6.8371 L 672 12"></path></g>
                <g id="cu_9" data-entity-classes="Decor">                    <g id="cu_10" >                        <path id="fill_10" transform="translate(398.3828125, 132.0438995361328)" fill="#fef2e6" d="M123.022 0 C126.478 0 129.428 2.1622 130.594 5.2073 C130.768 5.1827 130.946 5.1699 131.127 5.1699 C133.209 5.1699 134.896 6.8576 134.896 8.9395 L135.122 8.9395 C135.577 7.8979 136.616 7.1699 137.826 7.1699 C139.035 7.1699 140.074 7.8979 140.529 8.9395 L142.73 8.9395 C142.992 8.9395 143.243 9.0419 143.43 9.2249 L144.261 10.039 C144.448 10.222 144.699 10.3244 144.961 10.3244 L147.304 10.3244 C147.698 10.3244 148.056 10.556 148.217 10.9158 L154.575 25.1207 C155.216 26.5526 154.646 28.2366 153.267 28.9841 L152.542 29.3767 L145.049 13.087 L129.596 13.087 L129.596 67.4307 L143.409 67.4307 L160.223 47.052 L173.972 47.052 C175.14 47.052 176.201 47.7293 176.693 48.7882 L181.845 59.8796 L182.472 59.8796 C183.577 59.8796 184.472 60.775 184.472 61.8796 L184.472 63.107 C184.472 63.1623 184.428 63.207 184.372 63.207 L179.538 63.207 L174.23 51.764 L162.634 51.7833 L143.409 75.1938 L136.502 75.1938 L129.596 80.5185 L129.596 92.9847 L153.245 119.903 L161.641 119.903 L181.999 147.91 L181.71 167.578 L194.472 187.886 L213.402 215.377 L213.402 229.122 L242.331 267.631 L242.331 324.651 L184.472 377.579 L184.472 409.011 L131.812 455.626 L90.7113 425.697 L61.8555 425.697 L45.7844 408.012 L45.7844 376.824 L23.2385 350.6 L22.8376 336.147 L0 310.775 L0 253.403 L38.7973 209.067 L38.7973 179.595 L76.8427 137.525 L76.8427 124.736 L68.5375 114.473 L62.6419 114.473 L56.7428 106.57 L43.7926 90.8009 L32.4368 90.8009 L22.4003 83.3567 L16.2819 90.8009 C14.7436 89.5065 14.5488 87.209 15.8471 85.6741 L18.9634 81.9896 C20.6934 79.9443 23.728 79.6258 25.8449 81.2673 L33.2385 87.0006 L45.7844 87.0006 L61.8555 106.57 L71.4014 106.57 L76.8427 110.581 L97.4522 110.581 L108.609 93.0334 L108.609 13.087 L97.4522 13.087 L90.6781 22.272 L90.3923 22.1224 C88.7497 21.2624 88.2727 19.1333 89.3913 17.6546 L95.6844 9.3361 C95.8734 9.0863 96.1686 8.9395 96.4819 8.9395 L100.429 8.9395 C100.843 7.9025 101.857 7.1699 103.042 7.1699 C104.228 7.1699 105.242 7.9025 105.656 8.9395 L106.153 8.9395 C106.9 6.747 108.976 5.1699 111.421 5.1699 C112.817 5.1699 114.093 5.6841 115.07 6.5333 C115.802 2.8093 119.084 0 123.022 0 Z"></path>
                        <path id="fill_11" transform="translate(398.3828125, 132.0438995361328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 123.0225 0 C 126.4777 0 129.4279 2.1622 130.5938 5.2073 C 130.768 5.1827 130.946 5.1699 131.127 5.1699 C 133.2088 5.1699 134.8965 6.8576 134.8965 8.9395 L 135.1222 8.9395 C 135.5771 7.8979 136.6164 7.1699 137.8257 7.1699 C 139.035 7.1699 140.0742 7.8979 140.5292 8.9395 L 142.73 8.9395 C 142.9915 8.9395 143.2427 9.0419 143.4296 9.2249 L 144.2612 10.039 C 144.4481 10.222 144.6992 10.3244 144.9607 10.3244 L 147.3038 10.3244 C 147.6981 10.3244 148.0555 10.556 148.2166 10.9158 L 154.5754 25.1207 C 155.2164 26.5526 154.6459 28.2366 153.2666 28.9841 L 152.5422 29.3767 L 145.0493 13.087 L 129.5958 13.087 L 129.5958 67.4307 L 143.409 67.4307 L 160.2226 47.052 L 173.9721 47.052 C 175.1396 47.052 176.2011 47.7293 176.6929 48.7882 L 181.8446 59.8796 L 182.4723 59.8796 C 183.5769 59.8796 184.4723 60.775 184.4723 61.8796 L 184.4723 63.107 C 184.4723 63.1623 184.4275 63.207 184.3723 63.207 L 179.538 63.207 L 174.2296 51.764 L 162.6344 51.7833 L 143.409 75.1938 L 136.5024 75.1938 L 129.5958 80.5185 L 129.5958 92.9847 L 153.2447 119.9026 L 161.6412 119.9026 L 181.9985 147.9097 L 181.7096 167.5775 L 194.4723 187.8856 L 213.4016 215.3768 L 213.4016 229.1224 L 242.331 267.6315 L 242.331 324.6505 L 184.4723 377.579 L 184.4723 409.011 L 131.8115 455.626 L 90.7113 425.6968 L 61.8555 425.6968 L 45.7844 408.012 L 45.7844 376.8238 L 23.2385 350.6002 L 22.8376 336.147 L 0 310.7745 L 0 253.4028 L 38.7973 209.0672 L 38.7973 179.5954 L 76.8427 137.5246 L 76.8427 124.7359 L 68.5375 114.4729 L 62.6419 114.4729 L 56.7428 106.5702 L 43.7926 90.8009 L 32.4368 90.8009 L 22.4003 83.3567 L 16.2819 90.8009 C 14.7436 89.5065 14.5488 87.209 15.8471 85.6741 L 18.9634 81.9896 C 20.6934 79.9443 23.728 79.6258 25.8449 81.2673 L 33.2385 87.0006 L 45.7844 87.0006 L 61.8555 106.5702 L 71.4014 106.5702 L 76.8427 110.5811 L 97.4522 110.5811 L 108.6089 93.0334 L 108.6089 13.087 L 97.4522 13.087 L 90.6781 22.272 L 90.3923 22.1224 C 88.7497 21.2624 88.2727 19.1333 89.3913 17.6546 L 95.6844 9.3361 C 95.8734 9.0863 96.1686 8.9395 96.4819 8.9395 L 100.4287 8.9395 C 100.8435 7.9025 101.8574 7.1699 103.0425 7.1699 C 104.2275 7.1699 105.2415 7.9025 105.6562 8.9395 L 106.1531 8.9395 C 106.8997 6.747 108.9764 5.1699 111.4214 5.1699 C 112.8174 5.1699 114.0933 5.684 115.0702 6.5333 C 115.8018 2.8093 119.084 0 123.0225 0 Z"></path></g>
                    <g id="cu_11" >                        <path id="fill_12" transform="translate(541.80078125, 131.7147979736328)" fill="#fef2e6" d="M14.3713 0.6766 C10.6465 -0.9408 3.6939 0.5458 0 2.979 C4.8882 1.1499 9.3872 1.881 13.3748 3.665 C15.3273 4.5385 16.7842 1.7243 14.3713 0.6766 Z"></path>
                        <path id="fill_13" transform="translate(541.80078125, 131.7147979736328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 14.3713 0.6766 C 10.6465 -0.9408 3.6939 0.5458 0 2.979 C 4.8882 1.1499 9.3872 1.881 13.3748 3.665 C 15.3273 4.5385 16.7842 1.7243 14.3713 0.6766 Z"></path></g>
                    <g id="cu_12" >                        <path id="fill_14" transform="translate(533.361328125, 115.85249328613281)" fill="#fef2e6" d="M12.5896 0.1475 C7.4685 1.639 1.6907 8.9756 0 14.534 C3.4045 8.5838 8.564 5.547 14.0903 4.0035 C16.7964 3.2476 15.9069 -0.8186 12.5896 0.1475 Z"></path>
                        <path id="fill_15" transform="translate(533.361328125, 115.85249328613281)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 12.5896 0.1475 C 7.4685 1.639 1.6907 8.9756 0 14.534 C 3.4045 8.5838 8.564 5.547 14.0903 4.0034 C 16.7964 3.2476 15.9069 -0.8186 12.5896 0.1475 Z"></path></g>
                    <g id="cu_13" >                        <path id="fill_16" transform="translate(480.658203125, 129.4794464111328)" fill="#fef2e6" d="M12.5545 3.83 C9.872 1.4081 4.4406 -0.6524 1.2322 0.1933 C-0.8461 0.741 -0.0175 3.1941 1.6676 2.73 C5.1087 1.7823 8.8327 1.7483 12.5545 3.83 Z"></path>
                        <path id="fill_17" transform="translate(480.658203125, 129.4794464111328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 12.5545 3.83 C 9.872 1.4081 4.4406 -0.6524 1.2322 0.1933 C -0.8461 0.741 -0.0175 3.1941 1.6676 2.73 C 5.1087 1.7823 8.8327 1.7483 12.5545 3.83 Z"></path></g>
                    <g id="cu_14" >                        <path id="fill_18" transform="translate(485.9765625, 110.52046203613281)" fill="#fef2e6" d="M16.7149 17.7931 C16.0836 14.814 13.4458 7.6245 7.9457 2.6995 C1.0705 -3.4567 -1.7642 2.5744 1.1187 4.5142 C1.7915 4.9669 2.5216 5.2306 3.3572 5.5323 C6.1025 6.5236 9.9864 7.9259 16.7149 17.7931 Z"></path>
                        <path id="fill_19" transform="translate(485.9765625, 110.52046203613281)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="none" stroke-miterlimit="4"  d="M 16.7149 17.7931 C 16.0836 14.814 13.4458 7.6245 7.9457 2.6995 C 1.0705 -3.4567 -1.7642 2.5744 1.1187 4.5142 C 1.7915 4.9669 2.5216 5.2306 3.3572 5.5323 C 6.1025 6.5235 9.9864 7.9259 16.7149 17.7931 Z"></path></g>
                    <path id="line_14" transform="matrix(-4.3711455788297826e-8, 1, -1, -4.3711455788297826e-8, 514.43359375, 146.5165557861328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 23.2295 0"></path>
                    <path id="line_15" transform="matrix(-4.3711455788297826e-8, 1, -1, -4.3711455788297826e-8, 514.43359375, 190.6347198486328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 29.4406 0"></path>
                    <path id="line_16" transform="matrix(4.371121420376767e-8, 1, -1, 4.371121420376767e-8, 520.53125, 167.8095245361328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 8.2969 0"></path>
                    <path id="line_17" transform="matrix(7.281700931116575e-8, 1, -1, 7.281700931116575e-8, 514.43359375, 177.7646026611328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 4.9805 0"></path>
                    <path id="line_18" transform="matrix(-4.3711370523169535e-8, 1, -1, -4.3711370523169535e-8, 520.53125, 211.6444854736328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 18.9844 0"></path>
                    <path id="line_19" transform="matrix(4.371121420376767e-8, 1, -1, 4.371121420376767e-8, 520.53125, 146.5165557861328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 15.25 0"></path>
                    <path id="line_20" transform="matrix(4.37113598650285e-8, 1, -1, 4.37113598650285e-8, 520.53125, 182.7431182861328)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 22.466 9.8202e-7"></path></g>
                <g id="cu_15" data-entity-classes="Decor">                    <g id="cu_16" >                        <path id="fill_20" transform="translate(444, 244.96484375)" fill="#fefbdb" d="M108 74.8245 L108 35.1326 L78.6943 0 L69.2523 0 L54.8652 25.3462 L54.8652 35.1326 L24 69.0041 L24 117.004 L12 129.004 L0 141.004 L0 189.004 L14 203.004 L14 227.004 L61.3642 284.368 L84.6358 284.368 L156 213.004 L156 165.004 L132 129.004 L132 109.03 L108 74.8245 Z"></path>
                        <path id="fill_21" transform="translate(444, 244.96484375)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 108 35.1326 L 108 74.8245 L 132 109.0299 L 132 129.004 L 156 165.004 L 156 213.004 L 84.6358 284.3681 L 61.3642 284.3681 L 14 227.004 L 14 203.004 L 0 189.004 L 0 141.004 L 12 129.004 L 24 117.0041 L 24 69.0041 L 54.8652 35.1326 L 54.8652 25.3462 L 69.2523 0 L 78.6943 0 L 108 35.1326 Z"></path></g></g>
                <g id="ar-with-terminator" data-entity-classes="LeaderLine">                    <path id="line_21" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 167.99610090255737)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 144 0"></path></g></g>
            <g id="g-1">                <g id="ar-with-terminator_1" data-entity-classes="LeaderLine">                    <path id="line_22" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 335.9960813522339)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 180 0"></path></g></g>
            <g id="g-2">                <g id="ar-with-terminator_2" data-entity-classes="LeaderLine">                    <path id="line_23" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="matrix(1, 0, 0, -1, 348, 396.0038757324219)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0037 L 142 0"></path></g></g>
            <g id="g-3">                <g id="ar-with-terminator_3" data-entity-classes="LeaderLine">                    <path id="line_24" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 455.9960951805115)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 204 0"></path></g></g>
            <g id="g-4">                <g id="ar-with-terminator_4" data-entity-classes="LeaderLine">                    <path id="line_25" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 515.9961218833923)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 168 0"></path></g></g></g>
        <path id="tx-rc-4" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 492)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-3" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 432)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-2" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 372)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-1" transform="translate(24, 312)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-top" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 144)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 492)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 432)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 372)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 312)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-top" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 144)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 504)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 444)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 384)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 324)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lc-under" transform="translate(24, 264)" fill="#ff00001a" d="M0 0 L252 0 L252 24 L0 24 L0 0 Z"></path>
        <path id="tx-lc-over" transform="translate(24, 216)" fill="#ff00001a" d="M0 0 L252 0 L252 24 L0 24 L0 0 Z"></path>
        <path id="tx-lb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 24, 72)" fill="#ff00001a" d="M0 0 L312 0 L312 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 474)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4_1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 534)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 414)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 354)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 294)" width="24" height="24" rx="0" ry="0"></rect></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>