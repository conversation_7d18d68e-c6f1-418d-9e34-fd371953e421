<svg xmlns="http://www.w3.org/2000/svg" width="888" height="420">    <g id="decision-v3--family--3">        <g id="lines">            <g id="g-3">                <g id="cu">                    <g id="cu_1" >                        <path id="Vector" transform="translate(413.99975708329293, 240)" fill="#f6f6f6" d="M18.2735 3.6985 C17.1024 3.2473 15.8301 3 14.5 3 C8.701 3 4 7.701 4 13.5 C4 14.4121 4.1163 15.297 4.3349 16.1407 C1.6359 19.3079 -1.9856e-7 23.4575 4.5475e-13 28 C2.4874e-7 33.6905 2.5673 38.7643 6.5755 42.0628 C6.2026 43.1397 6 44.2962 6 45.5 C6 51.299 10.701 56 16.5 56 C17.2901 56 18.0599 55.9127 18.8001 55.7473 C21.3536 58.3652 24.9788 60 29 60 C33.5014 60 37.5067 57.9514 40.0677 54.7683 C41.6517 56.1577 43.7275 57 46 57 C50.9706 57 55 52.9706 55 48 C55 47.1394 54.8792 46.3069 54.6536 45.5188 C57.9818 41.6447 60 36.5635 60 31 C60 26.6626 58.7733 22.6183 56.6556 19.2107 C56.8812 18.1763 57 17.102 57 16 C57 7.7157 50.2843 1 42 1 C40.2108 1 38.4947 1.3133 36.9037 1.8879 C34.5425 0.6823 31.8533 -1.2472e-7 29 0 C24.9329 1.7778e-7 21.1993 1.3863 18.2735 3.6985 Z"></path>
                        <path id="Vector_1" transform="translate(413.99975708329293, 240)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 18.2735 3.6985 C 17.1024 3.2473 15.8301 3 14.5 3 C 8.701 3 4 7.701 4 13.5 C 4 14.4121 4.1163 15.297 4.3349 16.1407 C 1.6359 19.3079 -1.9856e-7 23.4575 1.8075e-14 28 C 2.4874e-7 33.6905 2.5673 38.7643 6.5755 42.0628 C 6.2026 43.1397 6 44.2962 6 45.5 C 6 51.299 10.701 56 16.5 56 C 17.2901 56 18.0599 55.9127 18.8001 55.7473 C 21.3536 58.3652 24.9788 60 29 60 C 33.5014 60 37.5067 57.9514 40.0677 54.7683 C 41.6517 56.1577 43.7275 57 46 57 C 50.9706 57 55 52.9706 55 48 C 55 47.1394 54.8792 46.3069 54.6536 45.5188 C 57.9818 41.6447 60 36.5635 60 31 C 60 26.6626 58.7733 22.6183 56.6556 19.2107 C 56.8812 18.1763 57 17.102 57 16 C 57 7.7157 50.2843 1 42 1 C 40.2108 1 38.4947 1.3133 36.9037 1.8879 C 34.5425 0.6823 31.8533 -1.2472e-7 29 2.1316e-14 C 24.9329 1.7778e-7 21.1993 1.3862 18.2735 3.6985 Z"></path></g>
                    <ellipse id="Vector_2" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="translate(436.0000228881836, 214)" cx="8" cy="8" rx="8" ry="8"></ellipse>
                    <ellipse id="Vector_3" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1, 1.224648970167536e-16, 1.224648970167536e-16, -1, 440.0001220703125, 204)" cx="4" cy="4" rx="4" ry="4"></ellipse></g></g>
            <g id="g-2">                <g id="cu_2">                    <g id="cu_3" >                        <path id="Vector_4" transform="translate(552, 138)" fill="#f6f6f6" d="M29 60 C33.9502 60 38.5185 58.4023 42.1944 55.7057 C43.7818 56.5326 45.5863 57 47.5 57 C53.8513 57 59 51.8513 59 45.5 C59 42.5141 57.8621 39.794 55.9962 37.7498 C58.4346 35.827 60 32.8463 60 29.5 C60 27.0802 59.1815 24.8516 57.8062 23.076 C57.9338 22.2366 58 21.3763 58 20.5 C58 11.3873 50.8366 4 42 4 C41.0095 4 40.04 4.0928 39.0993 4.2705 C36.0114 1.6132 31.9496 0 27.5 0 C22.9328 0 18.7743 1.6996 15.658 4.4832 C14.6613 4.1693 13.6004 4 12.5 4 C6.701 4 2 8.701 2 14.5 C2 17.0722 2.9249 19.4283 4.4603 21.2541 C2.9145 23.4174 2 26.0976 2 29 C2 31.3016 2.5751 33.4636 3.5841 35.3388 C1.3594 37.7456 0 40.9641 0 44.5 C0 51.9558 6.0442 58 13.5 58 C15.1219 58 16.677 57.714 18.1175 57.1897 C21.3267 58.9782 25.0413 60 29 60 Z"></path>
                        <path id="Vector_5" transform="translate(552, 138)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 29 60 C 33.9502 60 38.5185 58.4023 42.1944 55.7057 C 43.7818 56.5326 45.5863 57 47.5 57 C 53.8513 57 59 51.8513 59 45.5 C 59 42.5141 57.8621 39.794 55.9962 37.7498 C 58.4346 35.827 60 32.8463 60 29.5 C 60 27.0802 59.1815 24.8516 57.8062 23.076 C 57.9338 22.2366 58 21.3763 58 20.5 C 58 11.3873 50.8366 4 42 4 C 41.0095 4 40.04 4.0928 39.0993 4.2705 C 36.0114 1.6132 31.9496 0 27.5 0 C 22.9328 0 18.7743 1.6996 15.658 4.4832 C 14.6613 4.1693 13.6004 4 12.5 4 C 6.701 4 2 8.701 2 14.5 C 2 17.0722 2.9249 19.4283 4.4602 21.2541 C 2.9145 23.4174 2 26.0976 2 29 C 2 31.3016 2.5751 33.4636 3.5841 35.3388 C 1.3594 37.7456 0 40.9641 0 44.5 C 0 51.9558 6.0442 58 13.5 58 C 15.1219 58 16.677 57.714 18.1175 57.1897 C 21.3267 58.9782 25.0413 60 29 60 Z"></path></g>
                    <ellipse id="Vector_6" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="translate(526, 142)" cx="8" cy="8" rx="8" ry="8"></ellipse>
                    <ellipse id="Vector_7" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1, 1.224648970167536e-16, 1.224648970167536e-16, -1, 508.00000034969116, 148)" cx="4" cy="4" rx="4" ry="4"></ellipse></g></g>
            <g id="g-1">                <g id="cu_4">                    <g id="cu_5" >                        <path id="Vector_8" transform="translate(276, 138)" fill="#f6f6f6" d="M10.598 9.0005 C10.5653 9.0002 10.5327 9 10.5 9 C4.701 9 0 13.701 0 19.5 C0 22.5738 1.3208 25.3392 3.4259 27.2594 C3.1475 28.4594 3 29.7119 3 31 C3 31.7449 3.0493 32.4779 3.1448 33.1957 C1.7875 35.3013 1 37.8087 1 40.5 C1 47.9558 7.0442 54 14.5 54 C14.8465 54 15.19 53.9869 15.5299 53.9613 C19.172 57.6819 24.3084 60 30 60 C35.6415 60 40.7374 57.7226 44.3734 54.0594 C45.6214 54.6621 47.0212 55 48.5 55 C53.7467 55 58 50.7467 58 45.5 C58 42.6285 56.726 40.0545 54.7124 38.3125 C57.9476 35.4695 60 31.2324 60 26.5 C60 22.4248 58.4781 18.717 55.9895 15.9503 C55.9965 15.801 56 15.6509 56 15.5 C56 10.2533 51.7467 6 46.5 6 C45.9205 6 45.3531 6.0519 44.8022 6.1513 C40.6815 2.3382 35.1199 0 29 0 C21.475 0 14.7941 3.5352 10.598 9.0005 Z"></path>
                        <path id="Vector_9" transform="translate(276, 138)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 10.598 9.0005 C 10.5653 9.0002 10.5327 9 10.5 9 C 4.701 9 0 13.701 0 19.5 C 0 22.5738 1.3208 25.3392 3.4259 27.2594 C 3.1475 28.4594 3 29.7119 3 31 C 3 31.7449 3.0493 32.4779 3.1448 33.1957 C 1.7875 35.3013 1 37.8087 1 40.5 C 1 47.9558 7.0442 54 14.5 54 C 14.8465 54 15.19 53.9869 15.5299 53.9613 C 19.172 57.6819 24.3084 60 30 60 C 35.6415 60 40.7374 57.7226 44.3734 54.0594 C 45.6214 54.6621 47.0212 55 48.5 55 C 53.7467 55 58 50.7467 58 45.5 C 58 42.6285 56.726 40.0545 54.7124 38.3125 C 57.9476 35.4694 60 31.2324 60 26.5 C 60 22.4248 58.4781 18.717 55.9895 15.9503 C 55.9965 15.801 56 15.6509 56 15.5 C 56 10.2533 51.7467 6 46.5 6 C 45.9205 6 45.3531 6.0519 44.8022 6.1513 C 40.6815 2.3382 35.1199 0 29 0 C 21.475 0 14.7941 3.5352 10.598 9.0005 Z"></path></g>
                    <ellipse id="Vector_10" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="translate(346, 142)" cx="8" cy="8" rx="8" ry="8"></ellipse>
                    <ellipse id="Vector_11" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1, 1.224648970167536e-16, 1.224648970167536e-16, -1, 372, 148)" cx="4" cy="4" rx="4" ry="4"></ellipse></g></g></g>
        <g id="Frame" fill="#ffffff" transform="translate(408, 102)"></g>
        <g id="ic-cc-0">            <path id="rect" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 408.00000000000006, 96)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
            <g id="icon" transform="translate(408.00000000000006, 96)">                <path id="icon_1" transform="translate(7.7760009765625, 3.87109375)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 28.8 26.064 C 28.32 25.68 27.792 25.248 27.216 24.768 C 26.16 23.904 25.152 23.328 24.192 23.04 C 23.136 22.656 22.176 22.512 21.312 22.608 C 20.352 22.8 19.44 23.184 18.576 23.76 C 18.192 24.048 17.76 24.336 17.28 24.624 M 0.288 31.824 C 0.288 31.728 0.288 31.632 0.288 31.536 C 0.192 31.248 0.144 30.96 0.144 30.672 C 0.048 30.384 0 30.096 0 29.808 C 0 29.424 0 29.04 0 28.656 C 0 28.272 0.096 27.12 0.288 25.2 C 0.48 23.376 0.816 21.6 1.296 19.872 C 1.776 18.144 2.448 16.464 3.312 14.832 C 4.176 13.296 5.232 11.808 6.48 10.368 C 7.728 8.928 8.352 8.208 8.352 8.208 C 8.352 8.208 9.072 7.584 10.512 6.336 C 11.952 5.088 13.44 4.032 14.976 3.168 C 16.608 2.304 18.288 1.632 20.016 1.152 C 21.744 0.672 23.52 0.336 25.344 0.144 C 27.264 -0.048 29.184 -0.048 31.104 0.144 C 32.928 0.336 34.704 0.672 36.432 1.152 C 38.16 1.632 39.84 2.304 41.472 3.168 C 43.008 4.032 44.496 5.088 45.936 6.336 C 47.376 7.584 48.096 8.208 48.096 8.208 C 48.096 8.208 48.72 8.928 49.968 10.368 C 51.216 11.808 52.272 13.296 53.136 14.832 C 54 16.464 54.672 18.144 55.152 19.872 C 55.632 21.6 55.968 23.376 56.16 25.2 C 56.352 27.12 56.304 29.328 56.016 31.824 C 55.728 34.32 55.152 36.672 54.288 38.88 C 53.424 41.088 52.272 43.104 50.832 44.928 C 49.392 46.752 47.664 48.432 45.648 49.968 C 44.592 50.736 43.584 51.504 42.624 52.272 M 22.896 62.64 C 23.184 62.544 23.472 62.4 23.76 62.208 C 24.336 61.92 24.816 61.584 25.2 61.2 C 25.68 60.816 26.064 60.384 26.352 59.904 C 26.64 59.424 26.88 58.896 27.072 58.32 C 27.168 57.744 27.216 57.456 27.216 57.456 C 27.216 57.456 27.216 57.264 27.216 56.88 C 27.312 56.496 27.312 56.016 27.216 55.44 C 27.216 54.96 27.168 54.384 27.072 53.712 C 26.976 53.136 26.832 52.464 26.64 51.696 C 26.544 51.024 26.496 50.688 26.496 50.688 C 26.496 50.688 27.984 50.112 30.96 48.96 C 34.032 47.712 35.856 46.944 36.432 46.656 C 36.912 46.368 37.296 46.032 37.584 45.648 C 37.968 45.264 38.208 44.832 38.304 44.352 C 38.4 43.872 38.4 43.296 38.304 42.624 C 38.208 42.048 38.16 41.76 38.16 41.76 C 38.16 41.76 38.112 41.616 38.016 41.328 C 37.824 41.136 37.68 40.896 37.584 40.608 C 37.392 40.416 37.2 40.224 37.008 40.032 C 36.816 39.84 36.576 39.648 36.288 39.456 C 36 39.264 35.712 39.12 35.424 39.024 C 35.136 38.928 34.848 38.88 34.56 38.88 C 34.368 38.88 34.128 38.88 33.84 38.88 C 33.552 38.88 33.264 38.928 32.976 39.024 C 32.688 39.12 32.544 39.168 32.544 39.168 C 32.544 39.168 29.712 40.224 24.048 42.336 C 18.48 44.544 15.456 45.696 14.976 45.792 C 14.496 45.888 14.064 45.936 13.68 45.936 C 13.392 45.84 13.152 45.648 12.96 45.36 C 12.768 45.072 12.624 44.736 12.528 44.352 C 12.528 43.872 12.528 43.632 12.528 43.632 C 12.528 43.632 12.48 43.2 12.384 42.336 C 12.384 41.376 12.336 40.752 12.24 40.464 C 12.24 40.176 12.192 39.936 12.096 39.744 C 12 39.456 11.904 39.216 11.808 39.024 C 11.616 38.736 11.424 38.496 11.232 38.304 C 11.04 38.112 10.848 37.92 10.656 37.728 C 10.368 37.632 10.128 37.536 9.936 37.44 C 9.648 37.344 9.36 37.248 9.072 37.152 C 8.88 37.056 8.64 37.008 8.352 37.008 C 8.064 37.104 7.92 37.152 7.92 37.152 C 6.864 37.344 6.144 37.584 5.76 37.872 C 5.376 38.16 5.088 38.496 4.896 38.88 C 4.704 39.264 4.56 39.744 4.464 40.32 C 4.368 40.8 4.32 41.04 4.32 41.04 C 4.32 41.04 4.32 43.008 4.32 46.944 C 4.32 50.88 4.368 53.28 4.464 54.144 C 4.56 55.104 4.752 55.968 5.04 56.736 C 5.328 57.504 5.664 58.272 6.048 59.04 C 6.528 59.712 7.104 60.384 7.776 61.056 C 8.352 61.632 8.64 61.92 8.64 61.92 C 8.64 61.92 9.024 62.16 9.792 62.64 C 10.56 63.12 11.328 63.504 12.096 63.792 C 12.864 64.08 13.68 64.272 14.544 64.368 C 15.408 64.368 16.272 64.32 17.136 64.224 C 18 64.128 18.432 64.08 18.432 64.08 C 18.432 64.08 19.2 63.84 20.736 63.36 C 22.176 62.88 22.896 62.64 22.896 62.64 Z M 14.688 14.544 C 14.592 14.544 14.496 14.544 14.4 14.544 C 14.208 14.544 14.064 14.496 13.968 14.4 C 13.872 14.304 13.776 14.208 13.68 14.112 C 13.584 14.016 13.536 13.872 13.536 13.68 C 13.536 13.584 13.536 13.44 13.536 13.248 C 13.536 13.056 13.584 12.912 13.68 12.816 C 13.776 12.72 13.872 12.624 13.968 12.528 C 14.064 12.432 14.208 12.384 14.4 12.384 C 14.496 12.384 14.592 12.384 14.688 12.384 M 14.688 14.544 C 14.688 14.544 14.736 14.544 14.832 14.544 C 15.024 14.544 15.168 14.496 15.264 14.4 C 15.36 14.304 15.456 14.208 15.552 14.112 C 15.648 14.016 15.696 13.872 15.696 13.68 C 15.696 13.584 15.696 13.44 15.696 13.248 C 15.696 13.056 15.648 12.912 15.552 12.816 C 15.456 12.72 15.36 12.624 15.264 12.528 C 15.168 12.432 15.024 12.384 14.832 12.384 C 14.736 12.384 14.688 12.384 14.688 12.384 M 29.952 14.544 C 29.856 14.544 29.76 14.544 29.664 14.544 C 29.568 14.544 29.472 14.496 29.376 14.4 C 29.184 14.304 29.088 14.208 29.088 14.112 C 28.992 14.016 28.944 13.872 28.944 13.68 C 28.944 13.584 28.944 13.44 28.944 13.248 C 28.944 13.056 28.992 12.912 29.088 12.816 C 29.088 12.72 29.184 12.624 29.376 12.528 C 29.472 12.432 29.568 12.384 29.664 12.384 C 29.76 12.384 29.856 12.384 29.952 12.384 M 29.952 14.544 C 30.048 14.544 30.144 14.544 30.24 14.544 C 30.336 14.544 30.432 14.496 30.528 14.4 C 30.72 14.304 30.816 14.208 30.816 14.112 C 30.912 14.016 30.96 13.872 30.96 13.68 C 31.056 13.584 31.056 13.44 30.96 13.248 C 30.96 13.056 30.912 12.912 30.816 12.816 C 30.816 12.72 30.72 12.624 30.528 12.528 C 30.432 12.432 30.336 12.384 30.24 12.384 C 30.144 12.384 30.048 12.384 29.952 12.384"></path></g></g>
        <path id="tx-ct-3-desc" data-entity-classes="Description" transform="translate(312, 348)" fill="#ff00001a" d="M0 0 L264 0 L264 72 L0 72 L0 0 Z"></path>
        <g id="tx-cc-3" data-entity-classes="DescTitle">            <path id="rect_1" transform="translate(204, 312)" fill="#ff00001a" d="M0 0 L480 0 L480 24 L0 24 L0 0 Z"></path>
            <text id="Label" fill="#b960e2" transform="translate(419, 312)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
        <g id="ic-cc-3">            <path id="rect_2" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 426.00000000000006, 252)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
            <g id="icon_2" transform="translate(426.00000000000006, 252)">                <path id="icon_3" transform="translate(6, 3)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 8.0098 4.7231 16.0001 9.4783 24 14.2174 C 19.3972 16.7863 11.9664 20.8863 6.5915 23.8469 C 2.7784 25.9472 0 27.4741 0 27.4741 L 0.0002 0 Z M 24 14.2174 C 19.0755 19.4363 14.2416 24.7394 9.3627 30 C 8.441 27.9487 7.5519 25.8809 6.5915 23.8469"></path></g></g>
        <path id="tx-lt-2-desc" data-entity-classes="Description" transform="translate(624, 192)" fill="#ff00001a" d="M0 0 L264 0 L264 72 L0 72 L0 0 Z"></path>
        <g id="tx-lc-2" data-entity-classes="DescTitle">            <path id="rect_3" transform="translate(624, 156)" fill="#ff00001a" d="M0 0 L480 0 L480 24 L0 24 L0 0 Z"></path>
            <text id="Label_1" fill="#17aee1" transform="translate(624, 156)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
        <g id="ic-cc-2">            <path id="rect_4" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 564.0000000000001, 150)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
            <g id="icon_4" transform="translate(564.0000000000001, 150)">                <path id="icon_5" transform="translate(6, 3)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 8.0098 4.7231 16.0001 9.4783 24 14.2174 C 19.3972 16.7863 11.9664 20.8863 6.5915 23.8469 C 2.7784 25.9472 0 27.4741 0 27.4741 L 0.0002 0 Z M 24 14.2174 C 19.0755 19.4363 14.2416 24.7394 9.3627 30 C 8.441 27.9487 7.5519 25.8809 6.5915 23.8469"></path></g></g>
        <path id="tx-rt-1-desc" data-entity-classes="Description" transform="translate(0, 192)" fill="#ff00001a" d="M0 0 L264 0 L264 72 L0 72 L0 0 Z"></path>
        <g id="tx-rc-1" data-entity-classes="DescTitle">            <path id="rect_5" transform="translate(-216, 156)" fill="#ff00001a" d="M0 0 L480 0 L480 24 L0 24 L0 0 Z"></path>
            <text id="Label_2" fill="#93c332" transform="translate(210, 156)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g>
        <g id="ic-cc-1">            <path id="rect_6" transform="matrix(1, 1.7151245100058819e-15, -1.7151245100058819e-15, 1, 288.00000000000006, 150)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
            <g id="icon_6" transform="translate(288.00000000000006, 150)">                <path id="icon_7" transform="translate(6, 3)" fill="none" stroke="#93c332" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="1.5"  d="M 0.0002 0 C 8.0098 4.7231 16.0001 9.4783 24 14.2174 C 19.3972 16.7863 11.9664 20.8863 6.5915 23.8469 C 2.7784 25.9472 0 27.4741 0 27.4741 L 0.0002 0 Z M 24 14.2174 C 19.0755 19.4363 14.2416 24.7394 9.3627 30 C 8.441 27.9487 7.5519 25.8809 6.5915 23.8469"></path></g></g>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 432, 288)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 600, 156)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 264, 156)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 684, 312)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 180, 312)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 744, 120)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 120, 120)" width="24" height="24" rx="0" ry="0"></rect>
        <g id="tx-cb-title">            <path id="rect_7" transform="translate(276, 0)" fill="#ff00001a" d="M0 0 L336 0 L336 72 L0 72 L0 0 Z"></path>
            <text id="Label_3" fill="#484848" transform="translate(417, 24)" font-size="20" font-family="Roboto" font-weight="700" data-align="horizontal:LEFT;vertical:TOP"></text></g></g></svg>