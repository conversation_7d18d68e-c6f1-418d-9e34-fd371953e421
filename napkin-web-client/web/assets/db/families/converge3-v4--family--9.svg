<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="converge3-v4--family--9">        <g id="lines">            <g id="g-9">                <g id="ar-with-terminator">                    <path id="line" marker-end="url(#arrow)" transform="matrix(0.3420202136039733, 0.9396926760673523, -0.9396926760673523, 0.3420202136039733, 334.9587122374477, 222.7966616352761)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-8">                <g id="ar-with-terminator_1">                    <path id="line_1" marker-end="url(#arrow)" transform="matrix(0.8660253286361693, 0.5000000596046449, -0.5000000596046449, 0.8660253286361693, 274.4697685241699, 263.07200050354004)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-7">                <g id="ar-with-terminator_2">                    <path id="line_2" marker-end="url(#arrow)" transform="matrix(0.9848077297210693, -0.17364810407161702, 0.17364810407161702, 0.9848077297210693, 265.53792288154364, 342.87682378292084)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-6">                <g id="ar-with-terminator_3">                    <path id="line_3" marker-end="url(#arrow)" transform="matrix(0.6427876949310304, -0.7660443782806395, 0.7660443782806395, 0.6427876949310304, 302.47604978084564, 407.0161546468735)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-5">                <g id="ar-with-terminator_4">                    <path id="line_4" marker-end="url(#arrow)" transform="matrix(6.241054263423516e-8, -1, 1, 6.241054263423516e-8, 372.00030994415283, 432.00018310546875)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-4">                <g id="ar-with-terminator_5">                    <path id="line_5" marker-end="url(#arrow)" transform="matrix(-0.6427875757217406, -0.7660444974899293, 0.7660444974899293, -0.6427875757217406, 441.3181834912948, 406.85571003970927)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-3">                <g id="ar-with-terminator_6">                    <path id="line_6" marker-end="url(#arrow)" transform="matrix(-0.9848077893257141, -0.17364823818206798, 0.17364823818206798, -0.9848077893257141, 478.2564495141422, 343.0374653765784)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-2">                <g id="ar-with-terminator_7">                    <path id="line_7" marker-end="url(#arrow)" transform="matrix(-0.8660253882408143, 0.5000000596046447, -0.5000000596046447, -0.8660253882408143, 469.5311584472656, 263.0719738006592)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g>
            <g id="g-1">                <g id="ar-with-terminator_8">                    <path id="line_8" marker-end="url(#arrow)" transform="matrix(-0.34202006459236156, 0.9396926760673523, -0.9396926760673523, -0.34202006459236156, 408.8352290852047, 222.6361966836401)" fill="none" stroke="#666666" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 36 0"></path></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 48)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-end" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 287.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-9" fill="#1ac6ff33" transform="translate(323.0475769042969, 210.51853942871094)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-8" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 266.46923828125, 258)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(1, 1.942890293094024e-16, -1.942890293094024e-16, 1, 253.64358520507812, 330.76904296875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 290.5908203125, 394.7426452636719)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 359.765380859375, 420.4063720703125)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 429.421142578125, 394.73291015625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 466.359130859375, 330.754150390625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 453.765380859375, 257.59375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 396.938232421875, 210.51318359375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-10" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 360, 204.0001220703125)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-9" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 290.578857421875, 229.267333984375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-8" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 253.640625, 293.24609375)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 266.46923828125, 366)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, 1.1102230246251565e-16, -1.1102230246251565e-16, 1, 323.061767578125, 413.486572265625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="translate(396.952392578125, 413.481689453125)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 453.53857421875, 365.98681640625)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, 1.942890293094024e-16, -1.942890293094024e-16, 1, 466.356689453125, 293.231201171875)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, 2.220446049250313e-16, -2.220446049250313e-16, 1, 429.40966796875, 229.257568359375)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rb-9" transform="matrix(1, -1.2766959045507065e-8, 1.2766959045507065e-8, 1, 180, 120)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-8" transform="translate(96, 216)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-7" transform="translate(84, 312)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-rt-6" transform="translate(120, 408)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-ct-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 456)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lt-4" transform="translate(468, 408)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-3" transform="translate(504, 312)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="translate(492, 216)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="tx-lb-1" transform="translate(408, 120)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>