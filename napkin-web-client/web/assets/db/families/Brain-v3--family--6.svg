<svg xmlns="http://www.w3.org/2000/svg" width="816" height="492">    <g id="Brain-v3--family--6">        <g id="lines">            <g id="g-6">                <g id="cu">                    <g id="cu_1" >                        <path id="Vector" transform="translate(442.6307678222656, 248.97622680664062)" fill="#ffedeb" d="M77.3327 0.4347 C77.2339 0.2874 77.1303 0.1449 77.0291 0 C73.6377 10.4897 66.6353 19.3658 57.4707 25.1743 C60.6399 30.278 60.9731 36.9019 57.7669 42.4746 C53.7412 49.4768 45.4454 52.5272 38.011 50.2381 C30.6137 63.1864 14.5404 69.464 0 64.2081 C2.8237 73.7473 11.3836 80.6807 21.5058 80.6807 C27.9825 80.6807 33.8175 77.8415 37.9222 73.3003 C42.9722 73.2512 47.7951 72.4898 47.7951 72.4898 C54.4816 71.1292 60.6522 68.3538 65.9787 64.4954 C77.73 55.9828 85.3693 42.1873 85.3693 26.616 C85.3693 16.9172 82.4074 7.9084 77.3327 0.4347 Z"></path>
                        <path id="Vector_1" transform="translate(442.6307678222656, 248.97622680664062)" fill="none" stroke="#df5e59" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 77.3327 0.4347 C 77.2339 0.2874 77.1303 0.1449 77.0291 0 C 73.6377 10.4897 66.6353 19.3658 57.4707 25.1743 C 60.6399 30.278 60.9731 36.9019 57.7669 42.4746 C 53.7412 49.4768 45.4454 52.5272 38.011 50.2381 C 30.6137 63.1864 14.5404 69.464 0 64.2081 C 2.8237 73.7473 11.3836 80.6807 21.5058 80.6807 C 27.9825 80.6807 33.8175 77.8415 37.9222 73.3003 C 42.9722 73.2512 47.7951 72.4898 47.7951 72.4898 C 54.4816 71.1292 60.6522 68.3538 65.9787 64.4954 C 77.73 55.9828 85.3693 42.1873 85.3693 26.616 C 85.3693 16.9172 82.4074 7.9084 77.3327 0.4347 Z"></path></g>
                    <g id="cu_2" >                        <path id="Vector_2" transform="translate(412.77435302734375, 297.8268127441406)" fill="#ffedeb" d="M14.5879 18.3662 C20.0378 19.6212 25.6283 18.3392 29.8589 15.3576 C32.6826 24.8968 41.2425 31.8302 51.3647 31.8302 C57.8414 31.8302 63.6764 28.991 67.7811 24.4498 C72.8311 24.4007 77.6541 23.6393 77.6541 23.6393 C84.3405 22.2787 90.5111 19.5033 95.8376 15.6449 C96.2893 17.8897 96.5287 20.2131 96.5287 22.5906 C96.5287 42.0129 80.7048 57.761 61.1834 57.761 C59.1323 57.761 57.1232 57.5842 55.1683 57.2526 C55.1683 57.3091 55.1683 57.3631 55.1683 57.4171 C55.1683 75.5083 40.4304 90.1733 22.2493 90.1733 C13.6721 90.1733 5.8601 86.9067 0.0005 81.5575 L0.0005 0 C-0.0637 8.5765 5.8379 16.3547 14.5854 18.3662 L14.5879 18.3662 Z"></path>
                        <path id="Vector_3" transform="translate(412.77435302734375, 297.8268127441406)" fill="none" stroke="#df5e59" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 14.5879 18.3662 C 20.0378 19.6212 25.6283 18.3392 29.8589 15.3576 C 32.6826 24.8968 41.2425 31.8302 51.3647 31.8302 C 57.8414 31.8302 63.6764 28.991 67.7811 24.4498 C 72.8311 24.4007 77.6541 23.6393 77.6541 23.6393 C 84.3405 22.2787 90.5111 19.5033 95.8376 15.6449 C 96.2893 17.8897 96.5287 20.2131 96.5287 22.5906 C 96.5287 42.0129 80.7048 57.761 61.1834 57.761 C 59.1323 57.761 57.1232 57.5841 55.1683 57.2526 C 55.1683 57.3091 55.1683 57.3631 55.1683 57.4171 C 55.1683 75.5083 40.4304 90.1733 22.2493 90.1733 C 13.6721 90.1733 5.8601 86.9067 0.0005 81.5575 L 0.0005 0 C -0.0637 8.5765 5.8379 16.3547 14.5854 18.3662 L 14.5879 18.3662 Z"></path></g>
                    <path id="Vector_4" transform="translate(420.7596435546875, 313.4324035644531)" fill="none" stroke="#df5e59" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 47.1806 41.647 C 37.6136 40.0137 29.3573 34.5539 24.0654 26.9083 C 24.0654 26.9083 24.0629 26.9034 24.0605 26.9009 C 23.905 26.9034 23.747 26.9058 23.5915 26.9058 C 10.5616 26.9058 0 16.3965 0 3.4311 C 0 2.2669 0.0864 1.12 0.2493 0"></path>
                    <path id="Vector_5" transform="translate(412.77679443359375, 344.1916809082031)" fill="none" stroke="#df5e59" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0.0005 0 C -0.0637 8.5765 5.8379 16.3547 14.5854 18.3662"></path>
                    <path id="Vector_6" transform="translate(469.872802734375, 328.8888244628906)" fill="none" stroke="#df5e59" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0.0019 C 1.9647 -0.03 3.9665 0.3433 5.8868 1.171 C 10.3913 3.1137 13.3754 7.0974 14.2171 11.5649"></path></g>
                <g id="ar-with-terminator">                    <path id="line" marker-end="url(#arrow)" transform="matrix(-1, -8.742277657347586e-8, 8.742277657347586e-8, -1, 576, 360.0000309944153)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 72 0"></path></g></g>
            <g id="g-5">                <g id="cu_3">                    <g id="cu_4" >                        <path id="Vector_7" transform="translate(288, 248.9762725830078)" fill="#f2fae1" d="M47.3583 50.2381 C39.9239 52.5247 31.6281 49.4743 27.6024 42.4746 C24.3986 36.9019 24.7318 30.278 27.8986 25.1743 C18.734 19.3683 11.7316 10.4897 8.3402 0 C8.239 0.1449 8.1353 0.2874 8.0366 0.4347 C2.9619 7.9084 0 16.9172 0 26.616 C0 42.1873 7.6392 55.9828 19.3906 64.4954 C24.717 68.3538 30.8876 71.1267 37.5741 72.4898 C37.5741 72.4898 42.3971 73.2512 47.4471 73.3003 C51.5518 77.844 57.3867 80.6807 63.8634 80.6807 C73.9857 80.6807 82.5456 73.7473 85.3693 64.2081 C70.8288 69.4664 54.7556 63.1864 47.3583 50.2381 Z"></path>
                        <path id="Vector_8" transform="translate(288, 248.9762725830078)" fill="none" stroke="#93c332" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 47.3583 50.2381 C 39.9239 52.5247 31.6281 49.4743 27.6024 42.4746 C 24.3986 36.9019 24.7318 30.278 27.8986 25.1743 C 18.734 19.3683 11.7316 10.4897 8.3402 0 C 8.239 0.1449 8.1353 0.2874 8.0366 0.4347 C 2.9619 7.9084 0 16.9172 0 26.616 C 0 42.1873 7.6392 55.9828 19.3906 64.4954 C 24.717 68.3538 30.8876 71.1267 37.5741 72.4898 C 37.5741 72.4898 42.3971 73.2512 47.4471 73.3003 C 51.5518 77.844 57.3867 80.6807 63.8634 80.6807 C 73.9857 80.6807 82.5456 73.7473 85.3693 64.2081 C 70.8288 69.4664 54.7556 63.1864 47.3583 50.2381 Z"></path></g>
                    <g id="cu_5" >                        <path id="Vector_9" transform="translate(306.699462890625, 297.8267364501953)" fill="#f2fae1" d="M81.9409 18.3662 C76.491 19.6212 70.9004 18.3392 66.6698 15.3576 C63.8461 24.8968 55.2863 31.8302 45.164 31.8302 C38.6873 31.8302 32.8524 28.991 28.7477 24.4498 C23.6976 24.4007 18.8747 23.6393 18.8747 23.6393 C12.1882 22.2787 6.0176 19.5033 0.6911 15.6449 C0.2394 17.8897 0 20.2131 0 22.5906 C0 42.0129 15.8239 57.761 35.3453 57.761 C37.3964 57.761 39.4056 57.5842 41.3604 57.2526 C41.3604 57.3091 41.3604 57.3631 41.3604 57.4171 C41.3604 75.5083 56.0983 90.1733 74.2794 90.1733 C82.8566 90.1733 90.6686 86.9067 96.5282 81.5575 L96.5282 0 C96.5924 8.5765 90.6908 16.3547 81.9433 18.3662 L81.9409 18.3662 Z"></path>
                        <path id="Vector_10" transform="translate(306.699462890625, 297.8267364501953)" fill="none" stroke="#93c332" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 81.9409 18.3662 C 76.491 19.6212 70.9004 18.3392 66.6698 15.3576 C 63.8461 24.8968 55.2863 31.8302 45.164 31.8302 C 38.6873 31.8302 32.8524 28.991 28.7477 24.4498 C 23.6976 24.4007 18.8747 23.6393 18.8747 23.6393 C 12.1882 22.2787 6.0176 19.5033 0.6911 15.6449 C 0.2394 17.8897 0 20.2131 0 22.5906 C 0 42.0129 15.8239 57.761 35.3453 57.761 C 37.3964 57.761 39.4056 57.5841 41.3604 57.2526 C 41.3604 57.3091 41.3604 57.3631 41.3604 57.4171 C 41.3604 75.5083 56.0983 90.1733 74.2794 90.1733 C 82.8566 90.1733 90.6686 86.9067 96.5282 81.5575 L 96.5282 0 C 96.5924 8.5765 90.6908 16.3547 81.9433 18.3662 L 81.9409 18.3662 Z"></path></g>
                    <path id="Vector_11" transform="translate(348.05987548828125, 313.4324493408203)" fill="none" stroke="#93c332" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 41.647 C 9.5669 40.0137 17.8232 34.5539 23.1151 26.9083 C 23.1151 26.9083 23.1176 26.9034 23.1201 26.9009 C 23.2756 26.9034 23.4335 26.9058 23.589 26.9058 C 36.6189 26.9058 47.1806 16.3965 47.1806 3.4311 C 47.1806 2.2669 47.0942 1.12 46.9313 0"></path>
                    <path id="Vector_12" transform="translate(388.6378860473633, 344.1917266845703)" fill="none" stroke="#93c332" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 14.5849 0 C 14.649 8.5765 8.7475 16.3547 0 18.3662"></path>
                    <path id="Vector_13" transform="translate(331.91016387939453, 328.8887481689453)" fill="none" stroke="#93c332" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 14.2171 0.0019 C 12.2524 -0.03 10.2506 0.3433 8.3303 1.171 C 3.8258 3.1137 0.8417 7.0974 0 11.5649"></path></g>
                <g id="ar-with-terminator_1">                    <path id="line_1" marker-end="url(#arrow)" transform="translate(240, 359.9999542236328)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 72 0"></path></g></g>
            <g id="g-4">                <g id="cu_6">                    <g id="cu_7" >                        <path id="Vector_14" transform="translate(412.77978515625, 159.95974731445312)" fill="#e7fbf2" d="M95.4521 34.355 C95.4521 19.3978 86.9144 6.4274 74.4275 0 C74.4275 9.5171 70.9819 18.9778 65.2506 25.7147 C61.0867 27.0336 57.409 29.8948 55.1629 34.0185 C53.9337 36.2732 53.2574 38.6776 53.0871 41.0796 L53.0451 43.6928 C52.976 47.7993 51.9393 51.9525 49.8364 55.8134 C42.8537 68.6338 26.7459 73.3936 13.8617 66.4455 C5.4178 61.892 0.4542 53.4384 0 44.5451 L0 97.6543 C4.2503 100.049 9.1646 101.417 14.3998 101.417 C16.1078 101.417 17.7812 101.272 19.4078 100.992 L19.4177 100.992 C21.7008 108.623 28.8069 114.191 37.2212 114.191 C47.4792 114.191 55.7923 105.916 55.7923 95.7116 C55.7923 94.0022 55.5578 92.3468 55.1234 90.775 L55.1653 90.7627 C67.588 90.7627 77.6585 80.7421 77.6585 68.3809 C77.6585 67.8774 77.6091 66.8827 77.6091 66.8827 C86.5417 61.1601 92.9641 51.8935 94.8696 41.0821 C95.2546 38.8986 95.457 36.6514 95.457 34.3599 L95.4521 34.355 Z"></path>
                        <path id="Vector_15" transform="translate(412.77978515625, 159.95974731445312)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 95.4521 34.355 C 95.4521 19.3978 86.9144 6.4274 74.4275 0 C 74.4275 9.5171 70.9819 18.9778 65.2506 25.7147 C 61.0867 27.0336 57.409 29.8948 55.1629 34.0185 C 53.9337 36.2732 53.2574 38.6776 53.0871 41.0796 L 53.0451 43.6928 C 52.976 47.7993 51.9393 51.9525 49.8364 55.8134 C 42.8537 68.6338 26.7459 73.3936 13.8617 66.4455 C 5.4178 61.892 0.4542 53.4384 0 44.5451 L 0 97.6543 C 4.2503 100.049 9.1646 101.417 14.3998 101.417 C 16.1078 101.417 17.7812 101.2721 19.4078 100.9921 L 19.4177 100.9921 C 21.7008 108.623 28.8069 114.1908 37.2212 114.1908 C 47.4792 114.1908 55.7923 105.9164 55.7923 95.7116 C 55.7923 94.0022 55.5578 92.3468 55.1234 90.775 L 55.1653 90.7627 C 67.588 90.7627 77.6585 80.7421 77.6585 68.3809 C 77.6585 67.8774 77.6091 66.8827 77.6091 66.8827 C 86.5417 61.1601 92.9641 51.8935 94.8696 41.0821 C 95.2546 38.8986 95.457 36.6514 95.457 34.3599 L 95.4521 34.355 Z"></path></g>
                    <g id="cu_8" >                        <path id="Vector_16" transform="translate(412.77923583984375, 201.03701782226562)" fill="#e7fbf2" d="M67.865 98.1775 C75.2993 100.464 83.5951 97.4137 87.6208 90.414 C90.8246 84.8412 90.4914 78.2173 87.3247 73.1137 C96.4893 67.3076 103.492 58.429 106.883 47.9393 C108.344 43.4153 109.141 38.5941 109.141 33.5863 C109.141 20.4121 103.672 8.5102 94.8701 0 C92.9646 10.8139 86.5422 20.0781 77.6096 25.8006 C77.6417 26.2967 77.659 26.7953 77.659 27.2988 C77.659 39.66 67.5885 49.6807 55.1658 49.6807 L55.1239 49.6929 C55.5608 51.2648 55.7928 52.9202 55.7928 54.6296 C55.7928 64.8368 47.4773 73.1087 37.2217 73.1087 C28.8074 73.1087 21.7014 67.5409 19.4182 59.9076 L19.4083 59.9076 C17.7818 60.19 16.1083 60.3349 14.4003 60.3349 C9.1651 60.3349 4.2533 58.9669 0.0005 56.5723 L0.0005 96.7849 C-0.0637 105.361 5.8379 113.14 14.5854 115.151 C20.0353 116.406 25.6259 115.124 29.8564 112.142 C44.3969 117.401 60.4701 111.121 67.8675 98.1726 L67.865 98.1775 Z"></path>
                        <path id="Vector_17" transform="translate(412.77923583984375, 201.03701782226562)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 67.865 98.1775 C 75.2993 100.464 83.5951 97.4137 87.6208 90.414 C 90.8246 84.8412 90.4914 78.2173 87.3247 73.1137 C 96.4893 67.3076 103.4917 58.429 106.883 47.9393 C 108.3442 43.4153 109.1415 38.5941 109.1415 33.5863 C 109.1415 20.4121 103.6719 8.5102 94.8701 0 C 92.9646 10.8139 86.5422 20.0781 77.6096 25.8006 C 77.6417 26.2967 77.659 26.7953 77.659 27.2988 C 77.659 39.66 67.5885 49.6807 55.1658 49.6807 L 55.1239 49.6929 C 55.5608 51.2648 55.7928 52.9202 55.7928 54.6296 C 55.7928 64.8368 47.4773 73.1087 37.2217 73.1087 C 28.8074 73.1087 21.7014 67.5409 19.4182 59.9076 L 19.4083 59.9076 C 17.7818 60.19 16.1083 60.3349 14.4003 60.3349 C 9.1651 60.3349 4.2533 58.9669 0.0005 56.5723 L 0.0005 96.7849 C -0.0637 105.3614 5.8379 113.1396 14.5854 115.1511 C 20.0353 116.4062 25.6259 115.1241 29.8564 112.1425 C 44.3969 117.4009 60.4701 111.1208 67.8675 98.1726 L 67.865 98.1775 Z"></path></g>
                    <path id="Vector_18" transform="translate(479.1879653930664, 208.95022583007812)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 6.3138 3.6349 10.69 10.2367 11.1984 17.8897"></path>
                    <path id="Vector_19" transform="translate(445.447021484375, 228.87112426757812)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 5.5733 14.7485 C 2.1029 10.809 0 5.6489 0 0"></path>
                    <path id="Vector_20" transform="translate(478.0279006958008, 184.84976196289062)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0.8247 C 4.3811 -0.563 9.3004 -0.2413 13.6617 2.1092"></path>
                    <path id="Vector_21" transform="translate(458.70886993408203, 221.32620239257812)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 1.7845 2.8809 4.3219 5.3566 7.5133 7.1053 C 8.5549 7.6751 9.6237 8.1467 10.7122 8.52"></path>
                    <path id="Vector_22" transform="translate(431.4249038696289, 245.33889770507812)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 3.1717 0 C 1.1699 2.9497 0 6.506 0 10.3325 C 0 12.1672 0.269 13.938 0.7676 15.6105"></path>
                    <path id="Vector_23" transform="translate(498.8821105957031, 221.32376098632812)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 4.6674 28.3647 C 9.7051 19.1841 8.0613 7.4369 0 0"></path>
                    <path id="Vector_24" transform="translate(464.23536682128906, 268.3003234863281)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 19.8546 16.9712 C 16.9396 7.712 9.0313 1.3827 0 0"></path>
                    <path id="Vector_25" transform="translate(442.6332244873047, 287.0497131347656)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 26.1346 C 7.2517 18.9188 7.2517 7.2158 0 0"></path>
                    <path id="Vector_26" transform="translate(491.17378997802734, 267.8950500488281)" fill="none" stroke="#3cc583" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 3.4876 0.9603 6.6741 3.0824 8.9202 6.2557"></path></g>
                <g id="ar-with-terminator_2">                    <path id="line_2" marker-end="url(#arrow)" transform="matrix(-1, 0, 0, 1, 576, 252.00003051757812)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 40 0"></path></g></g>
            <g id="g-3">                <g id="cu_9">                    <g id="cu_10" >                        <path id="Vector_27" transform="translate(307.7706604003906, 159.96218872070312)" fill="#feecf7" d="M81.5904 66.4431 C68.7061 73.3912 52.5984 68.6314 45.6157 55.8109 C43.5127 51.95 42.4761 47.7969 42.407 43.6904 L42.365 41.0772 C42.1947 38.6752 41.5159 36.2707 40.2892 34.0161 C38.0431 29.8899 34.3654 27.0311 30.2015 25.7122 C24.4702 18.9778 21.0245 9.5147 21.0245 0 C8.5352 6.4274 0 19.4002 0 34.355 C0 36.6489 0.2024 38.8937 0.5874 41.0772 C2.4929 51.8911 8.9153 61.1552 17.8479 66.8778 C17.8479 66.8778 17.7985 67.8725 17.7985 68.376 C17.7985 80.7372 27.869 90.7578 40.2917 90.7578 L40.3336 90.7701 C39.8968 92.3419 39.6647 93.9973 39.6647 95.7067 C39.6647 105.914 47.9803 114.186 58.2358 114.186 C66.6501 114.186 73.7562 108.618 76.0393 100.985 L76.0492 100.985 C77.6757 101.267 79.3492 101.412 81.0572 101.412 C86.2924 101.412 91.2042 100.044 95.457 97.6494 L95.457 44.5402 C95.0028 53.4335 90.0392 61.8871 81.5953 66.4406 L81.5904 66.4431 Z"></path>
                        <path id="Vector_28" transform="translate(307.7706604003906, 159.96218872070312)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 81.5904 66.4431 C 68.7061 73.3912 52.5984 68.6314 45.6157 55.8109 C 43.5127 51.95 42.4761 47.7969 42.407 43.6904 L 42.365 41.0772 C 42.1947 38.6752 41.5159 36.2707 40.2892 34.0161 C 38.0431 29.8899 34.3654 27.0311 30.2015 25.7122 C 24.4702 18.9778 21.0245 9.5147 21.0245 0 C 8.5352 6.4274 0 19.4002 0 34.355 C 0 36.6489 0.2024 38.8937 0.5874 41.0772 C 2.4929 51.8911 8.9153 61.1552 17.8479 66.8778 C 17.8479 66.8778 17.7985 67.8725 17.7985 68.376 C 17.7985 80.7372 27.869 90.7578 40.2917 90.7578 L 40.3336 90.7701 C 39.8968 92.3419 39.6647 93.9973 39.6647 95.7067 C 39.6647 105.914 47.9803 114.1859 58.2358 114.1859 C 66.6501 114.1859 73.7562 108.6181 76.0393 100.9847 L 76.0492 100.9847 C 77.6757 101.2671 79.3492 101.4121 81.0572 101.4121 C 86.2924 101.4121 91.2042 100.044 95.457 97.6494 L 95.457 44.5402 C 95.0028 53.4335 90.0392 61.8871 81.5953 66.4406 L 81.5904 66.4431 Z"></path></g>
                    <g id="cu_11" >                        <path id="Vector_29" transform="translate(294.0841979980469, 201.04190063476562)" fill="#feecf7" d="M94.7412 60.3349 C93.0332 60.3349 91.3597 60.19 89.7331 59.91 L89.7233 59.91 C87.4401 67.5409 80.3341 73.1087 71.9198 73.1087 C61.6618 73.1087 53.3487 64.8344 53.3487 54.6296 C53.3487 52.9202 53.5832 51.2648 54.0176 49.6929 L53.9756 49.6806 C41.5529 49.6806 31.4825 39.66 31.4825 27.2988 C31.4825 26.7953 31.4998 26.2968 31.5319 25.8006 C22.5993 20.0781 16.1769 10.8115 14.2714 0 C5.4696 8.5077 0 20.4096 0 33.5863 C0 38.5941 0.7948 43.4153 2.2585 47.9393 C5.6498 58.429 12.6522 67.3051 21.8168 73.1137 C18.6476 78.2173 18.3144 84.8412 21.5206 90.414 C25.5464 97.4161 33.8421 100.466 41.2765 98.1775 C48.6738 111.126 64.7471 117.403 79.2875 112.147 C83.5181 115.129 89.1087 116.411 94.5586 115.156 C103.306 113.142 109.208 105.364 109.143 96.7898 L109.143 56.5772 C104.893 58.9718 99.9788 60.3398 94.7437 60.3398 L94.7412 60.3349 Z"></path>
                        <path id="Vector_30" transform="translate(294.0841979980469, 201.04190063476562)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 94.7412 60.3349 C 93.0332 60.3349 91.3597 60.19 89.7331 59.91 L 89.7233 59.91 C 87.4401 67.5409 80.3341 73.1087 71.9198 73.1087 C 61.6618 73.1087 53.3487 64.8344 53.3487 54.6296 C 53.3487 52.9202 53.5832 51.2648 54.0176 49.6929 L 53.9756 49.6806 C 41.5529 49.6806 31.4825 39.66 31.4825 27.2988 C 31.4825 26.7953 31.4998 26.2968 31.5319 25.8006 C 22.5993 20.0781 16.1769 10.8115 14.2714 0 C 5.4696 8.5077 0 20.4096 0 33.5863 C 0 38.5941 0.7948 43.4153 2.2584 47.9393 C 5.6498 58.429 12.6522 67.3051 21.8168 73.1137 C 18.6476 78.2173 18.3144 84.8412 21.5206 90.414 C 25.5464 97.4161 33.8421 100.4665 41.2765 98.1775 C 48.6738 111.1257 64.7471 117.4033 79.2875 112.1474 C 83.5181 115.129 89.1087 116.4111 94.5586 115.156 C 103.306 113.1421 109.2076 105.3638 109.1434 96.7898 L 109.1434 56.5772 C 104.8931 58.9718 99.9788 60.3398 94.7437 60.3398 L 94.7412 60.3349 Z"></path></g>
                    <path id="Vector_31" transform="translate(325.6136245727539, 208.95022583007812)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 11.1984 0 C 4.8847 3.6349 0.5085 10.2367 0 17.8897"></path>
                    <path id="Vector_32" transform="translate(364.9796905517578, 228.87112426757812)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 14.7485 C 3.4704 10.809 5.5733 5.6489 5.5733 0"></path>
                    <path id="Vector_33" transform="translate(324.31036376953125, 184.84988403320312)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 13.6617 0.8247 C 9.2806 -0.563 4.3614 -0.2413 0 2.1092"></path>
                    <path id="Vector_34" transform="translate(346.57889556884766, 221.32620239257812)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 10.7122 0 C 8.9277 2.8809 6.3903 5.3566 3.1988 7.1053 C 2.1572 7.6751 1.0885 8.1467 0 8.52"></path>
                    <path id="Vector_35" transform="translate(381.40589904785156, 245.33889770507812)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 2.0017 2.9497 3.1717 6.506 3.1717 10.3325 C 3.1717 12.1672 2.9027 13.938 2.4041 15.6105"></path>
                    <path id="Vector_36" transform="translate(309.5874557495117, 221.32376098632812)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 2.863 28.3647 C -2.1747 19.1841 -0.5308 7.4369 7.5305 0"></path>
                    <path id="Vector_37" transform="translate(331.91263580322266, 268.3003234863281)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 16.9712 C 2.915 7.712 10.8233 1.3827 19.8546 0"></path>
                    <path id="Vector_38" transform="translate(315.9060363769531, 267.8950500488281)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 8.9202 0 C 5.4326 0.9603 2.2461 3.0824 0 6.2557"></path>
                    <path id="Vector_39" transform="translate(367.9280471801758, 287.0497131347656)" fill="none" stroke="#d95da7" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 5.4388 26.1346 C -1.8129 18.9188 -1.8129 7.2158 5.4388 0"></path></g>
                <g id="ar-with-terminator_3">                    <path id="line_3" marker-end="url(#arrow)" transform="translate(240, 252.00003051757812)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 42 0"></path></g></g>
            <g id="g-2">                <g id="cu_12">                    <g id="cu_13" >                        <path id="Vector_40" transform="translate(412.77484130859375, 153.42431640625)" fill="#fefbdb" d="M13.8642 72.981 C26.7484 79.9292 42.8562 75.1694 49.8389 62.3489 C51.9418 58.488 52.9785 54.3348 53.0476 50.2283 L53.0895 47.6151 C53.2598 45.2131 53.9386 42.8087 55.1653 40.554 C57.4114 36.4279 61.0891 33.5691 65.2531 32.2502 C70.9843 25.5157 74.43 16.0526 74.43 6.538 C74.4029 5.1528 74.3017 3.7848 74.1338 2.4364 C71.2682 0.8866 67.9681 0 64.478 0 C59.6576 0 55.2468 1.6726 51.7715 4.4724 C52.8156 7.6113 52.8131 11.116 51.5173 14.439 C49.481 19.663 44.7864 23.0769 39.5685 23.7449 L36.5918 23.9218 C30.7741 24.4228 25.4624 28.1265 23.2089 33.908 C21.7181 37.732 21.8341 41.7796 23.2262 45.3187 C20.2396 43.7051 16.8878 42.762 13.2496 42.762 C8.2291 42.762 3.6185 44.5082 0 47.426 L0 51.0806 C0.4542 59.9739 5.4178 68.4276 13.8617 72.981 L13.8642 72.981 Z"></path>
                        <path id="Vector_41" transform="translate(412.77484130859375, 153.42431640625)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 13.8642 72.981 C 26.7484 79.9292 42.8562 75.1694 49.8389 62.3489 C 51.9418 58.488 52.9785 54.3348 53.0476 50.2283 L 53.0895 47.6151 C 53.2598 45.2131 53.9386 42.8087 55.1653 40.554 C 57.4115 36.4279 61.0891 33.5691 65.2531 32.2502 C 70.9843 25.5157 74.43 16.0526 74.43 6.538 C 74.4029 5.1528 74.3017 3.7847 74.1338 2.4364 C 71.2682 0.8866 67.9681 0 64.478 0 C 59.6576 0 55.2468 1.6726 51.7715 4.4724 C 52.8156 7.6112 52.8131 11.116 51.5173 14.439 C 49.481 19.663 44.7864 23.0769 39.5685 23.7449 L 36.5918 23.9218 C 30.7741 24.4228 25.4624 28.1265 23.2089 33.908 C 21.7181 37.732 21.8341 41.7796 23.2262 45.3187 C 20.2396 43.7051 16.8878 42.762 13.2496 42.762 C 8.2291 42.762 3.6185 44.5082 0 47.426 L 0 51.0806 C 0.4542 59.9739 5.4178 68.4276 13.8617 72.981 L 13.8642 72.981 Z"></path></g>
                    <g id="cu_14" >                        <path id="Vector_42" transform="translate(412.77484130859375, 108)" fill="#fefbdb" d="M51.7715 49.8943 C52.8156 53.0331 52.8131 56.5379 51.5173 59.8609 C49.481 65.0849 44.7864 68.4988 39.5685 69.1668 L36.5918 69.3436 C30.7741 69.8447 25.4624 73.5484 23.2089 79.3299 C21.7181 83.1539 21.8341 87.2015 23.2262 90.7406 C20.2396 89.127 16.8878 88.1839 13.2496 88.1839 C8.2291 88.1839 3.6185 89.9301 0 92.8479 L0 23.8284 C0 10.6666 10.7221 0 23.9469 0 C34.2568 0 43.0462 6.4839 46.4203 15.5786 C61.0941 19.8104 72.1888 32.4196 74.1313 47.8607 C71.2657 46.311 67.9657 45.4243 64.4756 45.4243 C59.6551 45.4243 55.2443 47.0969 51.769 49.8968 L51.7715 49.8943 Z"></path>
                        <path id="Vector_43" transform="translate(412.77484130859375, 108)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 51.7715 49.8943 C 52.8156 53.0331 52.8131 56.5379 51.5173 59.8609 C 49.481 65.0849 44.7864 68.4988 39.5685 69.1668 L 36.5918 69.3436 C 30.7741 69.8447 25.4624 73.5484 23.2089 79.3299 C 21.7181 83.1539 21.8341 87.2015 23.2262 90.7406 C 20.2396 89.127 16.8878 88.1839 13.2496 88.1839 C 8.2291 88.1839 3.6185 89.9301 0 92.8479 L 0 23.8284 C 0 10.6666 10.7221 0 23.9469 0 C 34.2568 0 43.0462 6.4839 46.4203 15.5786 C 61.0941 19.8104 72.1888 32.4196 74.1313 47.8607 C 71.2657 46.311 67.9657 45.4243 64.4756 45.4243 C 59.6551 45.4243 55.2443 47.0969 51.769 49.8968 L 51.7715 49.8943 Z"></path></g>
                    <path id="Vector_44" transform="translate(436.0034637451172, 198.7430419921875)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 1.1033 2.8048 3.0088 5.2903 5.556 7.0881"></path>
                    <path id="Vector_45" transform="translate(412.7767562866211, 144.2386474609375)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0.0005 18.3662 C -0.0637 9.7897 5.8379 2.0115 14.5854 0"></path>
                    <path id="Vector_46" transform="translate(432.7676010131836, 123.233642578125)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 10.3814 34.56 C 19.7559 31.7061 25.033 21.8328 22.1649 12.5024 C 19.2968 3.1743 9.3744 -2.0767 0 0.7773"></path>
                    <path id="Vector_47" transform="translate(452.35562896728516, 123.57861328125)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 6.842 0 C 4.6724 1.0193 2.7299 2.5937 1.2465 4.6838 C 0.7627 5.3666 0.348 6.074 0 6.8034"></path>
                    <path id="Vector_48" transform="translate(432.7379913330078, 170.3857421875)" fill="none" stroke="#d1bd08" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 3.0359 2.9423 6.8543 5.1699 11.2453 6.3194 C 12.6769 6.6951 14.1134 6.9407 15.5401 7.0635"></path></g>
                <g id="ar-with-terminator_4">                    <path id="line_4" marker-end="url(#arrow)" transform="matrix(-1, 0, 0, 1, 576, 144)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 72 0"></path></g></g>
            <g id="g-1">                <g id="cu_15">                    <g id="cu_16" >                        <path id="Vector_49" transform="translate(329.0938491821289, 108)" fill="#edf4ff" d="M22.3598 49.8943 C21.3158 53.0331 21.3182 56.5379 22.6141 59.8609 C24.6504 65.0849 29.345 68.4988 34.5629 69.1668 L37.5396 69.3436 C43.3572 69.8447 48.6689 73.5484 50.9224 79.3299 C52.4132 83.1539 52.2972 87.2015 50.9051 90.7406 C53.8917 89.127 57.2436 88.1839 60.8818 88.1839 C65.9022 88.1839 70.5129 89.9301 74.1313 92.8479 L74.1313 23.8284 C74.1313 10.6666 63.4093 0 50.1844 0 C39.8745 0 31.0851 6.4839 27.711 15.5786 C13.0373 19.8104 1.9425 32.4196 0 47.8607 C2.8656 46.311 6.1657 45.4243 9.6558 45.4243 C14.4763 45.4243 18.887 47.0969 22.3623 49.8968 L22.3598 49.8943 Z"></path>
                        <path id="Vector_50" transform="translate(329.0938491821289, 108)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 22.3598 49.8943 C 21.3158 53.0331 21.3182 56.5379 22.6141 59.8609 C 24.6504 65.0849 29.345 68.4988 34.5629 69.1668 L 37.5396 69.3436 C 43.3572 69.8447 48.6689 73.5484 50.9224 79.3299 C 52.4132 83.1539 52.2972 87.2015 50.9051 90.7406 C 53.8917 89.127 57.2436 88.1839 60.8818 88.1839 C 65.9022 88.1839 70.5129 89.9301 74.1313 92.8479 L 74.1313 23.8284 C 74.1313 10.6666 63.4093 0 50.1844 0 C 39.8745 0 31.0851 6.4839 27.711 15.5786 C 13.0373 19.8104 1.9425 32.4196 0 47.8607 C 2.8656 46.311 6.1657 45.4243 9.6558 45.4243 C 14.4763 45.4243 18.887 47.0969 22.3623 49.8968 L 22.3598 49.8943 Z"></path></g>
                    <g id="cu_17" >                        <path id="Vector_51" transform="translate(328.792724609375, 153.42431640625)" fill="#edf4ff" d="M9.1794 32.2502 C13.3433 33.5691 17.021 36.4303 19.2671 40.554 C20.4963 42.8087 21.1726 45.2131 21.3429 47.6151 L21.3849 50.2283 C21.454 54.3348 22.4882 58.488 24.5936 62.3489 C31.5763 75.1694 47.6841 79.9292 60.5683 72.981 C69.0122 68.4276 73.9758 59.9739 74.43 51.0806 L74.43 47.426 C70.8116 44.5082 66.2009 42.762 61.1805 42.762 C57.5447 42.762 54.1904 43.7051 51.2038 45.3187 C52.5959 41.7796 52.7094 37.7296 51.2211 33.908 C48.9676 28.1265 43.6559 24.4228 37.8382 23.9218 L34.8615 23.7449 C29.6437 23.0769 24.949 19.663 22.9127 14.439 C21.6169 11.116 21.6144 7.6113 22.6585 4.4724 C19.1832 1.6726 14.7725 0 9.952 0 C6.4619 0 3.1618 0.8891 0.2962 2.4364 C0.1259 3.7823 0.0272 5.1503 0 6.538 C0 16.0551 3.4457 25.5157 9.1769 32.2526 L9.1794 32.2502 Z"></path>
                        <path id="Vector_52" transform="translate(328.792724609375, 153.42431640625)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 9.1794 32.2502 C 13.3433 33.5691 17.021 36.4303 19.2671 40.554 C 20.4963 42.8087 21.1726 45.2131 21.3429 47.6151 L 21.3849 50.2283 C 21.454 54.3348 22.4882 58.488 24.5936 62.3489 C 31.5763 75.1694 47.6841 79.9292 60.5683 72.981 C 69.0122 68.4276 73.9758 59.9739 74.43 51.0806 L 74.43 47.426 C 70.8115 44.5082 66.2009 42.762 61.1805 42.762 C 57.5447 42.762 54.1904 43.7051 51.2038 45.3187 C 52.5959 41.7796 52.7094 37.7296 51.2211 33.908 C 48.9676 28.1265 43.6559 24.4228 37.8382 23.9218 L 34.8615 23.7449 C 29.6437 23.0769 24.949 19.663 22.9127 14.439 C 21.6169 11.116 21.6144 7.6112 22.6585 4.4724 C 19.1832 1.6726 14.7725 0 9.952 0 C 6.4619 0 3.1618 0.8891 0.2962 2.4364 C 0.1259 3.7823 0.0272 5.1503 0 6.538 C 0 16.0551 3.4457 25.5157 9.1769 32.2526 L 9.1794 32.2502 Z"></path></g>
                    <path id="Vector_53" transform="translate(388.6378936767578, 144.2386474609375)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 14.5849 18.3662 C 14.649 9.7897 8.7475 2.0115 0 0"></path>
                    <path id="Vector_54" transform="translate(360.28639221191406, 123.233642578125)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 12.5645 34.56 C 3.1901 31.7061 -2.087 21.8328 0.7811 12.5024 C 3.6492 3.1743 13.5716 -2.0767 22.946 0.7773"></path>
                    <path id="Vector_55" transform="translate(356.8023910522461, 123.57861328125)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 C 2.1696 1.0193 4.1121 2.5937 5.5955 4.6838 C 6.0793 5.3666 6.494 6.074 6.842 6.8034"></path>
                    <path id="Vector_56" transform="translate(367.7219467163086, 170.3857421875)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 15.5401 0 C 12.5041 2.9423 8.6858 5.1699 4.2948 6.3194 C 2.8632 6.6951 1.4266 6.9407 0 7.0635"></path>
                    <path id="Vector_57" transform="translate(374.44051361083984, 198.7430419921875)" fill="none" stroke="#4987ec" stroke-width="2.4609317779541016" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 5.556 0 C 4.4527 2.8048 2.5472 5.2903 0 7.0881"></path></g>
                <g id="ar-with-terminator_5">                    <path id="line_5" marker-end="url(#arrow)" transform="translate(240, 144)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 72 0"></path></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 108, 0)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-add-7" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 84, 402)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-6" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 708, 295)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-5" fill="#1ac6ff33" transform="translate(84, 294)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-4" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 708, 186)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -8.326672684688674e-17, 8.326672684688674e-17, 1, 84, 186)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 708, 78)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, 5.551115123125783e-17, -5.551115123125783e-17, 1, 84, 78)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="ic-cc-1" transform="matrix(0.9999999403953552, 1.1102230246251565e-16, -1.1102230246251565e-16, 0.9999999403953552, 192, 126)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-3" transform="translate(192, 234)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 192, 342)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-6" transform="translate(588, 342)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 588, 234)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 588, 126)" fill="#33de7b1a" d="M0 0 L36 0 L36 36 L0 36 L0 0 Z"></path>
        <path id="tx-rc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 12, 108)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-3" transform="translate(12, 216)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-rc-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 12, 324)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 324)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-4" transform="translate(636, 216)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="tx-lc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 636, 108)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 564, 348)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 228, 348)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 564, 240)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 228, 240)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(0.9999999403953552, -5.551115123125783e-17, 5.551115123125783e-17, 0.9999999403953552, 564, 132)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 228, 132)" width="24" height="24" rx="0" ry="0"></rect></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>