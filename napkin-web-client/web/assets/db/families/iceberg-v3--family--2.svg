<svg xmlns="http://www.w3.org/2000/svg" width="672" height="504">    <g id="iceberg-v3--family--2">        <g id="lines">            <g id="common">                <g id="cu" data-entity-classes="Decor">                    <g id="cu_1" >                        <path id="fill" transform="translate(403.0001220703125, 247.435546875)" fill="#e8f9ff" d="M217.535 0 L228 14.6077 L228 26.7044 L211.714 55.4906 L211.714 85.0629 L184.571 123.444 L184.571 151.824 L157.429 185.408 L157.429 199.793 L114 256.566 L86.8571 213.386 L76 213.386 L54.2857 184.6 L54.2857 157.421 L32.5714 128.635 L32.5714 113.849 L10.8571 85.0629 L10.8571 60.2885 L0 45.8954 L0 14.5645 L10.8994 0 C11.2572 0.4243 11.6348 0.8141 12.0323 1.1925 C15.0433 3.9789 19.4455 5.3778 23.8179 5.3778 C28.1903 5.3778 32.5925 3.9903 35.6035 1.1925 C36.011 0.8141 36.3886 0.4243 36.7364 0 C37.0941 0.4243 37.4717 0.8141 37.8692 1.1925 C40.8802 3.9789 45.2824 5.3778 49.6548 5.3778 C54.0273 5.3778 58.4295 3.9903 61.4405 1.1925 C61.8379 0.8141 62.2156 0.4243 62.5634 0 C62.9211 0.4243 63.2988 0.8141 63.6963 1.1925 C66.7073 3.9789 71.1095 5.3778 75.4819 5.3778 C79.8543 5.3778 84.2565 3.9903 87.2675 1.1925 C87.665 0.8141 88.0426 0.4243 88.3904 0 C88.7482 0.4243 89.1258 0.8141 89.5233 1.1925 C92.5343 3.9789 96.9365 5.3778 101.309 5.3778 C105.681 5.3778 110.084 3.9903 113.095 1.1925 C113.492 0.8141 113.87 0.4243 114.217 0 C114.575 0.4243 114.953 0.8141 115.35 1.1925 C118.361 3.9789 122.763 5.3778 127.136 5.3778 C131.508 5.3778 135.91 3.9903 138.921 1.1925 C139.319 0.8141 139.697 0.4243 140.044 0 C140.402 0.4243 140.78 0.8141 141.177 1.1925 C144.188 3.9789 148.59 5.3778 152.963 5.3778 C157.335 5.3778 161.737 3.9903 164.748 1.1925 C165.156 0.8141 165.534 0.4243 165.881 0 C166.229 0.4243 166.607 0.8141 167.004 1.1925 C170.015 3.9789 174.408 5.3778 178.79 5.3778 C183.172 5.3778 187.565 3.9903 190.576 1.1925 C190.983 0.8141 191.361 0.4243 191.708 0 C192.056 0.4243 192.434 0.8141 192.831 1.1925 C195.842 3.9789 200.244 5.3778 204.617 5.3778 C208.989 5.3778 213.392 3.9903 216.402 1.1925 C216.81 0.8141 217.188 0.4243 217.535 0 Z"></path>
                        <path id="fill_1" transform="translate(403.0001220703125, 247.435546875)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="round" stroke-miterlimit="4"  d="M 217.5354 0 L 228 14.6077 L 228 26.7044 L 211.7143 55.4906 L 211.7143 85.0629 L 184.5714 123.4442 L 184.5714 151.8238 L 157.4286 185.4076 L 157.4286 199.7929 L 114 256.5657 L 86.8571 213.3864 L 76 213.3864 L 54.2857 184.6002 L 54.2857 157.4211 L 32.5714 128.6351 L 32.5714 113.8488 L 10.8571 85.0629 L 10.8571 60.2885 L 0 45.8954 L 0 14.5645 L 10.8994 0 C 11.2572 0.4243 11.6348 0.8141 12.0323 1.1925 C 15.0433 3.9789 19.4455 5.3778 23.8179 5.3778 C 28.1903 5.3778 32.5925 3.9903 35.6035 1.1925 C 36.011 0.8141 36.3886 0.4243 36.7364 0 C 37.0941 0.4243 37.4717 0.8141 37.8692 1.1925 C 40.8802 3.9789 45.2824 5.3778 49.6548 5.3778 C 54.0273 5.3778 58.4295 3.9903 61.4405 1.1925 C 61.8379 0.8141 62.2156 0.4243 62.5634 0 C 62.9211 0.4243 63.2988 0.8141 63.6963 1.1925 C 66.7073 3.9789 71.1095 5.3778 75.4819 5.3778 C 79.8543 5.3778 84.2565 3.9903 87.2675 1.1925 C 87.665 0.8141 88.0426 0.4243 88.3904 0 C 88.7482 0.4243 89.1258 0.8141 89.5233 1.1925 C 92.5343 3.9789 96.9365 5.3778 101.3089 5.3778 C 105.6813 5.3778 110.0835 3.9903 113.0945 1.1925 C 113.492 0.8141 113.8696 0.4243 114.2174 0 C 114.5751 0.4243 114.9528 0.8141 115.3503 1.1925 C 118.3613 3.9789 122.7635 5.3778 127.1359 5.3778 C 131.5083 5.3778 135.9105 3.9903 138.9215 1.1925 C 139.319 0.8141 139.6966 0.4243 140.0444 0 C 140.4021 0.4243 140.7798 0.8141 141.1773 1.1925 C 144.1883 3.9789 148.5905 5.3778 152.9629 5.3778 C 157.3353 5.3778 161.7375 3.9903 164.7485 1.1925 C 165.1559 0.8141 165.5335 0.4243 165.8813 0 C 166.2291 0.4243 166.6068 0.8141 167.0043 1.1925 C 170.0153 3.9789 174.4075 5.3778 178.7899 5.3778 C 183.1722 5.3778 187.5645 3.9903 190.5755 1.1925 C 190.983 0.8141 191.3605 0.4243 191.7083 0 C 192.0562 0.4243 192.4338 0.8141 192.8313 1.1925 C 195.8423 3.9789 200.2445 5.3778 204.6169 5.3778 C 208.9893 5.3778 213.3915 3.9903 216.4025 1.1925 C 216.8099 0.8141 217.1876 0.4243 217.5354 0 Z"></path></g>
                    <path id="line" transform="matrix(-1, 0, 0, 1, 468.1427001953125, 274.00390625)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 32.5714 0 L 54.2856 19.1906 L 65.1427 19.1906"></path>
                    <path id="line_1" transform="matrix(-1, 0, 0, 1, 614.7149658203125, 283.7353515625)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 19.1907 L 16.2857 0 L 38 0"></path>
                    <path id="line_2" transform="matrix(-1, 0, 0, 1, 521.2872314453125, 451.2265625)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 31.4286 9.5953 L 20.5714 9.5953 L 9.7143 0 L 0 0"></path>
                    <path id="line_3" transform="matrix(0.510276198387146, 0.86001056432724, 0.8510268926620483, -0.5251221656799316, 435.5723876953125, 361.2841796875)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 11.9686 1.3287 L 19.5715 13.7803"></path>
                    <path id="line_4" transform="matrix(0.920337438583374, -0.39112529158592224, -0.3782845437526703, -0.9256894588470459, 562.8399658203125, 391.169921875)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 6.3704 L 15.077 0 L 30.5735 9.0009"></path></g>
                <g id="cu_2" data-entity-classes="Decor">                    <g id="cu_3" >                        <path id="fill_2" transform="translate(413.9000701904297, 144)" fill="#f1f1f1" d="M136.101 28 L125.101 28 L103.101 0 L63.1012 51 L55.1007 42 L19.1012 90 L17.9242 90 L13.9247 85 L0 103.436 C0.3577 103.86 0.7345 104.25 1.132 104.628 C4.143 107.414 8.5452 108.813 12.9176 108.813 C17.29 108.813 21.6923 107.426 24.7033 104.628 C25.1107 104.25 25.4883 103.86 25.8361 103.436 C26.1938 103.86 26.5715 104.25 26.9689 104.628 C29.9799 107.414 34.3822 108.813 38.7546 108.813 C43.127 108.813 47.5292 107.426 50.5402 104.628 C50.9377 104.25 51.3153 103.86 51.6631 103.436 C52.0208 103.86 52.3985 104.25 52.796 104.628 C55.807 107.414 60.2092 108.813 64.5816 108.813 C68.954 108.813 73.3562 107.426 76.3672 104.628 C76.7647 104.25 77.1423 103.86 77.4901 103.436 C77.8479 103.86 78.2255 104.25 78.623 104.628 C81.634 107.414 86.0362 108.813 90.4086 108.813 C94.781 108.813 99.1832 107.426 102.194 104.628 C102.592 104.25 102.969 103.86 103.317 103.436 C103.675 103.86 104.052 104.25 104.45 104.628 C107.461 107.414 111.863 108.813 116.236 108.813 C120.608 108.813 125.01 107.426 128.021 104.628 C128.419 104.25 128.796 103.86 129.144 103.436 C129.502 103.86 129.88 104.25 130.277 104.628 C133.288 107.414 137.69 108.813 142.063 108.813 C146.435 108.813 150.837 107.426 153.848 104.628 C154.256 104.25 154.633 103.86 154.981 103.436 C155.329 103.86 155.706 104.25 156.104 104.628 C159.115 107.414 163.507 108.813 167.89 108.813 C172.272 108.813 176.664 107.426 179.675 104.628 C180.083 104.25 180.46 103.86 180.808 103.436 C181.156 103.86 181.533 104.25 181.931 104.628 C184.942 107.414 189.344 108.813 193.717 108.813 C198.089 108.813 202.491 107.426 205.502 104.628 C205.91 104.25 206.287 103.86 206.635 103.436 L180.303 69.1439 L173.101 77 L136.101 28 Z"></path>
                        <path id="fill_3" transform="translate(413.9000701904297, 144)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="symbol(figma.mixed)" stroke-linecap="round" stroke-miterlimit="4"  d="M 136.1011 28 L 125.1011 28 L 103.1011 0 L 63.1012 51 L 55.1007 42 L 19.1012 90 L 17.9242 90 L 13.9247 85 L 0 103.4356 C 0.3577 103.8598 0.7345 104.2497 1.132 104.6281 C 4.143 107.4144 8.5452 108.8133 12.9176 108.8133 C 17.29 108.8133 21.6923 107.4259 24.7033 104.6281 C 25.1107 104.2497 25.4883 103.8598 25.8361 103.4356 C 26.1938 103.8598 26.5715 104.2497 26.9689 104.6281 C 29.9799 107.4144 34.3822 108.8133 38.7546 108.8133 C 43.127 108.8133 47.5292 107.4259 50.5402 104.6281 C 50.9377 104.2497 51.3153 103.8598 51.6631 103.4356 C 52.0208 103.8598 52.3985 104.2497 52.796 104.6281 C 55.807 107.4144 60.2092 108.8133 64.5816 108.8133 C 68.954 108.8133 73.3562 107.4259 76.3672 104.6281 C 76.7647 104.2497 77.1423 103.8598 77.4901 103.4356 C 77.8479 103.8598 78.2255 104.2497 78.623 104.6281 C 81.634 107.4144 86.0362 108.8133 90.4086 108.8133 C 94.781 108.8133 99.1832 107.4259 102.1942 104.6281 C 102.5917 104.2497 102.9693 103.8598 103.3171 103.4356 C 103.6749 103.8598 104.0525 104.2497 104.45 104.6281 C 107.461 107.4144 111.8632 108.8133 116.2356 108.8133 C 120.608 108.8133 125.0102 107.4259 128.0212 104.6281 C 128.4187 104.2497 128.7963 103.8598 129.1441 103.4356 C 129.5019 103.8598 129.8795 104.2497 130.277 104.6281 C 133.288 107.4144 137.6902 108.8133 142.0626 108.8133 C 146.435 108.8133 150.8372 107.4259 153.8482 104.6281 C 154.2556 104.2497 154.6332 103.8598 154.981 103.4356 C 155.3288 103.8598 155.7065 104.2497 156.104 104.6281 C 159.115 107.4144 163.5072 108.8133 167.8896 108.8133 C 172.2719 108.8133 176.6642 107.4259 179.6752 104.6281 C 180.0827 104.2497 180.4603 103.8598 180.8081 103.4356 C 181.1559 103.8598 181.5335 104.2497 181.931 104.6281 C 184.942 107.4144 189.3442 108.8133 193.7166 108.8133 C 198.089 108.8133 202.4912 107.4259 205.5022 104.6281 C 205.9097 104.2497 206.2873 103.8598 206.6351 103.4356 L 180.3026 69.1439 L 173.1012 77 L 136.1011 28 Z"></path></g>
                    <path id="line_5" transform="matrix(-0.7071067690849304, -0.7071067690849304, -0.7071067690849304, 0.7071067690849304, 539.0016326904297, 172)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 11.3137 11.3137"></path>
                    <path id="line_6" transform="matrix(-0.7071067690849304, -0.7071067690849304, -0.7071067690849304, 0.7071067690849304, 448.9098358154297, 234)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0.0002 0.0002 L 11.2495 11.2495"></path>
                    <path id="line_7" transform="matrix(-1, -5.551115123125783e-17, -5.551115123125783e-17, 1, 587.0016326904297, 221)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 9 9.5"></path>
                    <path id="line_8" transform="matrix(-1, -5.551115123125783e-17, -5.551115123125783e-17, 1, 477.0016326904297, 195)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4"  d="M 0 0 L 10 13"></path></g>
                <g id="cu_4" data-entity-classes="Decor">                    <path id="line_9" transform="translate(0, 247.435546875)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 672.7422 0 C 672.3944 0.4243 672.0167 0.8141 671.6093 1.1925 C 668.5983 3.9903 664.206 5.3778 659.8237 5.3778 C 655.4413 5.3778 651.0491 3.9789 648.0381 1.1925 C 647.6406 0.8141 647.263 0.4243 646.9152 0 C 646.5674 0.4243 646.1897 0.8141 645.7823 1.1925 C 642.7713 3.9903 638.3691 5.3778 633.9967 5.3778 C 629.6243 5.3778 625.222 3.9789 622.2111 1.1925 C 621.8136 0.8141 621.436 0.4243 621.0782 0 C 620.7304 0.4243 620.3528 0.8141 619.9553 1.1925 C 616.9443 3.9903 612.5421 5.3778 608.1697 5.3778 C 603.7973 5.3778 599.395 3.9789 596.384 1.1925 C 595.9865 0.8141 595.609 0.4243 595.2512 0 C 594.9034 0.4243 594.5258 0.8141 594.1283 1.1925 C 591.1173 3.9903 586.7151 5.3778 582.3427 5.3778 C 577.9702 5.3778 573.5681 3.9789 570.5571 1.1925 C 570.1596 0.8141 569.7819 0.4243 569.4242 0 C 569.0764 0.4243 568.6988 0.8141 568.3013 1.1925 C 565.2903 3.9903 560.8881 5.3778 556.5157 5.3778 C 552.1433 5.3778 547.741 3.9789 544.73 1.1925 C 544.3326 0.8141 543.955 0.4243 543.5972 0 C 543.2494 0.4243 542.8718 0.8141 542.4743 1.1925 C 539.4633 3.9903 535.0611 5.3778 530.6887 5.3778 C 526.3163 5.3778 521.9141 3.9789 518.9031 1.1925 C 518.5056 0.8141 518.1279 0.4243 517.7702 0 C 517.4224 0.4243 517.0448 0.8141 516.6373 1.1925 C 513.6263 3.9903 509.2241 5.3778 504.8517 5.3778 C 500.4793 5.3778 496.0771 3.9789 493.0661 1.1925 C 492.6686 0.8141 492.291 0.4243 491.9333 0 C 491.5855 0.4243 491.2078 0.8141 490.8004 1.1925 C 487.7894 3.9903 483.0033 5.3862 478.6309 5.3862 C 474.2486 5.3862 469.7255 3.9789 466.7145 1.1925 C 466.317 0.8141 465.9395 0.4243 465.5817 0 C 465.2339 0.4243 464.8563 0.8141 464.4489 1.1925 C 461.4379 3.9903 457.0356 5.3778 452.6632 5.3778 C 448.2908 5.3778 443.8886 3.9789 440.8776 1.1925 C 440.4801 0.8141 440.1025 0.4243 439.7547 0 C 439.4069 0.4243 439.0293 0.8141 438.6219 1.1925 C 435.6109 3.9903 431.2186 5.3778 426.8362 5.3778 C 422.4539 5.3778 418.0616 3.9789 415.0506 1.1925 C 414.6531 0.8141 414.2755 0.4243 413.9277 0 C 413.5799 0.4243 413.2023 0.8141 412.7948 1.1925 C 409.7838 3.9903 405.3816 5.3778 401.0092 5.3778 C 396.6368 5.3778 392.2346 3.9789 389.2236 1.1925 C 388.8261 0.8141 388.4485 0.4243 388.0908 0 C 387.743 0.4243 387.3653 0.8141 386.9678 1.1925 C 383.9568 3.9903 379.5547 5.3778 375.1823 5.3778 C 370.8098 5.3778 366.4076 3.9789 363.3966 1.1925 C 362.9991 0.8141 362.6215 0.4243 362.2638 0 C 361.916 0.4243 361.5384 0.8141 361.1409 1.1925 C 358.1299 3.9903 353.7276 5.3778 349.3552 5.3778 C 344.9828 5.3778 340.5806 3.9789 337.5696 1.1925 C 337.1721 0.8141 336.7945 0.4243 336.4368 0 C 336.089 0.4243 335.7113 0.8141 335.3138 1.1925 C 332.3028 3.9903 327.9006 5.3778 323.5282 5.3778 C 319.1558 5.3778 314.7536 3.9789 311.7426 1.1925 C 311.3451 0.8141 310.9675 0.4243 310.6097 0 C 310.2619 0.4243 309.8843 0.8141 309.4868 1.1925 C 306.4758 3.9903 302.0736 5.3778 297.7012 5.3778 C 293.3288 5.3778 288.9266 3.9789 285.9156 1.1925 C 285.5181 0.8141 285.1405 0.4243 284.7827 0 C 284.4349 0.4243 284.0573 0.8141 283.6499 1.1925 C 280.6389 3.9903 276.2367 5.3778 271.8643 5.3778 C 267.4919 5.3778 263.0896 3.9789 260.0786 1.1925 C 259.6812 0.8141 259.3035 0.4243 258.9458 0 C 258.598 0.4243 258.2204 0.8141 257.813 1.1925 C 254.802 3.9903 250.0157 5.3862 245.6433 5.3862 C 241.2609 5.3862 236.7376 3.9789 233.7266 1.1925 C 233.3291 0.8141 232.9515 0.4243 232.5937 0 C 232.2459 0.4243 231.8683 0.8141 231.4609 1.1925 C 228.4499 3.9903 224.0477 5.3778 219.6752 5.3778 C 215.3028 5.3778 210.9006 3.9789 207.8896 1.1925 C 207.4921 0.8141 207.1145 0.4243 206.7667 0 C 206.4189 0.4243 206.0413 0.8141 205.6339 1.1925 C 202.6229 3.9903 198.2305 5.3778 193.8482 5.3778 C 189.4659 5.3778 185.0736 3.9789 182.0626 1.1925 C 181.6651 0.8141 181.2875 0.4243 180.9397 0 C 180.5919 0.4243 180.2143 0.8141 179.8068 1.1925 C 176.7958 3.9903 172.3936 5.3778 168.0212 5.3778 C 163.6488 5.3778 159.2466 3.9789 156.2356 1.1925 C 155.8381 0.8141 155.4605 0.4243 155.1028 0 C 154.755 0.4243 154.3773 0.8141 153.9798 1.1925 C 150.9688 3.9903 146.5666 5.3778 142.1942 5.3778 C 137.8218 5.3778 133.4196 3.9789 130.4086 1.1925 C 130.0111 0.8141 129.6335 0.4243 129.2758 0 C 128.9279 0.4243 128.5503 0.8141 128.1528 1.1925 C 125.1419 3.9903 120.7396 5.3778 116.3672 5.3778 C 111.9948 5.3778 107.5926 3.9789 104.5816 1.1925 C 104.1841 0.8141 103.8065 0.4243 103.4488 0 C 103.101 0.4243 102.7233 0.8141 102.3259 1.1925 C 99.3149 3.9903 94.9126 5.3778 90.5402 5.3778 C 86.1678 5.3778 81.7656 3.9789 78.7546 1.1925 C 78.3571 0.8141 77.9795 0.4243 77.6218 0 C 77.274 0.4243 76.8963 0.8141 76.4988 1.1925 C 73.4878 3.9903 69.0856 5.3778 64.7132 5.3778 C 60.3408 5.3778 55.9386 3.9789 52.9276 1.1925 C 52.5301 0.8141 52.1525 0.4243 51.7948 0 C 51.4469 0.4243 51.0693 0.8141 50.6619 1.1925 C 47.6509 3.9903 43.2487 5.3778 38.8763 5.3778 M 38.8763 5.3778 C 34.4939 5.3778 29.9709 3.9789 26.9599 1.1925 C 26.5624 0.8141 26.1848 0.4243 25.827 0 C 25.4792 0.4243 25.1016 0.8141 24.6942 1.1925 C 21.6832 3.9903 17.281 5.3778 12.9086 5.3778 C 8.5361 5.3778 4.1339 3.9789 1.1229 1.1925 C 0.7254 0.8141 0.3478 0.4243 0 0 M 38.8763 5.3778 L 38.8762 5.3862"></path></g>
                <g id="ar-with-terminator" data-entity-classes="LeaderLine">                    <path id="line_10" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 167.9960913658142)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 168 0"></path></g></g>
            <g id="g-1">                <g id="ar-with-terminator_1" data-entity-classes="LeaderLine">                    <path id="line_11" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="translate(348, 335.9960660934448)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0039 L 180 0"></path></g></g>
            <g id="g-2">                <g id="ar-with-terminator_2" data-entity-classes="LeaderLine">                    <path id="line_12" data-entity-classes="LeaderLine" marker-end="url(#arrow)" transform="matrix(1, 0, 0, -1, 348, 396.0038604736328)" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10"  d="M 0 0.0037 L 142 0"></path></g></g></g>
        <path id="tx-rc-2" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 372)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-1" transform="translate(24, 312)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="tx-rc-top" transform="matrix(1, -6.079792722388024e-17, 5.068409575331278e-17, 1, 24, 144)" fill="#ff00001a" d="M0 0 L252 0 L252 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 372)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 312)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-top" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 288, 144)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 384)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 324)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lc-under" transform="translate(24, 264)" fill="#ff00001a" d="M0 0 L252 0 L252 24 L0 24 L0 0 Z"></path>
        <path id="tx-lc-over" transform="translate(24, 216)" fill="#ff00001a" d="M0 0 L252 0 L252 24 L0 24 L0 0 Z"></path>
        <path id="tx-lb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 24, 72)" fill="#ff00001a" d="M0 0 L312 0 L312 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-add-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 414)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-2" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 354)" width="24" height="24" rx="0" ry="0"></rect>
        <rect id="bt-cc-add-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 138, 294)" width="24" height="24" rx="0" ry="0"></rect></g>
    <defs >        <marker id="arrow" viewBox="-13 -13 26 26" refX="0" refY="0" markerWidth="13" markerHeight="13" markerUnits="strokeWidth" orient="auto-start-reverse">            <path d="M -8 -6.5 L -1.5 0 L -8 6.5" stroke="#666666" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round"></path></marker></defs></svg>