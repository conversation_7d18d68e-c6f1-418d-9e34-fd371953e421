<svg xmlns="http://www.w3.org/2000/svg" width="1032" height="624">
    <g id="iceberg-grey-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L1032 0 L1032 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:1032;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:1032;h:0">
            <g id="body" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:144 36 72 36;gap:0;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:1032;h:576">
                <g id="top" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 36 0;gap:10;primary:MIN;counter:MIN" data-position="x:72;y:144;w:888;h:84" transform="translate(72, 144)">
                    <g id="row-top" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:0;w:888;h:48">
                        <path id="fill" transform="translate(376.984375, -120)" fill="#f6f6f6" d="M144.757 46.9121 L79.735 0 L56.78 35.6352 L25.6945 53.5456 L5.905 96 L5.9039 96 L0 108 L58.4439 108 L97.0154 78 L82.9727 17.1328 L135.616 55.1136 C142.371 67.0021 160.015 96 160.015 96 L178.178 108 L199.015 108 L167.515 86.5 L144.757 46.9121 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:376.984;y:-120;w:199.015;h:108"/>
                        <path id="fill_1" transform="translate(-12, -12)" fill="#f6f6f6" d="M355.55 0 L0 0 L0 72 L273.606 72 C274.389 71.2743 275.217 70.5854 276.088 69.9371 L284.519 63.6618 L294.258 46.6777 C297.677 40.7147 303.04 36.1066 309.45 33.6244 L345.964 19.4848 L355.55 0 Z M658.285 72 L630.982 29.5906 L619.614 0 L912 0 L912 72 L658.285 72 Z M367.714 43.2329 L320.283 61.6 L314.32 72 L492 72 L450 54 L456 36 L432 12 L447.429 0 L388.985 0 L367.714 43.2329 Z M604.086 43.2329 L588 0 L567.162 0 L577 6.5 L592.884 47.5363 C592.884 47.5363 603.3 64.2438 608.272 72 L622.372 72 L604.086 43.2329 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="fill_2" transform="translate(295.4375, 60)" fill="#f6f6f6" d="M322.56 24 L0 24 L6.8806 0 L184.561 0 L300.832 0 L314.932 0 L322.56 24 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:295.438;y:60;w:322.560;h:24"/>
                        <path id="stroke" transform="translate(376.984375, -120)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 108 L 5.9039 96 L 5.905 96 L 25.6945 53.5456 L 56.78 35.6352 L 79.735 0 L 144.7575 46.9121 L 167.5154 86.5 L 199.0154 108 M 58.4439 108 L 97.0154 78 L 82.9727 17.1328 L 135.6159 55.1136 C 142.3712 67.0021 160.0154 96 160.0154 96 L 178.1775 108" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:376.984;y:-120;w:199.015;h:108"/>
                        <path id="stroke_1" transform="translate(-12, -12)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 355.5504 0 L 0 0 L 0 72 L 273.6058 72 C 274.3893 71.2743 275.2171 70.5854 276.088 69.9371 L 284.5194 63.6618 L 294.2577 46.6777 C 297.6768 40.7147 303.0399 36.1066 309.4499 33.6244 L 345.9641 19.4848 L 355.5504 0 Z M 658.2852 72 L 630.9822 29.5906 L 619.6135 0 L 912 0 L 912 72 L 658.2852 72 Z M 588 0 L 604.0857 43.2329 L 622.3716 72 M 314.32 72 L 320.2831 61.6 L 367.7144 43.2329 L 388.9846 0 M 447.4286 0 L 432 12 L 456 36 L 450 54 L 492 72 L 608.2715 72 C 603.3 64.2438 592.884 47.5363 592.884 47.5363 L 577 6.5 L 567.1622 0" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="stroke_2" transform="translate(295.4375, 60)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 184.5605 0 L 300.8321 0 M 6.8806 0 L 0 24 L 322.56 24 L 314.9321 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:295.438;y:60;w:322.560;h:24"/>
                        <g id="text" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 60;gap:492;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:888;h:48;hMin:48">
                            <g id="text_1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MIN" data-position="x:60;y:0;w:168;h:48" transform="translate(60, 0)">
                                <g id="tx-lc-top" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#bcbcbc" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                </g>
                            </g>
                            <g id="text_2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:720;y:0;w:168;h:48" transform="translate(720, 0)">
                                <g id="tx-rc-top-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#bcbcbc" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                </g>
                            </g>
                            <g id="ic-cc-top" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                <rect id="Rectangle_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                                <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="g-0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:36;y:228;w:960;h:0.000" transform="translate(36, 228)">
                    <path id="Vector" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" stroke-dasharray="6 6" d="M 332 0 L 0 0 M 960 0 L 654 0" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:960;h:0.000"/>
                </g>
                <g id="bottom" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:36 0 0 0;gap:48;primary:MIN;counter:MIN" data-position="x:72;y:228.000;w:888;h:276" transform="translate(72, 228.00003051757812)">
                    <g id="row-1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:36;w:888;h:48" transform="translate(0, 36)">
                        <path id="fill_3" transform="translate(307.08203125, 60)" fill="#e8f9ff" d="M4.1973 12.0002 L286.816 12.0002 L297.783 0 L0 0 L4.1973 12.0002 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:307.082;y:60;w:297.783;h:12.000"/>
                        <path id="fill_4" transform="translate(281.8984375, -36)" fill="#e8f9ff" d="M336.101 0 L13.3212 0 L0 24 L343.926 24 L336.101 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:281.898;y:-36;w:343.926;h:24"/>
                        <path id="fill_5" transform="translate(-12, -12)" fill="#e8f9ff" d="M264.065 0 L0 0 L0 72 L286.195 72 L265.429 7.1523 C264.674 4.7941 264.224 2.3937 264.065 0 Z M656.971 72 L678.456 47.5484 C687.3 37.4829 688.398 22.773 681.144 11.5069 L673.736 0 L912 0 L912 72 L656.971 72 Z M637.826 0.0002 L655.92 27.7464 L616.866 72 L319.083 72 L293.899 0.0002 L637.826 0.0002 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="stroke_3" transform="translate(307.08203125, 60)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 297.7832 0 L 286.8159 12.0002 L 4.1973 12.0002 L 0 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:307.082;y:60;w:297.783;h:12.000"/>
                        <path id="stroke_4" transform="translate(281.8984375, -36)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 24 L 13.3212 0 L 336.1009 0 L 343.9265 24" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:281.898;y:-36;w:343.926;h:24"/>
                        <path id="stroke_5" transform="translate(-12, -12)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 264.0645 0 L 0 0 L 0 72 L 286.1955 72 L 265.4291 7.1523 C 264.674 4.7941 264.2244 2.3937 264.0645 0 Z M 656.9711 72 L 678.4561 47.5484 C 687.3004 37.4829 688.3976 22.773 681.1445 11.5069 L 673.7364 0 L 912 0 L 912 72 L 656.9711 72 Z M 293.8991 0.0002 L 319.0827 72 M 637.8256 0.0002 L 655.92 27.7464 L 616.8658 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:252;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:888;h:48;hMin:48">
                            <g id="text_3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:168;h:48">
                                <g id="tx-lc-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                </g>
                            </g>
                            <g id="ic-cc-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:420;y:0;w:48;h:48" transform="translate(420, 0)">
                                <rect id="Rectangle_7_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                                <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <g id="text_4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:720;y:0;w:168;h:48" transform="translate(720, 0)">
                                <g id="tx-rc-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#17aee1" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 12)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 432, 60)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432;y:-36;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 432, -36)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="row-2" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:132;w:888;h:48" transform="translate(0, 132)">
                        <path id="fill_6" transform="translate(311.22265625, -24)" fill="#edf4ff" d="M282.884 0 L272.278 11.9997 L4.4655 11.9997 L0 0 L282.884 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:311.223;y:-24;w:282.884;h:12.000"/>
                        <path id="fill_7" transform="translate(-12, -12)" fill="#edf4ff" d="M295.734 0 L0 0 L0 72 L317.413 72 L312.747 45.8616 L295.734 0 Z M599.386 72 L603.34 61.4328 C603.375 61.3399 603.409 61.2469 603.443 61.1536 L617.76 21.7068 L628.878 7.3261 L635.573 0 L912 0 L912 72 L599.386 72 Z M595.5 0 L575.243 50.9192 L567.355 72 L347.911 72 L341.808 37.9462 L327.687 0 L595.5 0 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="fill_8" transform="translate(335.91015625, 60)" fill="#edf4ff" d="M214.953 12.0003 L2.1504 12.0003 L0 0 L219.444 0 L214.953 12.0003 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:335.910;y:60;w:219.444;h:12.000"/>
                        <path id="stroke_6" transform="translate(311.22265625, -24)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 272.2782 11.9997 L 282.8842 0 L 0 0 L 4.4655 11.9997" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:311.223;y:-24;w:282.884;h:12.000"/>
                        <path id="stroke_7" transform="translate(-12, -12)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 295.7337 0 L 0 0 L 0 72 L 317.4129 72 L 312.7471 45.8616 L 295.7337 0 Z M 599.386 72 L 603.3403 61.4328 C 603.3751 61.3399 603.4094 61.2469 603.4432 61.1536 L 617.7603 21.7068 L 628.8775 7.3261 L 635.5731 0 L 912 0 L 912 72 L 599.386 72 Z M 595.5 0 L 575.2432 50.9192 L 567.3546 72 M 327.6873 0 L 341.8083 37.9462 L 347.9107 72" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="stroke_8" transform="translate(335.91015625, 60)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 2.1504 12.0003 L 214.9533 12.0003 L 219.4439 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:335.910;y:60;w:219.444;h:12.000"/>
                        <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:252;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:888;h:48;hMin:48">
                            <g id="text_5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:168;h:48">
                                <g id="tx-lc-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                </g>
                            </g>
                            <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:420;y:0;w:48;h:48" transform="translate(420, 0)">
                                <rect id="Rectangle_7_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                                <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <g id="text_6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:720;y:0;w:168;h:48" transform="translate(720, 0)">
                                <g id="tx-rc-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#4987ec" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 12)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 432, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="row-3" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:0;primary:MIN;counter:MAX" data-position="x:0;y:228;w:888;h:48" transform="translate(0, 228)">
                        <path id="fill_9" transform="translate(338.03125, -24)" fill="#f3f0ff" d="M213.088 0 L211.313 12.1203 L1.9707 12 L0 0 L213.088 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:338.031;y:-24;w:213.088;h:12.120"/>
                        <path id="fill_10" transform="translate(-12, -12)" fill="#f3f0ff" d="M321.449 0 L0 0 L0 72 L387.508 72 L333.538 25.2315 C327.428 20.4672 323.399 13.5241 322.294 5.8561 L321.449 0 Z M516.312 72 L578.434 24.7755 C585.434 19.9225 590.042 12.3225 591.107 3.8713 L591.595 0 L912 0 L912 72 L516.312 72 Z M561.342 0.1203 L490.12 57.813 L479.046 72 L430.582 72 L352 0 L561.342 0.1203 Z" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72"/>
                        <path id="fill_11" transform="translate(418.58203125, 60)" fill="#f3f0ff" d="M27.097 24 L0 0 L48.4641 0 L27.097 24 Z" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:418.582;y:60;w:48.464;h:24.000"/>
                        <path id="stroke_9" transform="translate(338.03125, -24)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 211.313 12.1203 L 213.0878 0 L 0 0 L 1.9707 12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:338.031;y:-24;w:213.088;h:12.120"/>
                        <g id="cu_stroke" data-constraints="horizontal:MIN;vertical:STRETCH;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-12;y:-12;w:912;h:72">
                            <path id="stroke_10" transform="translate(-12, -12)" fill="#f3f0ff" d="M321.449 0 L0 0 L0 72 L387.508 72 L333.538 25.2315 C327.428 20.4672 323.399 13.5241 322.294 5.8561 L321.449 0 Z M516.312 72 L578.434 24.7755 C585.434 19.9225 590.042 12.3225 591.107 3.8713 L591.595 0 L912 0 L912 72 L516.312 72 Z"/>
                            <path id="stroke_11" transform="translate(-12, -12)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 321.4492 0 L 0 0 L 0 72 L 387.5078 72 L 333.5375 25.2315 C 327.4283 20.4672 323.3991 13.5241 322.2935 5.856 L 321.4492 0 Z M 516.3123 72 L 578.4341 24.7755 C 585.4344 19.9225 590.0419 12.3225 591.1069 3.8713 L 591.5947 0 L 912 0 L 912 72 L 516.3123 72 Z M 352 0 L 430.5818 72 M 561.3423 0.1203 L 490.1201 57.813 L 479.0458 72"/>
                        </g>
                        <path id="stroke_12" transform="translate(418.58203125, 60)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="round" stroke-miterlimit="4" d="M 0 0 L 27.097 24 L 48.4641 0" data-constraints="horizontal:MIN;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:418.582;y:60;w:48.464;h:24.000"/>
                        <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:252;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:888;h:48;hMin:48">
                            <g id="text_7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MIN" data-position="x:0;y:0;w:168;h:48">
                                <g id="tx-lc-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:CENTER"/>
                                </g>
                            </g>
                            <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:420;y:0;w:48;h:48" transform="translate(420, 0)">
                                <rect id="Rectangle_7_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a" width="48" height="48" rx="0" ry="0"/>
                                <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                                    <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                                </g>
                            </g>
                            <g id="text_8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:10;primary:CENTER;counter:MAX" data-position="x:720;y:0;w:168;h:48" transform="translate(720, 0)">
                                <g id="tx-rc-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:168;h:48" fill="#ff00001a">
                                    <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:168;h:48" fill="#7e62ec" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:CENTER"/>
                                </g>
                            </g>
                        </g>
                        <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:CENTER;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:12;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 12)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 432, 60)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>