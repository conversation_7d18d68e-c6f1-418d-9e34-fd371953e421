<svg xmlns="http://www.w3.org/2000/svg" width="996" height="573">
    <g id="cycle-wheel-v1--family--8" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L996 0 L996 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:996;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:996;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:996;h:525">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:241.225;y:6;w:512.981;h:512.981" transform="translate(241.224853515625, 6)">
                    <g id="g-8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66.006;y:0.322;w:211.879;h:176.665" transform="translate(66.006103515625, 0.322265625)">
                        <g id="cu_Vector" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:211.879;h:176.665">
                            <path id="Vector" fill="#e8f9ff" d="M 211.8786 67.8438 L 179.1138 132.1807 C 145.2137 135.2499 115.2357 151.9083 94.6704 176.6652 L 72.3111 107.8981 L 0 84.3864 C 44.2034 35.4017 107.034 3.5515 177.3279 0 L 211.8786 67.8438 Z"/>
                            <path id="Vector_1" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 211.8786 67.8438 L 179.1138 132.1807 C 145.2137 135.2499 115.2357 151.9083 94.6704 176.6652 L 72.3111 107.8981 L 0 84.3864 C 44.2034 35.4017 107.034 3.5515 177.3279 0 L 211.8786 67.8438 Z"/>
                        </g>
                    </g>
                    <g id="g-7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:84.708;w:160.677;h:184.964" transform="translate(0, 84.70849609375)">
                        <g id="cu_Vector_1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:160.677;h:184.964">
                            <path id="Vector_2" fill="#edf4ff" d="M 138.3173 23.5116 L 160.6766 92.2788 C 139.6836 117.5506 129.5003 150.7383 132.5263 183.4099 L 67.9509 150.5236 L 0.3234 184.9642 C -3.0938 117.462 20.6925 50.2151 66.0061 0 L 138.3173 23.5116 Z"/>
                            <path id="Vector_3" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 138.3173 23.5116 L 160.6766 92.2788 C 139.6836 117.5506 129.5003 150.7383 132.5263 183.4099 L 67.9509 150.5236 L 0.3234 184.9642 C -3.0938 117.462 20.6925 50.2151 66.0061 0 L 138.3173 23.5116 Z"/>
                        </g>
                    </g>
                    <g id="g-6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0.323;y:235.232;w:176.721;h:211.624" transform="translate(0.3233642578125, 235.23193359375)">
                        <g id="cu_Vector_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:176.721;h:211.624">
                            <path id="Vector_4" fill="#f3f0ff" d="M 67.6276 0 L 132.2029 32.8862 C 135.3354 66.7067 151.9953 96.6051 176.7209 117.1196 L 107.6819 139.5673 L 84.2532 211.6236 C 35.3492 167.4319 3.5548 104.6609 0 34.4406 L 67.6276 0 Z"/>
                            <path id="Vector_5" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 67.6276 0 L 132.2029 32.8862 C 135.3354 66.7067 151.9953 96.6051 176.7209 117.1196 L 107.6819 139.5673 L 84.2532 211.6236 C 35.3492 167.4319 3.5548 104.6609 0 34.4406 L 67.6276 0 Z"/>
                        </g>
                    </g>
                    <g id="g-5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:84.577;y:352.352;w:184.819;h:160.630" transform="translate(84.5765380859375, 352.3515625)">
                        <g id="cu_Vector_3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:184.819;h:160.630">
                            <path id="Vector_6" fill="#faf0ff" d="M 150.4403 92.8141 L 183.3885 28.117 C 150.79 31.0959 117.6923 20.9284 92.4678 0 L 23.4288 22.4477 L 0 94.5039 C 50.1591 139.8298 117.3452 163.6631 184.8188 160.3198 L 150.4403 92.8141 Z"/>
                            <path id="Vector_7" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 150.4403 92.8141 L 183.3885 28.117 C 150.79 31.0959 117.6923 20.9284 92.4678 0 L 23.4288 22.4477 L 0 94.5039 C 50.1591 139.8298 117.3452 163.6631 184.8188 160.3198 L 150.4403 92.8141 Z"/>
                        </g>
                    </g>
                    <g id="g-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:235.017;y:336.161;w:211.695;h:176.511" transform="translate(235.016845703125, 336.16064453125)">
                        <g id="cu_Vector_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:211.695;h:176.511">
                            <path id="Vector_8" fill="#feecf7" d="M 0 109.005 L 32.9482 44.308 C 66.7293 41.221 96.6083 24.6394 117.1484 0 L 139.5675 68.9507 L 211.695 92.4027 C 167.4528 141.2809 104.6347 173.0294 34.3785 176.5107 L 0 109.005 Z"/>
                            <path id="Vector_9" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0 109.005 L 32.9482 44.308 C 66.7293 41.221 96.6083 24.6394 117.1484 0 L 139.5675 68.9507 L 211.695 92.4027 C 167.4528 141.2809 104.6347 173.0294 34.3785 176.5107 L 0 109.005 Z"/>
                        </g>
                    </g>
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:352.165;y:243.611;w:160.816;h:184.952" transform="translate(352.165283203125, 243.611328125)">
                        <g id="cu_Vector_5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:160.816;h:184.952">
                            <path id="Vector_10" fill="#ffedeb" d="M 22.419 161.5002 L 0 92.5494 C 20.9949 67.3644 31.2394 34.2791 28.3267 1.6617 L 92.7856 34.4886 L 160.5075 0 C 163.8474 67.5386 139.9574 134.7828 94.5465 184.9521 L 22.419 161.5002 Z"/>
                            <path id="Vector_11" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 22.419 161.5002 L 0 92.5494 C 20.9949 67.3644 31.2394 34.2791 28.3267 1.6617 L 92.7856 34.4886 L 160.5075 0 C 163.8474 67.5386 139.9574 134.7828 94.5465 184.9521 L 22.419 161.5002 Z"/>
                        </g>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:336.218;y:66.150;w:176.455;h:211.950" transform="translate(336.2176513671875, 66.14990234375)">
                        <g id="cu_Vector_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:176.455;h:211.950">
                            <path id="Vector_12" fill="#fef2e6" d="M 108.7333 211.9502 L 44.2744 179.1233 C 41.2506 145.2627 24.6706 115.3041 0 94.7132 L 68.6791 72.3826 L 92.2139 0 C 141.1729 44.254 172.9773 107.1318 176.4552 177.4616 L 108.7333 211.9502 Z"/>
                            <path id="Vector_13" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 108.7333 211.9502 L 44.2744 179.1233 C 41.2506 145.2627 24.6706 115.3041 0 94.7132 L 68.6791 72.3826 L 92.2139 0 C 141.1729 44.254 172.9773 107.1318 176.4552 177.4616 L 108.7333 211.9502 Z"/>
                        </g>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:243.334;y:0;w:185.097;h:160.863" transform="translate(243.3341064453125, 0)">
                        <g id="cu_Vector_7" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:185.097;h:160.863">
                            <path id="Vector_14" fill="#fefbdb" d="M 34.5507 68.1659 L 1.7859 132.5028 C 34.4763 129.543 67.6515 139.8036 92.8835 160.8631 L 161.5626 138.5324 L 185.0975 66.1498 C 134.8721 20.7512 67.5671 -3.0917 0 0.3221 L 34.5507 68.1659 Z"/>
                            <path id="Vector_15" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 34.5507 68.1659 L 1.7859 132.5028 C 34.4763 129.543 67.6515 139.8036 92.8835 160.8631 L 161.5626 138.5324 L 185.0975 66.1498 C 134.8721 20.7512 67.5671 -3.0917 0 0.3221 L 34.5507 68.1659 Z"/>
                        </g>
                    </g>
                    <g id="g-0" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:142.775;y:142.991;w:227;h:227" transform="translate(142.775146484375, 142.99072265625)">
                        <ellipse id="Ellipse_598" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:227;h:227" stroke="#bcbcbc" fill="#f6f6f6" stroke-width="2" stroke-linejoin="round" cx="113.5" cy="113.5" rx="113.5" ry="113.5"/>
                    </g>
                    <rect id="bt-cc-add-1" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:284.775;y:56;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 284.775390625, 56)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:432.775;y:265;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 432.775390625, 265)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-9" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:239.775;y:56;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 239.775390625, 56)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:392.775;y:125;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 392.775390625, 125)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:362.775;y:393;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 362.775390625, 393)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-5" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:223.775;y:432.010;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 223.775390625, 432.009765625)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-6" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:95.775;y:361.010;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 95.775390625, 361.009765625)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-7" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:54.775;y:223.010;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 54.775390625, 223.009765625)" width="24" height="24" rx="0" ry="0"/>
                    <rect id="bt-cc-add-8" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:124.775;y:97.010;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 124.775390625, 97.009765625)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:708;y:35.991;w:264;h:60" transform="translate(708, 35.99072265625)">
                    <g id="tx-lt-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:264;h:24" fill="#ff00001a">
                        <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#d1bd08" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:264;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:768;y:167.991;w:204;h:60" transform="translate(768, 167.99072265625)">
                    <g id="tx-lt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:204;h:24" fill="#ff00001a">
                        <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:204;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:768;y:299.991;w:204;h:60" transform="translate(768, 299.99072265625)">
                    <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:204;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:204;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-4" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:708;y:431.991;w:264;h:60" transform="translate(708, 431.99072265625)">
                    <g id="tx-lt-4" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:264;h:24" fill="#ff00001a">
                        <text id="Label_6" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#d95da7" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <g id="tx-lt-4-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:264;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_7" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:-24;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, -24, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-5" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:24;y:431.991;w:264;h:60" transform="translate(24, 431.99072265625)">
                    <g id="tx-rt-5" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:264;h:24" fill="#ff00001a">
                        <text id="Label_8" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#b960e2" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-5-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:264;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_9" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-5" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:264;y:0;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 264, 0)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-6" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:24;y:299.991;w:204;h:60" transform="translate(24, 299.99072265625)">
                    <g id="tx-rt-6" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:204;h:24" fill="#ff00001a">
                        <text id="Label_10" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#7e62ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-6-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:204;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_11" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <rect id="bt-cc-remove-6" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:0.010;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 204, 0.009765625)" width="24" height="24" rx="0" ry="0"/>
                </g>
                <g id="text-8" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:24;y:35.991;w:264;h:60" transform="translate(24, 35.99072265625)">
                    <g id="tx-rt-8" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:264;h:24" fill="#ff00001a">
                        <text id="Label_12" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#17aee1" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-8-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:264;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_13" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:264;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <path id="bt-cc-remove-8" transform="translate(264, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:264;y:0;w:24;h:24"/>
                </g>
                <g id="text-7" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:24;y:167.991;w:204;h:60" transform="translate(24, 167.99072265625)">
                    <g id="tx-rt-7" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:204;h:24" fill="#ff00001a">
                        <text id="Label_14" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#4987ec" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <g id="tx-rt-7-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:204;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_15" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:204;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                    </g>
                    <path id="bt-cc-remove-7" transform="translate(204, 0)" fill="#1ac6ff33" d="M 24 0 L 0 0 L 0 24 L 24 24 L 24 0 Z" data-constraints="horizontal:MAX;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:204;y:0;w:24;h:24"/>
                </g>
                <g id="text-0" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:407;y:232;w:180;h:60" transform="translate(407, 232)">
                    <g id="tx-ct-0" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:180;h:24" fill="#ff00001a">
                        <text id="Label_16" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <g id="tx-ct-0-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:180;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_17" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:180;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                </g>
                <g id="ic-cc-1" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:560;y:65.991;w:48;h:48" fill="#33de7b1a" transform="translate(560, 65.99072265625)">
                    <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#d1bd08" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-2" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:649;y:173.991;w:48;h:48" fill="#33de7b1a" transform="translate(649, 173.99072265625)">
                    <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-3" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:644;y:320.991;w:48;h:48" fill="#33de7b1a" transform="translate(644, 320.99072265625)">
                    <g id="icon_4" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_5" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-4" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:536;y:416.991;w:48;h:48" fill="#33de7b1a" transform="translate(536, 416.99072265625)">
                    <g id="icon_6" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_7" transform="translate(9, 5)" fill="none" stroke="#d95da7" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-5" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:391;y:407.991;w:48;h:48" fill="#33de7b1a" transform="translate(391, 407.99072265625)">
                    <g id="icon_8" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_9" transform="translate(9, 5)" fill="none" stroke="#b960e2" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-8" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:409;y:58.991;w:48;h:48" fill="#33de7b1a" transform="translate(409, 58.99072265625)">
                    <g id="icon_10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_11" transform="translate(9, 5)" fill="none" stroke="#17aee1" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-6" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:296;y:305.991;w:48;h:48" fill="#33de7b1a" transform="translate(296, 305.99072265625)">
                    <g id="icon_12" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_13" transform="translate(9, 5)" fill="none" stroke="#7e62ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
                <g id="ic-cc-7" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:301;y:157.991;w:48;h:48" fill="#33de7b1a" transform="translate(301, 157.99072265625)">
                    <g id="icon_14" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                        <path id="icon_15" transform="translate(9, 5)" fill="none" stroke="#4987ec" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>