<svg xmlns="http://www.w3.org/2000/svg" width="744" height="612">    <g id="diverge2-v4--family--10">        <g id="lines">            <g id="g-10">                <ellipse id="cr-small" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999949288467, 0, 0, 0.9999999949288467, 284.00003082773765, 127.99994141518255)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-9">                <ellipse id="cr-small_1" stroke="#b960e2" fill="#faf0ff" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999903312897, -1.3186436831347237e-8, 1.3186436831347237e-8, 0.9999999903312897, 200.00008709532995, 188.9999025370962)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-8">                <ellipse id="cr-small_2" stroke="#4987ec" fill="#edf4ff" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000000000056, 0, 0, 1.0000000000000056, 168.00220203399618, 287.99998251946545)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-7">                <ellipse id="cr-small_3" stroke="#d95da7" fill="#feecf7" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.00000000351773, 0, 0, 1.00000000351773, 200.00223354719105, 386.9999558231484)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-6">                <ellipse id="cr-small_4" stroke="#93c332" fill="#f2fae1" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000303356904, -1.4308287887843107e-9, 1.4308287887843107e-9, 1.0000000303356904, 284.0021464133897, 447.9999398085929)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-5">                <ellipse id="cr-small_5" stroke="#db8333" fill="#fef2e6" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999842885732, 9.924844945885525e-9, -9.924844945885525e-9, 0.9999999842885732, 387.99996301388774, 448.00001747519667)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-4">                <ellipse id="cr-small_6" stroke="#17aee1" fill="#e8f9ff" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.0000000121795871, 2.184830449891706e-8, -2.184830449891706e-8, 1.0000000121795871, 472.0001063013276, 386.9999739845023)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-3">                <ellipse id="cr-small_7" stroke="#df5e59" fill="#ffedeb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1, -1.7763568394002505e-15, 1.7763568394002505e-15, 1, 503.9999840266221, 287.9999930851943)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-2">                <ellipse id="cr-small_8" stroke="#3cc583" fill="#e7fbf2" stroke-width="2" stroke-linejoin="miter"  transform="matrix(0.9999999948558873, -2.184827607720763e-8, 2.184827607720763e-8, 0.9999999948558873, 472.0000825181935, 188.9999495949619)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-1">                <ellipse id="cr-small_9" stroke="#d1bd08" fill="#fefbdb" stroke-width="2" stroke-linejoin="miter"  transform="matrix(1.00000002255711, 8.49399839353282e-9, -8.49399839353282e-9, 1.00000002255711, 388.0000568746509, 127.99993529348649)" cx="36" cy="36" rx="36" ry="36"></ellipse></g>
            <g id="g-0">                <g id="cu" >                    <path id="Union" transform="matrix(1.0000000596046377, 0, 0, 1.0000000596046377, 260.0023435356769, 217.36723582353653)" fill="#f6f6f6" d="M77.476 0 L69.53 17.2658 L76.2873 15.0703 L84.4211 40.1037 C73.9203 44.4616 64.6739 51.2332 57.3743 59.7263 L36.0894 44.262 L40.3076 38.4561 L21.4313 40.6778 L25.1515 59.3167 L29.3277 53.5686 L50.5939 69.0194 C44.8294 78.4102 41.1587 89.2247 40.2318 100.813 L13.9653 100.813 L13.9653 93.6365 L0 106.529 L13.9653 119.422 L13.9653 112.317 L40.2211 112.317 C41.1268 123.915 44.7809 134.742 50.5326 144.146 L29.3065 159.568 L25.0883 153.762 L21.3681 172.401 L40.2444 174.622 L36.0681 168.874 L57.2978 153.45 C64.5949 161.968 73.8471 168.762 84.359 173.136 L76.2529 198.084 L69.4277 195.867 L77.3737 213.132 L93.9508 203.835 L87.1935 201.639 L95.3011 176.686 C100.66 177.959 106.251 178.633 112 178.633 C117.769 178.633 123.38 177.954 128.757 176.673 L136.873 201.65 L130.048 203.868 L146.625 213.166 L154.571 195.9 L147.813 198.095 L139.696 173.113 C150.192 168.735 159.43 161.944 166.717 153.433 L188.011 168.904 L183.793 174.709 L202.669 172.488 L198.949 153.849 L194.773 159.597 L173.479 144.126 C179.218 134.736 182.866 123.929 183.776 112.353 L210.135 112.353 L210.135 119.529 L224.101 106.637 L210.135 93.7441 L210.135 100.849 L183.771 100.849 C182.852 89.2808 179.198 78.4826 173.456 69.1009 L194.794 53.5979 L199.012 59.4037 L202.733 40.7648 L183.856 38.5431 L188.033 44.2912 L166.688 59.7989 C159.407 51.3048 150.181 44.5266 139.701 40.1545 L147.848 15.0815 L154.673 17.2992 L146.727 0.0333 L130.15 9.3311 L136.907 11.5267 L128.762 36.5942 C123.384 35.3118 117.771 34.6328 112 34.6328 C106.275 34.6328 100.706 35.301 95.3665 36.5637 L87.2279 11.5155 L94.0531 9.2978 L77.476 0 Z"></path>
                    <path id="Union_1" transform="matrix(1.0000000596046377, 0, 0, 1.0000000596046377, 260.0023435356769, 217.36723582353653)" fill="none" stroke="#bcbcbc" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4"  d="M 77.476 0 L 69.53 17.2659 L 76.2873 15.0703 L 84.4212 40.1037 C 73.9203 44.4616 64.6739 51.2332 57.3743 59.7264 L 36.0894 44.262 L 40.3076 38.4561 L 21.4313 40.6778 L 25.1515 59.3167 L 29.3277 53.5686 L 50.594 69.0194 C 44.8295 78.4102 41.1587 89.2248 40.2318 100.813 L 13.9653 100.813 L 13.9653 93.6365 L 0 106.5291 L 13.9653 119.4216 L 13.9653 112.3166 L 40.2211 112.3166 C 41.1268 123.915 44.7809 134.7417 50.5326 144.146 L 29.3065 159.5677 L 25.0883 153.7619 L 21.3681 172.4008 L 40.2444 174.6225 L 36.0681 168.8743 L 57.2978 153.4501 C 64.595 161.9682 73.8472 168.7619 84.359 173.136 L 76.2529 198.0842 L 69.4277 195.8666 L 77.3737 213.1324 L 93.9508 203.8346 L 87.1935 201.639 L 95.3011 176.6863 C 100.6603 177.9591 106.2515 178.6328 112 178.6328 C 117.7693 178.6328 123.3801 177.9542 128.757 176.6725 L 136.8727 201.6501 L 130.0475 203.8678 L 146.6246 213.1656 L 154.5706 195.8998 L 147.8133 198.0953 L 139.6961 173.1131 C 150.1924 168.7352 159.4305 161.944 166.7172 153.4326 L 188.0112 168.9037 L 183.7931 174.7095 L 202.6693 172.4878 L 198.9492 153.8489 L 194.7729 159.597 L 173.4794 144.1264 C 179.2182 134.7364 182.8662 123.9295 183.7761 112.3528 L 210.1353 112.3528 L 210.1353 119.5292 L 224.1006 106.6367 L 210.1353 93.7441 L 210.1353 100.8492 L 183.7711 100.8492 C 182.8516 89.2808 179.1978 78.4826 173.4561 69.1009 L 194.7942 53.5979 L 199.0124 59.4037 L 202.7326 40.7648 L 183.8563 38.5431 L 188.0325 44.2912 L 166.688 59.7989 C 159.407 51.3048 150.1813 44.5266 139.701 40.1545 L 147.8477 15.0815 L 154.6729 17.2992 L 146.7269 0.0333 L 130.1498 9.3311 L 136.9072 11.5267 L 128.7622 36.5942 C 123.3838 35.3118 117.7712 34.6328 112 34.6328 C 106.2749 34.6328 100.7058 35.301 95.3665 36.5637 L 87.2279 11.5155 L 94.0531 9.2978 L 77.476 0 Z"></path></g></g></g>
        <path id="tx-cb-title" transform="matrix(1, -1.6081230200044232e-16, 1.6081230200044232e-16, 1, 72, 24)" fill="#ff00001a" d="M0 0 L600 0 L600 48 L0 48 L0 0 Z"></path>
        <path id="ic-cc-start" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 336, 287.99609375)" fill="#33de7b1a" d="M0 0 L72 0 L72 72 L0 72 L0 0 Z"></path>
        <path id="tx-rb-10" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 120, 96)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-10" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 296.0849609375, 140.2225341796875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-10" fill="#1ac6ff33" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 295.106201171875, 112.2781982421875)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rc-9" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 192)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-9" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 212.085205078125, 201.2520751953125)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-9" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 190.1064453125, 188.565185546875)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rc-8" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 0, 288)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-8" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 179.999755859375, 300)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-8" fill="#1ac6ff33" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 150, 311.9998779296875)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rc-7" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 36, 384)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-7" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 212.085205078125, 398.748046875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-7" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 190.1064453125, 435.4349365234375)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-rt-6" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 120, 480)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-6" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 296.0849609375, 459.777587890625)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-6" fill="#1ac6ff33" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 295.106201171875, 511.7218017578125)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lt-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 468, 480)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-5" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 399.914794921875, 459.777587890625)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-5" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 424.8935546875, 511.721923828125)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lc-4" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 552, 384)" fill="#ff00001a" d="M0 0 L168 0 L168 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-4" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 483.91455078125, 398.748046875)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-4" fill="#1ac6ff33" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 529.893310546875, 435.4349365234375)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lc-3" transform="translate(588, 288)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-3" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 516, 300.0001220703125)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-3" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 569.999755859375, 312.000244140625)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lc-2" transform="translate(552, 192)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-2" transform="matrix(1, -6.695352740981406e-17, 6.695352740981406e-17, 1, 483.91455078125, 201.2520751953125)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-2" fill="#1ac6ff33" transform="matrix(1, -1.6653345369377348e-16, 1.6653345369377348e-16, 1, 529.893310546875, 188.565185546875)" width="24" height="24" rx="0" ry="0"></rect>
        <path id="tx-lb-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 468, 96)" fill="#ff00001a" d="M0 0 L156 0 L156 72 L0 72 L0 0 Z"></path>
        <path id="ic-cc-1" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 399.9150390625, 140.22265625)" fill="#33de7b1a" d="M0 0 L48 0 L48 48 L0 48 L0 0 Z"></path>
        <rect id="bt-cc-remove-1" fill="#1ac6ff33" transform="matrix(1, -5.551115123125783e-17, 5.551115123125783e-17, 1, 424.8935546875, 112.2781982421875)" width="24" height="24" rx="0" ry="0"></rect></g></svg>