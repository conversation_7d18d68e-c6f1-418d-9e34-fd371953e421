<svg xmlns="http://www.w3.org/2000/svg" width="840" height="300">
    <g id="impact-crack-v1--family--3" data-resize="horizontal:HUG;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:24;primary:MIN;counter:CENTER" data-position="x:0;y:0;w:0;h:0">
        <path id="tx-cb-title" fill="#ff00001a" d="M0 0 L840 0 L840 24 L0 24 L0 0 Z" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="w:840;h:24" data-entity-classes="Title">

        </path>
        <g id="content-group" transform="translate(0, 48)" data-resize="horizontal:FIXED;vertical:HUG_ALL_CONTENT" data-position="w:840;h:0">
            <g id="body" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:840;h:252">
                <g id="lines" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:276;y:0;w:291;h:156" transform="translate(276, 0)">
                    <g id="g-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:195.500;y:43.500;w:95;h:38" transform="translate(195.5, 43.5)">
                        <path id="Vector" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 95 38 L 68.5 33.5 C 68.5 33.5 58 28 36.5 29.5 L 21 19.5 L 17.4315 23.9914 M 17.4315 23.9914 C 16.0982 21.8276 13.7329 11.5994 12 9 C 9 4.5 4.5 1.5 0 0 M 17.4315 23.9914 L 0 29.5" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:95;h:38"/>
                    </g>
                    <g id="g-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:4.500;y:57.500;w:87.500;h:16.500" transform="translate(4.5, 57.5)">
                        <path id="Vector_1" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0 16.5 L 33.5 7 C 42.5 7 61 5.4999 63.5 5.5 L 87.5 9.9602e-10" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:87.500;h:16.500"/>
                    </g>
                    <g id="g-1" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:87;y:15.500;w:118.500;h:129.500" transform="translate(87, 15.5)">
                        <path id="Vector_2" fill="none" stroke="#484848" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 4.4999 41.9999 C 4.85 42.0174 5.1829 42.0367 5.4999 42.0579 M 45.4999 61.1466 C 53.3342 66.1535 61.082 71.4871 65.9999 74.9999 L 100.9999 101.923 M 45.4999 61.1466 C 36.6075 55.4634 27.6035 50.2011 22.4999 48.4999 C 10.92 44.6399 14.2395 42.6424 5.4999 42.0579 M 45.4999 61.1466 C 44.8332 64.5977 44.1999 72.7999 46.9999 77.9999 C 50.4999 84.4999 52.4999 87.1153 58.9999 89.6153 M 100.9999 101.923 C 96.4999 106.1153 94.9999 110.6999 82.9999 111.4999 M 100.9999 101.923 L 118.4999 106.5 M 5.4999 42.0579 C 3.8332 44.5386 0.9999 52.1999 2.9999 62.9999 C 4.9999 73.7999 7.1666 80.4999 7.9999 82.4999 M 46.7411 113.9999 L 46.9999 112.9999 M 66.4999 74.9999 L 46.9999 112.9999 M 66.4999 74.9999 L 72.1642 44.4999 M 66.4999 74.9999 L 28.9999 79.9999 C 28.6742 80.0523 28.3407 80.1061 27.9999 80.1612 M 66.4999 74.9999 L 67.9999 74.3749 M 46.9999 112.9999 C 43.0803 115.6665 35.0411 121.8999 34.2411 125.4999 M 72.1642 44.4999 L 74.4999 21.5 L 65.9999 0 M 72.1642 44.4999 L 55.4999 45.4999 L 44.9999 50.9999 M 94.9999 63.1249 L 108.4999 57.4999 C 105.8332 52.4999 97.8999 41.3999 87.4999 36.9999 C 74.4999 31.4999 63.2271 31.937 53.4999 34.9999 C 43.9999 37.9913 34.8332 45.3332 29.9999 49.4999 M 94.9999 63.1249 C 95.8332 65.9165 97.6999 72.1999 98.4999 74.9999 C 99.4999 78.4999 98.9999 82.4999 96.9999 84.9999 C 95.3999 86.9999 93.3332 90.8332 92.4999 92.4999 M 94.9999 63.1249 C 92.9999 59.5832 88.5146 53.4326 83.4999 51.9999 C 79.9999 50.9999 75.9999 50.9999 72.4999 51.9999 M 94.9999 63.1249 L 78.9999 69.7915 M 27.9999 80.1612 C 29.4999 83.7741 33.8999 92.0999 39.4999 96.4999 C 46.4999 101.9999 47.9999 103.4999 51.9999 103.9999 M 27.9999 80.1612 L 0 83.5 M 78.9999 69.7915 C 79.4999 71.8072 80.1999 76.2708 78.9999 77.9999 C 77.7999 79.7289 77.1666 81.7203 76.9999 82.4999 M 78.9999 69.7915 L 67.9999 74.3749 M 67.9999 74.3749 L 69.4999 93.4999 C 69.4999 94.4999 69.8999 97.0999 71.4999 99.4999 C 73.4999 102.4999 78.9999 109.9999 79.4999 112.9999 C 79.8999 115.3999 84.6666 124.9999 86.9999 129.4999" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:118.500;h:129.500"/>
                    </g>
                </g>
                <g id="text-3" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:576;y:48;w:216;h:60" transform="translate(576, 48)">
                    <g id="ic-cc-3" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48" fill="#33de7b1a">
                        <g id="icon" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                            <path id="icon_1" transform="translate(9, 5)" fill="none" stroke="#df5e59" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                        </g>
                    </g>
                    <g id="text-3_1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MIN" data-position="x:60;y:0;w:156;h:60" transform="translate(60, 0)">
                        <rect id="bt-cc-add-4" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, -24)" width="24" height="24" rx="0" ry="0"/>
                        <g id="tx-lt-3" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#df5e59" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                        <g id="tx-lt-3-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_1" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:LEFT;vertical:TOP"/>
                        </g>
                    </g>
                </g>
                <g id="text-2" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-stack="layoutMode:HORIZONTAL;padding:0 0 0 0;gap:12;primary:MAX;counter:MIN" data-position="x:48;y:48;w:216;h:60" transform="translate(48, 48)">
                    <g id="text-2_1" data-resize="horizontal:FILL;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:1;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:MAX" data-position="x:0;y:0;w:156;h:60">
                        <g id="tx-rt-2" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:156;h:24" fill="#ff00001a">
                            <text id="Label_2" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#db8333" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <g id="tx-rt-2-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:156;h:24" fill="#ff00001a" transform="translate(0, 36)">
                            <text id="Label_3" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:156;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:RIGHT;vertical:TOP"/>
                        </g>
                        <rect id="bt-cc-add-2" data-constraints="horizontal:CENTER;vertical:MAX;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:60;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, 60)" width="24" height="24" rx="0" ry="0"/>
                        <rect id="bt-cc-add-3" data-constraints="horizontal:CENTER;vertical:MIN;layoutPositioning:ABSOLUTE;layoutGrow:0;layoutAlign:INHERIT" data-position="x:66;y:-24;w:24;h:24" fill="#1ac6ff33" transform="matrix(0.9999999403953552, 0, 0, 0.9999999403953552, 66, -24)" width="24" height="24" rx="0" ry="0"/>
                    </g>
                    <g id="ic-cc-2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:168;y:0;w:48;h:48" fill="#33de7b1a" transform="translate(168, 0)">
                        <g id="icon_2" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:48;h:48">
                            <path id="icon_3" transform="translate(9, 5)" fill="none" stroke="#db8333" stroke-width="2" stroke-linejoin="miter" stroke-linecap="none" stroke-miterlimit="4" d="M 0.0002 0 C 9.6785 5.6677 19.3334 11.3739 29 17.0609 C 23.4383 20.1435 14.4594 25.0635 7.9647 28.6163 C 3.3573 31.1366 0 32.9689 0 32.9689 L 0.0002 0 Z M 29 17.0609 C 23.0495 23.3236 17.2086 29.6872 11.3133 36 C 10.1996 33.5384 9.1252 31.0571 7.9647 28.6163" data-constraints="horizontal:CENTER;vertical:CENTER;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:9;y:5;w:29;h:36"/>
                        </g>
                    </g>
                </g>
                <g id="text-1" data-resize="horizontal:FIXED;vertical:HUG" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-stack="layoutMode:VERTICAL;padding:0 0 0 0;gap:12;primary:MIN;counter:CENTER" data-position="x:336;y:168;w:192;h:60" transform="translate(336, 168)">
                    <g id="tx-ct-1" data-entity-classes="DescTitle" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:0;w:192;h:24" fill="#ff00001a">
                        <text id="Label_4" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="20" font-family="Roboto" font-weight="700" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                    <g id="tx-ct-1-desc" data-entity-classes="Description" data-resize="horizontal:FIXED;vertical:FIXED" data-constraints="horizontal:MIN;vertical:MIN;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:STRETCH" data-position="x:0;y:36;w:192;h:24" fill="#ff00001a" transform="translate(0, 36)">
                        <text id="Label_5" data-constraints="horizontal:STRETCH;vertical:STRETCH;layoutPositioning:AUTO;layoutGrow:0;layoutAlign:INHERIT" data-position="x:0;y:0;w:192;h:24" fill="#484848" font-size="15" font-family="Roboto" font-weight="400" alignment-baseline="hanging" data-align="horizontal:CENTER;vertical:TOP"/>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>